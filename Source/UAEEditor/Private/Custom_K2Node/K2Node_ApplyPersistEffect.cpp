// Copyright 1998-2017 Epic Games, Inc. All Rights Reserved.

#include "K2Node_ApplyPersistEffect.h"
#include "UObject/UnrealType.h"
#include "Engine/EngineTypes.h"
#include "GameFramework/Actor.h"
#include "Kismet/GameplayStatics.h"
#include "EdGraph/EdGraph.h"
#include "EdGraphSchema_K2.h"
#include "K2Node_CallFunction.h"
#include "K2Node_Select.h"
#include "Kismet2/BlueprintEditorUtils.h"
#include "KismetCompilerMisc.h"
#include "KismetCompiler.h"
#include "BlueprintNodeSpawner.h"
#include "EditorCategoryUtils.h"
#include "BlueprintActionDatabaseRegistrar.h"
#include "Character/PersistEffect.h"
#include "Character/PersistBaseComponent.h"


struct FK2Node_ApplyPersistEffectFromClassHelper
{
	static FString OverrideApplyTimePinName;
	static FString OwnerPinName;
};

FString FK2Node_ApplyPersistEffectFromClassHelper::OverrideApplyTimePinName(TEXT("OverrideApplyTime"));
FString FK2Node_ApplyPersistEffectFromClassHelper::OwnerPinName(TEXT("PersistComponent"));

#define LOCTEXT_NAMESPACE "K2Node_ApplyPersistEffectFromClass"

UK2Node_ApplyPersistEffectFromClass::UK2Node_ApplyPersistEffectFromClass(const FObjectInitializer& ObjectInitializer)
	: Super(ObjectInitializer)
{
	NodeTooltip = LOCTEXT("NodeTooltip", "Attempts to Apply a new PersistEffect to PersistComponent with the specified class");
}

UClass* UK2Node_ApplyPersistEffectFromClass::GetClassPinBaseClass() const
{
	return UPersistEffectBase::StaticClass();
}

void UK2Node_ApplyPersistEffectFromClass::AllocateDefaultPins()
{
	Super::AllocateDefaultPins();
#if UE5_OR_LATER
	UEdGraphPin* OwnerPin = CreatePin(EGPD_Input, UEdGraphSchema_K2::PC_Object.ToString(), FString(), UPersistBaseComponent::StaticClass(), FK2Node_ApplyPersistEffectFromClassHelper::OwnerPinName);
	UEdGraphPin* OverrideApplyTimePin = CreatePin(EGPD_Input, UEdGraphSchema_K2::PC_Real.ToString(), UEdGraphSchema_K2::PC_Float.ToString(), nullptr, FK2Node_ApplyPersistEffectFromClassHelper::OverrideApplyTimePinName);
#else
	UEdGraphPin* OwnerPin = CreatePin(EGPD_Input, UEdGraphSchema_K2::PC_Object, FString(), UPersistBaseComponent::StaticClass(), FK2Node_ApplyPersistEffectFromClassHelper::OwnerPinName);
	UEdGraphPin* OverrideApplyTimePin = CreatePin(EGPD_Input, UEdGraphSchema_K2::PC_Float, FString(), nullptr, FK2Node_ApplyPersistEffectFromClassHelper::OverrideApplyTimePinName);
#endif
	OverrideApplyTimePin->DefaultValue = TEXT("-1");
}

void UK2Node_ApplyPersistEffectFromClass::ReallocatePinsDuringReconstruction(TArray<UEdGraphPin*>& OldPins)
{
	Super::ReallocatePinsDuringReconstruction(OldPins);
}

bool UK2Node_ApplyPersistEffectFromClass::IsSpawnVarPin(UEdGraphPin* Pin) const
{
	return(Super::IsSpawnVarPin(Pin) && Pin &&
		Pin->PinName != FK2Node_ApplyPersistEffectFromClassHelper::OwnerPinName &&
		Pin->PinName != FK2Node_ApplyPersistEffectFromClassHelper::OverrideApplyTimePinName);
}

void UK2Node_ApplyPersistEffectFromClass::GetPinHoverText(const UEdGraphPin& Pin, FString& HoverTextOut) const
{
	const UEdGraphSchema_K2* K2Schema = GetDefault<UEdGraphSchema_K2>();

	if (UEdGraphPin* OwnerPin = GetOwnerPin())
	{
		K2Schema->ConstructBasicPinTooltip(*OwnerPin, LOCTEXT("OwnerPinDescription", "Can not be left empty; otherwise this call will fail."), OwnerPin->PinToolTip);
	}
	else if (UEdGraphPin* OverrideApplyTimePin = GetOverrideApplyTimePin())
	{
		K2Schema->ConstructBasicPinTooltip(*OverrideApplyTimePin, LOCTEXT("OverrideApplyTimePinDescription", "this param will override the effect time"), OverrideApplyTimePin->PinToolTip);
	}

	return Super::GetPinHoverText(Pin, HoverTextOut);
}

FSlateIcon UK2Node_ApplyPersistEffectFromClass::GetIconAndTint(FLinearColor& OutColor) const
{
	static FSlateIcon Icon("EditorStyle", "GraphEditor.SpawnActor_16x");
	return Icon;
}

UEdGraphPin* UK2Node_ApplyPersistEffectFromClass::GetOwnerPin() const
{
	UEdGraphPin* Pin = FindPin(FK2Node_ApplyPersistEffectFromClassHelper::OwnerPinName);
	check(Pin == nullptr || Pin->Direction == EGPD_Input);
	return Pin;
}

UEdGraphPin* UK2Node_ApplyPersistEffectFromClass::GetOverrideApplyTimePin() const
{
	UEdGraphPin* Pin = FindPin(FK2Node_ApplyPersistEffectFromClassHelper::OverrideApplyTimePinName);
	check(Pin == nullptr || Pin->Direction == EGPD_Input);
	return Pin;
}

FText UK2Node_ApplyPersistEffectFromClass::GetNodeTitle(ENodeTitleType::Type TitleType) const
{
	FText NodeTitle = NSLOCTEXT("K2Node", "ApplyPersistEffect_BaseTitle", "Apply PersistEffect from Class");
	if (TitleType != ENodeTitleType::MenuTitle)
	{
		if (UEdGraphPin* ClassPin = GetClassPin())
		{
			if (ClassPin->LinkedTo.Num() > 0)
			{
				// Blueprint will be determined dynamically, so we don't have the name in this case
				NodeTitle = NSLOCTEXT("K2Node", "ApplyPersistEffect_Title_Unknown", "ApplyPersistEffect");
			}
			else if (ClassPin->DefaultObject == nullptr)
			{
				NodeTitle = NSLOCTEXT("K2Node", "ApplyPersistEffect_Title_NONE", "ApplyPersistEffect NONE");
			}
			else
			{
				if (CachedNodeTitle.IsOutOfDate(this))
				{
					FText ClassName;
					if (UClass* PickedClass = Cast<UClass>(ClassPin->DefaultObject))
					{
						ClassName = PickedClass->GetDisplayNameText();
					}

					FFormatNamedArguments Args;
					Args.Add(TEXT("ClassName"), ClassName);

					// FText::Format() is slow, so we cache this to save on performance
					CachedNodeTitle.SetCachedText(FText::Format(NSLOCTEXT("K2Node", "ApplyPersistEffect_Title_Class", "ApplyPersistEffect {ClassName}"), Args), this);
				}
				NodeTitle = CachedNodeTitle;
			}
		}
		else
		{
			NodeTitle = NSLOCTEXT("K2Node", "ApplyPersistEffect_Title_NONE", "ApplyPersistEffect NONE");
		}
	}
	return NodeTitle;
}

bool UK2Node_ApplyPersistEffectFromClass::IsCompatibleWithGraph(const UEdGraph* TargetGraph) const
{
	UBlueprint* Blueprint = FBlueprintEditorUtils::FindBlueprintForGraph(TargetGraph);
	return Super::IsCompatibleWithGraph(TargetGraph) && (!Blueprint || (FBlueprintEditorUtils::FindUserConstructionScript(Blueprint) != TargetGraph && Blueprint->GeneratedClass->GetDefaultObject()->ImplementsGetWorld()));
}

void UK2Node_ApplyPersistEffectFromClass::GetNodeAttributes(TArray<TKeyValuePair<FString, FString>>& OutNodeAttributes) const
{
	UClass* ClassToSpawn = GetClassToSpawn();
	const FString ClassToSpawnStr = ClassToSpawn ? ClassToSpawn->GetName() : TEXT("InvalidClass");
	OutNodeAttributes.Add(TKeyValuePair<FString, FString>(TEXT("Type"), TEXT("ApplyPersistEffectFromClass")));
	OutNodeAttributes.Add(TKeyValuePair<FString, FString>(TEXT("Class"), GetClass()->GetName()));
	OutNodeAttributes.Add(TKeyValuePair<FString, FString>(TEXT("Name"), GetName()));
	OutNodeAttributes.Add(TKeyValuePair<FString, FString>(TEXT("EffectClass"), ClassToSpawnStr));
}

FNodeHandlingFunctor* UK2Node_ApplyPersistEffectFromClass::CreateNodeHandler(FKismetCompilerContext& CompilerContext) const
{
	return new FNodeHandlingFunctor(CompilerContext);
}

void UK2Node_ApplyPersistEffectFromClass::ExpandNode(class FKismetCompilerContext& CompilerContext, UEdGraph* SourceGraph)
{
	Super::ExpandNode(CompilerContext, SourceGraph);

	UEdGraphPin* ApplyClassPin = GetClassPin();
	// Cache the class to spawn. Note, this is the compile time class that the pin was set to or the variable type it was connected to. Runtime it could be a child.
	UClass* SpawnClass = (ApplyClassPin != NULL) ? Cast<UClass>(ApplyClassPin->DefaultObject) : NULL;

	// @SStudio murphyxu - BEGIN: 原生写的有问题。应该使用GetClassToSpawn来获取class。 解决ue5中表演不正常的bug。 ue4中也是无效的class，但是后面流程和ue5不一样，没有ue5中的严格检查功能，所以功能正常。
#if UE5_OR_LATER
	if (SpawnClass == nullptr)
	{
		SpawnClass = GetClassToSpawn();
	}
#endif
	// @SStudio murphyxu - END

	if (!ApplyClassPin || ((0 == ApplyClassPin->LinkedTo.Num()) && (NULL == SpawnClass)))
	{
		CompilerContext.MessageLog.Error(*LOCTEXT("ApplyPersistEffectNodeMissingClass_Error", "Spawn node @@ must have a @@ specified.").ToString(), this, ApplyClassPin);
		// we break exec links so this is the only error we get, don't want the SpawnActor node being considered and giving 'unexpected node' type warnings
		BreakAllNodeLinks();
		return;
	}

	bool bSucceeded = true;
	static const FName CreatePersistEffectFuncName = GET_FUNCTION_NAME_CHECKED(UPersistBaseComponent, CreatePersistEffect);
	static const FName ApplyPersistEffectFuncName = GET_FUNCTION_NAME_CHECKED(UPersistBaseComponent, ApplyPersistEffect);

	UK2Node_CallFunction* CallCreateNode = CompilerContext.SpawnIntermediateNode<UK2Node_CallFunction>(this, SourceGraph);
	check(CallCreateNode);
	CallCreateNode->FunctionReference.SetExternalMember(CreatePersistEffectFuncName, UPersistBaseComponent::StaticClass());
	CallCreateNode->AllocateDefaultPins();

	UK2Node_CallFunction* CallApplyNode = CompilerContext.SpawnIntermediateNode<UK2Node_CallFunction>(this, SourceGraph);
	check(CallApplyNode);
	CallApplyNode->FunctionReference.SetExternalMember(ApplyPersistEffectFuncName, UPersistBaseComponent::StaticClass());
	CallApplyNode->AllocateDefaultPins();

	//connect exec
	UEdGraphPin* SelfExecPin = GetExecPin();
	UEdGraphPin* CallCreateExecPin = CallCreateNode->GetExecPin();
	bSucceeded &= SelfExecPin && CallCreateExecPin && CompilerContext.MovePinLinksToIntermediate(*SelfExecPin, *CallCreateExecPin).CanSafeConnect();

	//connect class
	UEdGraphPin* CallClassPin = CallCreateNode->FindPin(TEXT("EffectClass"));
	if (CallClassPin)
	{
		if (ApplyClassPin && ApplyClassPin->LinkedTo.Num() > 0)
		{
			bSucceeded &= CompilerContext.MovePinLinksToIntermediate(*ApplyClassPin, *CallClassPin).CanSafeConnect();
		}
		else
		{
			// Copy blueprint literal onto begin spawn call 
			CallClassPin->DefaultObject = SpawnClass;
		}
	}

	//connect Owner
	UEdGraphPin* ApplyOwnerPin = GetOwnerPin();
	UEdGraphPin* CallCreatePersistComponentPin = CallCreateNode->FindPin(TEXT("PersistComponent"));
	bSucceeded &= ApplyOwnerPin && CallCreatePersistComponentPin && CompilerContext.MovePinLinksToIntermediate(*ApplyOwnerPin, *CallCreatePersistComponentPin).CanSafeConnect();
	UEdGraphPin* CallApplyPersistComponentPin = CallApplyNode->FindPin(TEXT("PersistComponent"));
	if (CallCreatePersistComponentPin && CallApplyPersistComponentPin && CallCreatePersistComponentPin->LinkedTo.IsValidIndex(0))
	{
		CallApplyPersistComponentPin->MakeLinkTo(CallCreatePersistComponentPin->LinkedTo[0]);
	}

	//connect overtime 
	UEdGraphPin* CallTimePin = CallApplyNode->FindPin(TEXT("OverrideApplyTime"));
	UEdGraphPin* ApplyTimePin = GetOverrideApplyTimePin();
	if (CallTimePin && ApplyTimePin)
	{
		if (ApplyTimePin->LinkedTo.Num() > 0)
		{
			bSucceeded &= CompilerContext.MovePinLinksToIntermediate(*ApplyTimePin, *CallTimePin).CanSafeConnect();
		}
		else
		{
			CallTimePin->DefaultValue = ApplyTimePin->DefaultValue;
		}
	}

	//create 'set var' nodes
	UEdGraphPin* CallCreateResultPin = CallCreateNode->GetReturnValuePin();
	UEdGraphPin* LastThenPin = FKismetCompilerUtilities::GenerateAssignmentNodes(CompilerContext, SourceGraph, CallCreateNode, this, CallCreateResultPin, SpawnClass);

	//connect created data
	UEdGraphPin* CallDataModelPin = CallApplyNode->FindPin(TEXT("DataModel"));
	if (CallDataModelPin)
	{
		CallDataModelPin->MakeLinkTo(CallCreateResultPin);
	}

	//make link exec
	UEdGraphPin* CallApplyExecPin = CallApplyNode->GetExecPin();
	if (LastThenPin)
	{
		LastThenPin->MakeLinkTo(CallApplyExecPin);
		LastThenPin = CallApplyNode->GetThenPin();
	}

	//connect result
	UEdGraphPin* ApplyResultPin = GetResultPin();
	UEdGraphPin* CallApplyResultPin = CallApplyNode->GetReturnValuePin();
	// cast HACK. It should be safe. The only problem is native code generation.
	if (ApplyResultPin && CallApplyResultPin)
	{
		CallApplyResultPin->PinType = ApplyResultPin->PinType;
	}
	bSucceeded &= ApplyResultPin && CallApplyResultPin && CompilerContext.MovePinLinksToIntermediate(*ApplyResultPin, *CallApplyResultPin).CanSafeConnect();

	UEdGraphPin* SelfThenPin = GetThenPin();
	bSucceeded &= SelfThenPin && LastThenPin && CompilerContext.MovePinLinksToIntermediate(*SelfThenPin, *LastThenPin).CanSafeConnect();

	// Break any links to the expanded node
	BreakAllNodeLinks();

	if (!bSucceeded)
	{
		CompilerContext.MessageLog.Error(*LOCTEXT("ApplyPersistEffect_Error", "ICE: ApplyPersistEffectFromClass error @@").ToString(), this);
	}
}

bool UK2Node_ApplyPersistEffectFromClass::HasExternalDependencies(TArray<class UStruct*>* OptionalOutput) const
{
	UClass* SourceClass = GetClassToSpawn();
	const UBlueprint* SourceBlueprint = GetBlueprint();
	const bool bResult = (SourceClass != NULL) && (SourceClass->ClassGeneratedBy != SourceBlueprint);
	if (bResult && OptionalOutput)
	{
		OptionalOutput->AddUnique(SourceClass);
	}
	const bool bSuperResult = Super::HasExternalDependencies(OptionalOutput);
	return bSuperResult || bResult;
}

#undef LOCTEXT_NAMESPACE
