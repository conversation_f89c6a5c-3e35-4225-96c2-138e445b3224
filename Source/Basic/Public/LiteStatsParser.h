// Copyright 1998-2019 Epic Games, Inc. All Rights Reserved.

#pragma once

#include "CoreMinimal.h"
#include "LiteStatsDataStream.h"
#include "Misc/OutputDevice.h"

DECLARE_LOG_CATEGORY_EXTERN(LogLiteStatsParser, Log, All);

class FLiteStatsDataStream;
class FLiteStatsParser;

extern FLiteStatsParser GLocalLiteStatsParser;
extern FLiteStatsParser GRemoteLiteStatsParser;

class FLiteStatsParser : public FLiteStatsDataProducer
{
	
public:
	
	FLiteStatsParser();

	virtual ~FLiteStatsParser() override;

	void Parse(const TCHAR* V);

	void Empty();

private:

	void ParseCompressed(const TCHAR* V);

	void ParseUncompressed(const TCHAR* V);

	/** @return where this function stopped, NULL if failed */
	const TCHAR* ParseSection(const TCHAR* V);

	/** @return where this function stopped, NULL if failed */
	const TCHAR* ParseStatFieldNames(const TCHAR* V);

	/** @return where this function stopped, NULL if failed */
	const TCHAR* ParseStat(const TCHAR* V);

	/** @return where this function stopped, NULL if failed */
	const TCHAR* ParseHList(const TCHAR* V);

	/** Copied from appUncompressMemoryZLIB, don't check UncompressedSize, just return it */
	bool DecompressMemoryZLIB(void* UncompressedBuffer, int32& UncompressedSize, const void* CompressedBuffer, int32 CompressedSize, int32 BitWindow = DEFAULT_ZLIB_BIT_WINDOW);

private:

	TMap<FName, TArray<FName>> CategoryToFieldNames;
	
};
