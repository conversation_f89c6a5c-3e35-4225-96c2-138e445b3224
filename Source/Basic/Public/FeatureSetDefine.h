// Copyright 1998-2014 Epic Games, Inc. All Rights Reserved.

#pragma once
#include "CoreMinimal.h"
#include "FeatureSetDefine.generated.h"

UENUM(Blueprintable)
enum class EFeatureSetType : uint8
{
	None,
	DedicateServer,
	Autonomous,
	Simulate_Team,
	Simulate_NonTeam,
	Max,
};

namespace Lex
{
	BASIC_API FString ToString(EFeatureSetType Type);
}

UCLASS()
class BASIC_API UFeatureSetDefine : public UObject
{
	GENERATED_BODY()
public:
	static const FName FeatureActor_PlayerPawn;
	static const FName FeatureActor_Zombie;
};
