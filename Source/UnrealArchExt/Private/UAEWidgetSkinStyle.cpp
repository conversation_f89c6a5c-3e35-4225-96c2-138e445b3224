// Copyright 1998-2017 Epic Games, Inc. All Rights Reserved.

#include "UAEWidgetSkinStyle.h"
#include "UObject/UObjectHash.h"
#include "Kismet/KismetStringLibrary.h"

UNREALARCHEXT_API int32 GPCSkinForceUnrevertable = 1;
static FAutoConsoleVariableRef CVarPCSkinForceUnrevertable(
	TEXT("skin.PCSkinForceUnrevertable"),
	GPCSkinForceUnrevertable,
	TEXT(""),
	ECVF_Default);

uint32 GetTypeHash(const FSkinParentKey& SkinParentKey)
{
	return (uint32)(GetTypeHash(SkinParentKey.ParantName) + GetTypeHash(SkinParentKey.ParantBPPath) + SkinParentKey.ChildSkinMatchingKey) % 4093082899;/* a random prime number less than MAX_uint32 from https://primes.utm.edu/lists/small/small.html */
}

UUAEWidgetSkinStyle::UUAEWidgetSkinStyle(const FObjectInitializer& ObjectInitializer)
	: Super(ObjectInitializer)
{
	
}

UUAEWidgetSkinStyle::~UUAEWidgetSkinStyle()
{
	UE_LOG(LogTemp, Log, TEXT("UUAEWidgetSkinStyle InitPCReleasePure"));
}

void UUAEWidgetSkinStyle::ApplySkin(UObject* Context, UUAEWidgetSkinStyle* _ModeStyle)
{
	UE_LOG(LogTemp, Log, TEXT("UUAEWidgetSkinStyle ApplySkin"));
#if WITH_EDITOR
	StyleContext = Context;
#endif
	ModeStyle = _ModeStyle;
	TakeSkin(true);
}

void UUAEWidgetSkinStyle::DisapplySkin()
{
	UE_LOG(LogTemp, Log, TEXT("UUAEWidgetSkinStyle DisapplySkin"));
}

bool UUAEWidgetSkinStyle::IsValidStyleByMode(const TSoftClassPtr<UUAEUserWidget>& UUserWidgetClass)
{
	if (ModeStyle.IsValid() && !UUserWidgetClass.IsNull())
	{
		for (const auto& ModeInfoPair : ModeStyle->SkinInfoMap)
		{
			const TSoftClassPtr<UUAEUserWidget>& ModeUUserWidgetClass = ModeInfoPair.Key;
			if (!ModeUUserWidgetClass.IsNull())
			{
				if (ModeUUserWidgetClass.ToSoftObjectPath() == UUserWidgetClass.ToSoftObjectPath())
				{
					return false;
				}
			}
		}
	}
	return true;
}

void UUAEWidgetSkinStyle::OnUAEUserWdigetNativeConstruct(UUAEUserWidget* UAEUserWidget)
{
	if (UAEUserWidget)
	{
		UClass* WidgetClass = UAEUserWidget->GetClass();
		if (WidgetClass)
		{
			TSoftClassPtr<UUAEUserWidget> SoftClass(WidgetClass);
			if (!IsValidStyleByMode(SoftClass))
			{
				UE_LOG(LogTemp, Log, TEXT("UUserSkinStyle::OnUAEUserWdigetNativeConstruct IsNotValidStyleByMode[%s]"), *SoftClass.GetAssetName());
				return;
			}

			const FSkinParentInfo* pSkinParentInfo = SkinInfoMap.Find(SoftClass);
			if (pSkinParentInfo)
			{
				const TSoftClassPtr<UUserWidgetSkin>* WidgetSkinClass = GetWidgetSkinClassBySkinCondition(*pSkinParentInfo, UAEUserWidget);
				if (WidgetSkinClass)
				{
					if (GPCSkinForceUnrevertable > 0)
					{
						UAEUserWidget->ApplyUnRevertableSkinData(*WidgetSkinClass);
					}
					else
					{
						UAEUserWidget->ApplySkinDataByPath(*WidgetSkinClass);
					}
					UE_LOG(LogTemp, Log, TEXT("UUAEWidgetSkinStyle OnUAEUserWdigetNativeConstruct ApplySkin[%s]"), *UAEUserWidget->GetName());
				}
				else
				{
					UE_LOG(LogTemp, Warning, TEXT("UUAEWidgetSkinStyle OnUAEUserWdigetNativeConstruct No Skin[%s]"), *UAEUserWidget->GetName());
				}
			}
		}
	}
}

const TSoftClassPtr<UUserWidgetSkin>* UUAEWidgetSkinStyle::GetWidgetSkinClassBySkinCondition(const FSkinParentInfo& SkinParentInfo, UUAEUserWidget* UAEUserWidget)
{
	static FSkinParentKey DefaultKey;
	DefaultKey.ParantName = TEXT("");
	const TSoftClassPtr<UUserWidgetSkin>* pDefaultSkin = SkinParentInfo.SkinByCondition.Find(DefaultKey);

	for (const auto& ConditionPair : SkinParentInfo.SkinByCondition)
	{
		const FSkinParentKey& SkinParentKey = ConditionPair.Key;

		if (&(ConditionPair.Value) != pDefaultSkin)
		{
			// 条件1:父节点名对应皮
			bool bParantName = IsParantName(SkinParentKey.ParantName, UAEUserWidget);
			if (bParantName)
			{
				// 条件2:父蓝图ChildSkinMatchingKey值
				bool bChildSkinMatchingKey = IsChildSkinMatchingKey(SkinParentKey.ChildSkinMatchingKey, UAEUserWidget);
				if (bChildSkinMatchingKey)
				{
					// 条件3:父蓝图类型路径
					bool bParantBPPath = IsParantBPPath(SkinParentKey.ParantBPPath, UAEUserWidget);
					if (bParantBPPath)
					{
						return &ConditionPair.Value;
					}
				}
			}
		}
	}
	return pDefaultSkin;
}

bool UUAEWidgetSkinStyle::IsParantName(const FString& ParantName, UUserWidget* UserWidget)
{
	if (ParantName.IsEmpty())
	{
		return true;
	}
	if (UserWidget)
	{
		UPanelWidget* ParentWidget = UserWidget->GetParent();
		if (ParentWidget && ParentWidget->GetName() == ParantName)
		{
			return true;
		}
	}
	return false;
}

bool UUAEWidgetSkinStyle::IsChildSkinMatchingKey(int32 ChildSkinMatchingKey, UUAEUserWidget* UAEUserWidget)
{
	if (ChildSkinMatchingKey == 0)
	{
		return true;
	}
	if (UAEUserWidget)
	{
		UWidgetTree* WidgetTree = Cast<UWidgetTree>(UAEUserWidget->GetOuter());
		if (WidgetTree)
		{
			UUAEUserWidget* ParentBP = Cast<UUAEUserWidget>(WidgetTree->GetOuter());
			if (ParentBP && ParentBP->ChildSkinMatchingKey == ChildSkinMatchingKey)
			{
				return true;
			}
		}
	}
	return false;
}

static int32 SkinStyleConditionWidgetParentLayer = 10;
static FAutoConsoleVariableRef CVarSkinStyleConditionWidgetParentLayer(
	TEXT("g.SkinStyleConditionWidgetParentLayer"),
	SkinStyleConditionWidgetParentLayer,
	TEXT("SkinStyleConditionWidgetParentLayer"),
	ECVF_Default
);
UUserWidget* UUAEWidgetSkinStyle::GetUserWidgetParent(UUserWidget* UserWidget)
{
	if (UserWidget)
	{
		UWidgetTree* WidgetTree = Cast<UWidgetTree>(UserWidget->GetOuter());
		if (!WidgetTree)
		{
			UPanelWidget* Panel = UserWidget->GetParent();
			for (int32 i = 0; i < SkinStyleConditionWidgetParentLayer && !WidgetTree && Panel; ++i)
			{
				WidgetTree = Cast<UWidgetTree>(Panel->GetOuter());
				Panel = Panel->GetParent();
			}
		}

		if (WidgetTree)
		{
			UUserWidget* ParentBP = Cast<UUserWidget>(WidgetTree->GetOuter());
			return ParentBP;
		}
	}
	return nullptr;
}

bool UUAEWidgetSkinStyle::IsParantBPPath(const FString& ParantBPPath, UUserWidget* UserWidget)
{
	if (ParantBPPath.IsEmpty())
	{
		return true;
	}
	TArray<FString> ParantBPNameList = UKismetStringLibrary::ParseIntoArray(ParantBPPath, TEXT("."), true);
	for (int32 i = ParantBPNameList.Num() - 1; i >= 0; --i)
	{
		UUserWidget* ParentBP = GetUserWidgetParent(UserWidget);
		if (!ParentBP)
		{
			return false;
		}
		UClass* ParentClass = ParentBP->GetClass();
		if (!ParentClass)
		{
			return false;
		}
		if (ParentClass->GetName() != ParantBPNameList[i] && ParentClass->GetName() != (ParantBPNameList[i] + TEXT("_C")))
		{
			return false;
		}
		
		UserWidget = ParentBP;
	}
	return true;
}

void UUAEWidgetSkinStyle::TakeSkin(bool bApply)
{
	UE_LOG(LogTemp, Log, TEXT("UUAEWidgetSkinStyle TakeSkin[%d]"), (int32)bApply);
	for (const auto& InfoPair : SkinInfoMap)
	{
		const TSoftClassPtr<UUAEUserWidget>& UUserWidgetClass = InfoPair.Key;
		const FSkinParentInfo& SkinParentInfo = InfoPair.Value;
		// const TSoftClassPtr<UUserWidgetSkin>& WidgetSkinClass = InfoPair.Value;

		UClass* Class = UUserWidgetClass.Get();
		if (!Class)
		{
			continue;
		}

		TArray<UObject*> OutRet;
		GetObjectsOfClass(Class, OutRet, false);

		for (auto& Var : OutRet)
		{
			UUAEUserWidget* UAE = Cast<UUAEUserWidget>(Var);
			if (!UAE || !UAE->WidgetTree)
			{
				continue;
			}
#if WITH_EDITOR
			if (UAE->IsDesignTime())
			{
				continue;
			}

			if (!StyleContext.IsValid())
			{
				continue;
			}

			if (UAE->GetWorld() != StyleContext->GetWorld())
			{
				continue;
			}
#endif

#if WIN_RELEASE || WITH_EDITOR
			const TSoftClassPtr<UUserWidgetSkin>* WidgetSkinClass = GetWidgetSkinClassBySkinCondition(SkinParentInfo, UAE);
			if (WidgetSkinClass)
			{
				if (bApply)
				{
					// 1、加载PC皮
					if (GPCSkinForceUnrevertable > 0)
					{
						UAE->ApplyUnRevertableSkinData(*WidgetSkinClass);
					}
					else
					{
						UAE->ApplySkinDataByPath(*WidgetSkinClass);
					}
				}
				else
				{
					// 3、卸载PC皮
					UAE->RevertSkinDataByPath(*WidgetSkinClass);
				}
			}
			else
			{
				// ui有，但是找不到对应的皮
				UE_LOG(LogTemp, Warning, TEXT("UUAEWidgetSkinStyle TakeSkin No Skin[%s]"), *UAE->GetName());
			}
#endif
		}
	}
}

#if WITH_EDITOR
// 编辑器支持从指定的旧的皮Style转成新的皮Style
void UUAEWidgetSkinStyle::AddTransforSkin(TSoftClassPtr<UUAEUserWidget> Widget, TSoftClassPtr<UUserWidgetSkin> Skin)
{
	// 以最后一个为准
	if (SkinInfoMap.Find(Widget))
	{
		UE_LOG(LogTemp, Log, TEXT("AddTransforSkin[%s] Existed!"), *Widget.GetAssetName());
	}
	// 都转成默认皮
	FSkinParentInfo SkinParentInfo;
	const FSkinParentKey DefaultKey;
	SkinParentInfo.SkinByCondition.FindOrAdd(DefaultKey) = Skin;
	SkinInfoMap.FindOrAdd(Widget) = SkinParentInfo;
}

void UUAEWidgetSkinStyle::OnCheckBeforeSave()
{
	TSoftClassPtr<UUAEUserWidget> NoneWidget;
	if (SkinInfoMap.Find(NoneWidget))
	{
		FPlatformMisc::MessageBoxExt(EAppMsgType::Ok, TEXT("请检查：存在没有配置的UI"), TEXT("提示"));
	}
	// 检查皮是否能对上UI
	for (auto& InfoPair : SkinInfoMap)
	{
		TSoftClassPtr<UUAEUserWidget>& UUserWidgetClass = InfoPair.Key;
		const FSkinParentInfo& SkinParentInfo = InfoPair.Value;
		if (!UUserWidgetClass.IsNull())
		{
			for (const auto& ConditionPair : SkinParentInfo.SkinByCondition)
			{
				const TSoftClassPtr<UUserWidgetSkin>& WidgetSkinClass = ConditionPair.Value;
				UClass* SkinClass = WidgetSkinClass.LoadSynchronous();
				if (SkinClass)
				{
					UUserWidgetSkin* SkinCDO = Cast<UUserWidgetSkin>(SkinClass->GetDefaultObject());
					if (SkinCDO && SkinCDO->WidgetSkin)
					{
						TSoftClassPtr<UUAEUserWidget> BasicUserWidget = SkinCDO->WidgetSkin->BasicUserWidget.Get();
						if (UUserWidgetClass != BasicUserWidget)
						{
							FPlatformMisc::MessageBoxExt(EAppMsgType::Ok, *(UUserWidgetClass.GetAssetName()), TEXT("UI和皮对不上"));
						}
					}
				}
			}
		}
	}
}

// 编辑器配皮之后可以通过皮选择ui
void UUAEWidgetSkinStyle::FindUIBySkin()
{
	for (auto& InfoPair : SkinInfoMap)
	{
		TSoftClassPtr<UUAEUserWidget>& UUserWidgetClass = InfoPair.Key;
		const FSkinParentInfo& SkinParentInfo = InfoPair.Value;
		//TSoftClassPtr<UUserWidgetSkin>& WidgetSkinClass = InfoPair.Value;

		if (UUserWidgetClass.IsNull())
		{
			for (const auto& ConditionPair : SkinParentInfo.SkinByCondition)
			{
				const TSoftClassPtr<UUserWidgetSkin>& WidgetSkinClass = ConditionPair.Value;
				UClass* SkinClass = WidgetSkinClass.LoadSynchronous();
				if (SkinClass)
				{
					UUserWidgetSkin* SkinCDO = Cast<UUserWidgetSkin>(SkinClass->GetDefaultObject());
					if (SkinCDO && SkinCDO->WidgetSkin)
					{
						TSoftClassPtr<UUAEUserWidget> BasicUserWidget = SkinCDO->WidgetSkin->BasicUserWidget.Get();
						if (SkinInfoMap.Find(BasicUserWidget))
						{
							FPlatformMisc::MessageBoxExt(EAppMsgType::Ok, *(BasicUserWidget.GetAssetName()), TEXT("这个已经存在"));
						}
						else
						{
							UUserWidgetClass = SkinCDO->WidgetSkin->BasicUserWidget; 
						}
					}
				}
			}
		}
	}
}
#endif




//////////////////////////////////////////////////////////////////////////
// 旧的PC皮的配置
//////////////////////////////////////////////////////////////////////////
void UUserSkinStyle::TakeSkin(bool bApply)
{
	UE_LOG(LogTemp, Log, TEXT("UUserSkinStyle TakeSkin[%d]"), (int32)bApply);
	for (const FUserSkinInfo& Info : SkinInfoList)
	{
		UClass* Class = Info.WidgetClass.Get();
		if (!Class)
		{
			continue;
		}

		if (!IsValidStyleByMode(Info))
		{
			if (!Info.Skin.IsNull())
			{
				UE_LOG(LogTemp, Log, TEXT("UUserSkinStyle::TakeSkin IsValidStyleByMode[%s]"), *Info.Skin.GetAssetName());
			}
			continue;
		}

		TArray<UObject*> OutRet;
		GetObjectsOfClass(Class, OutRet, false);

		for (auto& Var : OutRet)
		{
			UUAEUserWidget* UAE = Cast<UUAEUserWidget>(Var);
			if (!UAE || !UAE->WidgetTree)
			{
				continue;
			}
#if WITH_EDITOR
			if (UAE->IsDesignTime())
			{
				continue;
			}

			if (!StyleContext.IsValid())
			{
				continue;
			}

			if (UAE->GetWorld() != StyleContext->GetWorld())
			{
				continue;
			}
#endif

#if WIN_RELEASE || WITH_EDITOR
			if (bApply)
			{
				// 1、加载PC皮
				if (GPCSkinForceUnrevertable > 0)
				{
					UAE->ApplyUnRevertableSkinData(Info.Skin);
				}
				else
				{
					UAE->ApplySkinDataByPath(Info.Skin);
				}
			}
			else
			{
				// 3、卸载PC皮
				UAE->RevertSkinDataByPath(Info.Skin);
			}
#endif
		}
	}
}

UUserSkinStyle::~UUserSkinStyle()
{
	UE_LOG(LogTemp, Log, TEXT("UUserSkinStyle::~UUserSkinStyle"));
}

bool UUserSkinStyle::IsValidStyleByMode(const FUserSkinInfo& Info)
{
	if (ModeStyle && !Info.WidgetClass.IsNull())
	{
		for (const FUserSkinInfo& ModeInfo : ModeStyle->SkinInfoList)
		{
			if (!ModeInfo.WidgetClass.IsNull())
			{
				if (ModeInfo.WidgetClass.ToSoftObjectPath() == Info.WidgetClass.ToSoftObjectPath())
				{
					return false;
				}
			}
		}
	}
	return true;
}

void UUserSkinStyle::ApplySkin(UObject* Context, UUserSkinStyle* _ModeStyle)
{
#if WITH_EDITOR
	StyleContext = Context;
#endif
	ModeStyle = _ModeStyle;
	TakeSkin(true);
}

void UUserSkinStyle::DisapplySkin()
{
	TakeSkin(false);
	ModeStyle = nullptr;
}

void UUserSkinStyle::OnUAEUserWdigetNativeConstruct(UUAEUserWidget* UserWidget)
{
	if (UserWidget)
	{
		UClass* WidgetClass = UserWidget->GetClass();
		if (WidgetClass)
		{
			for (const FUserSkinInfo& Info : SkinInfoList)
			{
				if (!IsValidStyleByMode(Info))
				{
					if (!Info.Skin.IsNull())
					{
						UE_LOG(LogTemp, Log, TEXT("UUserSkinStyle::OnUAEUserWdigetNativeConstruct IsValidStyleByMode[%s]"), *Info.Skin.GetAssetName());
					}
					continue;
				}

				if (Info.WidgetClass.IsValid())
				{
					if (Info.WidgetClass->GetFName() == WidgetClass->GetFName())
					{
						if (GPCSkinForceUnrevertable > 0)
						{
							UserWidget->ApplyUnRevertableSkinData(Info.Skin);
						}
						else
						{
							UserWidget->ApplySkinDataByPath(Info.Skin);
						}
					}
				}
			}
		}
	}
}