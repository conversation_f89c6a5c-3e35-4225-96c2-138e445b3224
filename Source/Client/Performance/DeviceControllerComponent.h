
#pragma once

#include "CoreMinimal.h"
#include "Components/ActorComponent.h"
#include "DeviceControllerComponent.generated.h"


UENUM(BlueprintType)
enum class EDeviceVisibilityStrategy : uint8
{
	None = 0			UMETA(DisplayName = "关闭"),

	LowLevelHiddenOnly	UMETA(DisplayName = "启用 隐藏 (<=指定设备等级)"),
	HighLevelHiddenOnly	UMETA(DisplayName = "启用 隐藏 (>=指定设备等级)"),

	SpecifyShowOnly		UMETA(DisplayName = "启用 显示 (指定机型)"),
	SpecifyHiddenOnly	UMETA(DisplayName = "启用 隐藏 (指定机型)")
};

UCLASS(ClassGroup = "Performance", BlueprintType, editinlinenew, meta = (DisplayName = "Device Controller", BlueprintSpawnableComponent))
class CLIENT_API UDeviceControllerComponent : public UActorComponent
{
	GENERATED_BODY()

public:
	/// <summary>
	/// Visibility Setting
	/// </summary>

	/**
	 * 可按照设备等级或机型来显隐当前Actor
	 */
	UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = DeviceControllerVisibility, meta = (DisplayName = "特定机型隐藏策略"))
	EDeviceVisibilityStrategy VisibilityStategy = EDeviceVisibilityStrategy::None;

	/**
	 * 当前机型设备档位 >= or <= 当前档位时（取决于策略）, 所属Actor不隐藏, 否则所属Actor将在BeginPlay时被隐藏 (2-3: 低端机; 4-5: 中端机; 6-8: 高端机）
	 */
	UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = DeviceControllerVisibility, meta = (DisplayName = "指定机型等级"))
	int32 DeviceGradeLevelSplit = 1;

	/**
	 * 当前机型设备名等于该数组中任意名称时, 所属Actor将在BeginPlay时被隐藏 (仅当"指定设备隐藏策略"启用时, 该数组才能生效)
	 */
	UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = DeviceControllerVisibility, meta = (DisplayName = "指定机型列表"))
	TSet<FString> DeviceModelArray;

public:

	void BeginPlay() override;

private:

	bool IsSpcifyDeviceModel() const 
	{ 
		return VisibilityStategy == EDeviceVisibilityStrategy::SpecifyHiddenOnly || VisibilityStategy == EDeviceVisibilityStrategy::SpecifyShowOnly;
	};

	bool IsSpcifyDeviceLevel() const
	{
		return VisibilityStategy == EDeviceVisibilityStrategy::LowLevelHiddenOnly || VisibilityStategy == EDeviceVisibilityStrategy::HighLevelHiddenOnly;
	};
};

