// Copyright 2023 Tencent, Inc. All Rights Reserved.

#pragma once
#if WIN_RELEASE || WIN_DEFERRED_OB || (UE_EDITOR && PLATFORM_WINDOWS)
#include "CoreMinimal.h"
#include "Algo/Transform.h"
namespace WindowsBenchmarkUtils
{
	struct FGPUName;
	struct FCPUName;

	template<typename DeviceName>
	using Requirement = typename TEnableIf<
		TIsSame<DeviceName, FGPUName>::Value ||
		TIsSame<DeviceName, FCPUName>::Value
	>::Type;

	template<typename DeviceName, typename = Requirement<DeviceName>>
	int32 MostSimilarDeviceIndex(const TArray<FString>& DeviceList, const FString& CurDevice)
	{
		const DeviceName CurDeviceName{CurDevice};
		int32 MostSimilarDeviceIndex = INDEX_NONE;
		int32 MaxSimilarity = 0;
		for (auto It = DeviceList.CreateConstIterator(); It; ++It)
		{
			const int32 Similarity = DeviceName(*It) == CurDeviceName;
			if (Similarity > MaxSimilarity)
			{
				MaxSimilarity = Similarity;
				MostSimilarDeviceIndex = It.GetIndex();
			}
		}
		return MostSimilarDeviceIndex;
	}

	template<typename DeviceName, typename = Requirement<DeviceName>>
	int32 MostSimilarDeviceIndex(const TArray<FName>& DeviceList, const FString& CurDevice)
	{
		TArray<FString> DeviceStringList;
		Algo::Transform(DeviceList, DeviceStringList, [](const FName& Name) -> FString
		{
			return Name.ToString();
		});
		return MostSimilarDeviceIndex<DeviceName>(DeviceStringList, CurDevice);
	}

	struct FDeviceName
	{
		explicit FDeviceName() = default;
		explicit FDeviceName(const FDeviceName& Other) = delete;
		explicit FDeviceName(FDeviceName&& Other) = delete;
		FDeviceName& operator=(const FDeviceName& Other) = delete;
		FDeviceName& operator=(FDeviceName&& Other) = delete;

		int32 operator==(const FDeviceName& Other) const;
		FString ToString() const;
	protected:
		static bool RegexSearch(const FString& Pattern, const FString& Text);
		static FString RegexSub(const FString& Pattern, const FString& Text);
		static FString GetInfo(TArray<FString>& TokenList, const FString& Pattern);
		static bool RemoveInfo(TArray<FString>& TokenList, const FString& Pattern);
		static bool RemoveSpecialInfo(TArray<FString>& TokenList, const FString& Unit);
		static void UpperAndSplit(TArray<FString>& TokenList, const FString& DeviceFullName);

		int32 MaxSimilarityScore = 10;
		FString Vendor{"Unknown"};
		FString Model{"Unknown"};
		TSet<FString> Features{};
	};

	struct FCPUName : public FDeviceName
	{
		explicit FCPUName(const FString& CPUFullName);
		explicit FCPUName() = delete;
		explicit FCPUName(const FCPUName& Other) = delete;
		explicit FCPUName(FCPUName&& Other) = delete;
		FCPUName& operator=(const FCPUName& Other) = delete;
		FCPUName& operator=(FCPUName&& Other) = delete;

	private:
		const FString ModelPattern = R"(\d{2,})";
		const FString RemovePattern = R"(^CPU$|^FOR$|^\d+TH$|^GEN$|^PROCESSOR|\d+-CORES$)";
	};

	struct FGPUName : public FDeviceName
	{
		explicit FGPUName(const FString& GPUFullName);
		explicit FGPUName() = delete;
		explicit FGPUName(const FGPUName& Other) = delete;
		explicit FGPUName(FGPUName&& Other) = delete;
		FGPUName& operator=(const FGPUName& Other) = delete;
		FGPUName& operator=(FGPUName&& Other) = delete;

	private:
		const FString ModelPattern = R"(^(?!R)\D*\d|TITAN|VEGA|FURY|^V.*)";
		const FString RemovePattern = R"(^\d+(\.\d+)?W$|^DESKTOP$|^GRAPHICS$|^GA\d+$|^CPU$|^GPU$|^FOR$|^\d+TH$|^GEN$|^PROCESSOR|^RADEON|^ANNIVERSARY$)";
		// const FString RemovePattern = R"(^\d+(\.\d+)?W$|^DESKTOP$|^GRAPHICS$|^GA\d+$|^CPU$|^FOR$|^\d+TH$|^GEN$|^PROCESSOR|^ANNIVERSARY$)";
	};
}
#endif
