// Fill out your copyright notice in the Description page of Project Settings.

#include "AutoMem.h"
#include "UAEUserWidget.h"
//#include "GameBackendHUD.h"
#include "GameFrontendHUD.h"
#include "LogicManagerBase.h"
#include "GameBusinessManager.h"
#include "Engine/GameViewportClient.h"
//#include "Engine/DataTable.h"
#include "Components/Widget.h"
//#include "Components/Image.h"
#include "Components/Button.h"
//#include "Components/PanelSlot.h"
#include "Blueprint/WidgetTree.h"
#include "Client/UMG/UReuseListC.h"
#include "Math/RandomStream.h"


DECLARE_LOG_CATEGORY_EXTERN(LogAutoMem, Log, All);

DEFINE_LOG_CATEGORY(LogAutoMem);



void UAutoMem::TriggerButton(ALuaClassObj* InLuaClassObj, const FString& ButtonName, FString ReuseListName, int32 idx/* = 1*/)
{
	struct FInner {
		static void Find(UUserWidget* userWidget, const FString& ButtonName, FString ReuseListName, int32 idx) {
			TArray<UWidget*> subWidgets;
			if (userWidget->WidgetTree)
			{
				userWidget->WidgetTree->GetAllWidgets(subWidgets);
			}

			for (UWidget* Widget : subWidgets)
			{
				CheckWidget(Widget, ButtonName, ReuseListName, idx);
			}
		}
		static void Find(UUAEUserWidget* uaeUserWidget, const FString& ButtonName, FString ReuseListName, int32 idx) { // 不用bool，保证每次遍历复杂度相同
			TArray<UWidget*> subWidgets;
			uaeUserWidget->GetAllWidgets(subWidgets);

			for (UWidget* Widget : subWidgets)
			{
				CheckWidget(Widget, ButtonName, ReuseListName, idx);
			}
		}
		static void CheckWidget(UWidget* Widget, const FString& ButtonName, FString ReuseListName, int32 idx) {

			FString WidgetName = Widget->GetName();
			UClass* WidgetClass = Widget->GetClass();
			if (WidgetClass == nullptr) {
				UE_LOG(LogAutoMem, Error, TEXT("UAutoMem::TriggerButton  WidgetClass is null  WidgetName:%s"), *WidgetName);
				return;
			}
			FString ClassName = WidgetClass->GetName();

			UE_LOG(LogAutoMem, Log, TEXT("Widget  name:%s  class:%s"), *WidgetName, *ClassName);

			if (Widget->IsA<UButton>() && Widget->GetName().Equals(ButtonName))
			{
				UE_LOG(LogAutoMem, Log, TEXT("TriggerButton Seccessfully Broadcast !"));

				UButton* Button = Cast<UButton>(Widget);
				if (Button)
				{
					Button->OnClicked.Broadcast();
				}
			}
			else if (Widget->IsA<UReuseListC>() && Widget->GetName().Equals(ReuseListName))
			{
				UReuseListC* ReuseList = Cast<UReuseListC>(Widget);
				if (ReuseList)
				{
					TArray<UUserWidget*> ResultItemList;
					ReuseList->GetAllWidgetItems(ResultItemList);
					if (ResultItemList.IsValidIndex(idx))
					{
						UUserWidget* userWidget = Cast<UUserWidget>(ResultItemList[idx]);
						if (userWidget)
						{
							Find(userWidget, ButtonName, ReuseListName, 0);
						}
					}
					else
					{
						UE_LOG(LogAutoMem, Log, TEXT("TriggerButton  not  ResultItemList.IsValidIndex(idx)!  name:%s  length:%d  idx:%d"), *ReuseListName, ResultItemList.Num(), idx);
					}

				}
			}
			else if (Widget->IsA<UUAEUserWidget>())
			{
				UUAEUserWidget* uaeUserWidget3 = Cast<UUAEUserWidget>(Widget);
				if (uaeUserWidget3)
				{
					Find(uaeUserWidget3, ButtonName, ReuseListName, idx);
				}
			}
			else if (Widget->IsA<UUserWidget>())
			{
				UUserWidget* userWidget = Cast<UUserWidget>(Widget);
				if (userWidget)
				{
					Find(userWidget, ButtonName, ReuseListName, idx);
				}
			}
		}

	};
	if (InLuaClassObj == nullptr) {
		UE_LOG(LogAutoMem, Error, TEXT("UAutoMem::TriggerButton  InLuaClassObj is null"));
		return;
	}
	ULogicManagerBase* pLogic = InLuaClassObj->GetManager();
	if (pLogic == nullptr) {
		UE_LOG(LogAutoMem, Error, TEXT("UAutoMem::TriggerButton  InLuaClassObj GetManager is null"));
		return;
	}
	UE_LOG(LogAutoMem, Log, TEXT("UAutoMem::TriggerButton  ALuaClassObj:%s  ButtonName:%s  ReuseListName:%s  idx:%d"), *InLuaClassObj->GetName(), *ButtonName, *ReuseListName, idx);


	// UGameBusinessManager* businessManager = Cast<UGameBusinessManager>(pLogic);
	// if (businessManager == nullptr) {
	// 	UE_LOG(LogAutoMem, Error, TEXT("UAutoMem::TriggerButton  InLuaClassObj businessManager is null"));
	// 	return;
	// }


	for (UUAEUserWidget* uaeUserWidget : pLogic->GetWidgetListRef()) {
		FInner::Find(uaeUserWidget, ButtonName, ReuseListName, idx - 1);
	}

}

FString UAutoMem::AutoTriggerButton(TArray<ALuaClassObj*> LuaClassObjList, const TArray<FString>& BannedButtonNames)
{
	struct FInner {
		static void Find(UUserWidget* userWidget, TArray<UButton*>& ButtonList, const TArray<FString>& BannedButtonNames) {
			if (!userWidget->IsVisible())
			{
				UE_LOG(LogAutoMem, Log, TEXT("UAutoMem::AutoTriggerButton  userWidget->IsVisible false  return   Name:%s"), *(userWidget->GetName()));
				return;
			}

			TArray<UWidget*> subWidgets;
			if (userWidget->WidgetTree)
			{
				userWidget->WidgetTree->GetAllWidgets(subWidgets);
			}

			for (UWidget* Widget : subWidgets)
			{
				CheckWidget(Widget, ButtonList, BannedButtonNames);
			}
		}
		//static void Find(UUAEUserWidget* uaeUserWidget, TArray<UButton*>& ButtonList) { // 不用bool，保证每次遍历复杂度相同
		//	if (!uaeUserWidget->Visible())
		//	{
		//		UE_LOG(LogAutoMem, Log, TEXT("UAutoMem::AutoTriggerButton  uaeUserWidget->Visible false  return   Name:%s"), *(uaeUserWidget->GetName()));
		//		return;
		//	}
		//	TArray<UWidget*> subWidgets;
		//	uaeUserWidget->GetAllWidgets(subWidgets);
		//	for (UWidget* Widget : subWidgets)
		//	{
		//		CheckWidget(Widget, ButtonList);
		//	}
		//}
		static void CheckWidget(UWidget* Widget, TArray<UButton*>& ButtonList, const TArray<FString>& BannedButtonNames) {

			FString WidgetName = Widget->GetName();
			UClass* WidgetClass = Widget->GetClass();
			if (WidgetClass == nullptr) {
				UE_LOG(LogAutoMem, Error, TEXT("UAutoMem::AutoTriggerButton  WidgetClass is null  WidgetName:%s"), *WidgetName);
				return;
			}
			FString ClassName = WidgetClass->GetName();

			UE_LOG(LogAutoMem, Log, TEXT("Widget  name:%s  class:%s"), *WidgetName, *ClassName);

			if (Widget->IsA<UButton>()&& Widget->IsVisible()&& !BannedButtonNames.Contains(WidgetName))
			{
				//UE_LOG(LogAutoMem, Log, TEXT("AutoTriggerButton Seccessfully Broadcast !"));

				UButton* Button = Cast<UButton>(Widget);
				if (Button)
				{
					ButtonList.Add(Button);
				}
			}
			/*else if (Widget->IsA<UReuseListC>() && Widget->GetName().Equals(ReuseListName))
			{
				UReuseListC* ReuseList = Cast<UReuseListC>(Widget);
				if (ReuseList)
				{
					TArray<UUserWidget*> ResultItemList;
					ReuseList->GetAllWidgetItems(ResultItemList);
					if (ResultItemList.IsValidIndex(idx))
					{
						UUserWidget* userWidget = Cast<UUserWidget>(ResultItemList[idx]);
						if (userWidget)
						{
							Find(userWidget, ButtonName, ReuseListName, 0);
						}
					}
					else
					{
						UE_LOG(LogAutoMem, Log, TEXT("AutoTriggerButton  not  ResultItemList.IsValidIndex(idx)!  name:%s  length:%d  idx:%d"), *ReuseListName, ResultItemList.Num(), idx);
					}

				}
			}*/
			/*else if (Widget->IsA<UUAEUserWidget>())
			{
				UUAEUserWidget* uaeUserWidget3 = Cast<UUAEUserWidget>(Widget);
				if (uaeUserWidget3)
				{
					Find(uaeUserWidget3, ButtonList);
				}
			}*/
			else if (Widget->IsA<UUserWidget>())
			{
				UUserWidget* userWidget = Cast<UUserWidget>(Widget);
				if (userWidget)
				{
					Find(userWidget, ButtonList, BannedButtonNames);
				}
			}
		}

	};

	UE_LOG(LogAutoMem, Log, TEXT("UAutoMem::AutoTriggerButton  Start"));

	TArray<UButton*> ButtonList;
	for (ALuaClassObj* InLuaClassObj : LuaClassObjList)
	{
		// 对每个元素执行操作
		// InLuaClassObj 是当前遍历到的元素
		if (InLuaClassObj == nullptr) {
			UE_LOG(LogAutoMem, Error, TEXT("UAutoMem::AutoTriggerButton  InLuaClassObj is null"));
			return TEXT("");
		}
		ULogicManagerBase* pLogic = InLuaClassObj->GetManager();
		if (pLogic == nullptr) {
			UE_LOG(LogAutoMem, Error, TEXT("UAutoMem::AutoTriggerButton  InLuaClassObj GetManager is null"));
			return TEXT("");
		}
		UE_LOG(LogAutoMem, Log, TEXT("UAutoMem::AutoTriggerButton  ALuaClassObj:%s"), *InLuaClassObj->GetName());

		for (UUAEUserWidget* uaeUserWidget : pLogic->GetWidgetListRef()) {
			FInner::Find(uaeUserWidget, ButtonList, BannedButtonNames);
		}
	}

	UE_LOG(LogAutoMem, Log, TEXT("UAutoMem::AutoTriggerButton  Find Finish"));
	for (UButton* Button : ButtonList)
	{
		FString ButtonName = Button->GetName();
		UE_LOG(LogAutoMem, Log, TEXT("Button Name: %s"), *ButtonName);
	}

	int32 RandomIndex = FMath::RandRange(0, ButtonList.Num() - 1);


	// 获取随机选中的元素
	UButton* RandomButton = ButtonList[RandomIndex];
	FString ButtonName = RandomButton->GetName();
	UE_LOG(LogAutoMem, Log, TEXT("UAutoMem::AutoTriggerButton  Trigger: %s  !"), *ButtonName);

	RandomButton->OnClicked.Broadcast();
	return ButtonName;
}