#pragma once

#include "Client/BattleUI/Modules/BUIModule_GameBase.h"
#include "BUIModule_HunterStore.generated.h"

// �ͽ��̵�
UCLASS(BlueprintType)
class CLIENT_API UBUIModule_HunterStore : public UBUIModule_GameBase
{
	GENERATED_BODY()
public:
	UBUIModule_HunterStore();

	virtual void PostLoad_M() override;

	virtual void Tick(float Delta) override;

	virtual void PostLocalMainCharInitialized(ASTExtraPlayerController* InPlayerController) override;

	UFUNCTION(BlueprintCallable)
	void AddToStoreList(AActor* Store);

	UFUNCTION(BlueprintCallable)
	void RemoveFromStoreList(AActor* Store);

	UFUNCTION(BlueprintCallable)
	AActor* GetFirstStore();

	UFUNCTION(BlueprintImplementableEvent)
	void OnUpdateCurrentHunterStore(AActor* Store);

	UFUNCTION(BlueprintImplementableEvent)
	void InitStoreUI();

public:
	//�̵��б�
	UPROPERTY(EditAnywhere, BlueprintReadOnly)
	TArray<AActor*> StoreList;

	//��ǰ�������̵�
	UPROPERTY(EditAnywhere, BlueprintReadOnly)
	AActor* CurrentHunterStore;

	//��������
	UPROPERTY(EditAnywhere, BlueprintReadWrite)
	float OperateDisMin = 300.f;

	//�Ƿ����ò����Ƕ�
	UPROPERTY(EditAnywhere, BlueprintReadWrite)
	bool UseOpreateDegree = false;

	//�����Ƕ�Min
	UPROPERTY(EditAnywhere, BlueprintReadWrite)
	float OpreateDegreeMin = 0.f;

	//�����Ƕ�Max
	UPROPERTY(EditAnywhere, BlueprintReadWrite)
	float OpreateDegreeMax = 90.f;

	//�Ƿ����ò����߶�
	UPROPERTY(EditAnywhere, BlueprintReadWrite)
		bool UseOpreateHeight = false;

	//�����Ƕ�Min
	UPROPERTY(EditAnywhere, BlueprintReadWrite)
		float OpreateHeightMin = 0.f;

	//�����Ƕ�Max
	UPROPERTY(EditAnywhere, BlueprintReadWrite)
		float OpreateHeightMax = 100.f;

	//Ĭ��UI
	UPROPERTY(EditAnywhere, BlueprintReadWrite)
	FString UIName = TEXT("HunterStoreUI");

	UPROPERTY(BlueprintReadOnly)
	ASTExtraPlayerController* MyPlayerController;

	//tickƵ��
	UPROPERTY(EditAnywhere, BlueprintReadWrite)
	float TickFrequence = 1.0f;

	//UI��ʼ��flag
	UPROPERTY(EditAnywhere, BlueprintReadWrite)
	bool bIsStoreUIInit = false;

private:
	bool CheckOpreateDistance(AActor* Store, class ASTExtraBaseCharacter* CurCharacter);

	bool CheckOpreateDegree(AActor* Store, class ASTExtraBaseCharacter* CurCharacter);

	bool CheckOpreateHeight(AActor* Store, class ASTExtraBaseCharacter* CurCharacter);

private:
	float CurTickFrequence = 0.f;
};

