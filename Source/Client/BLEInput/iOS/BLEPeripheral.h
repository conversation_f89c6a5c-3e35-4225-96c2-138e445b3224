#if PLATFORM_IOS
#pragma once
#import <Foundation/Foundation.h>


@interface BLEPeripheral : NSObject


/**
 *  SDK单例模式
 */
+ (BLEPeripheral *)sharedPeripheral;



/**
 * 连接设备
 */
- (void)connectPeripheral;


/**
 *  断开设备
 */
- (void)disconnectPeripheral;

/**
*  随机校验串
*/
- (void)setBLEIdendify:(const char *) idendifyString;

/**
*  服务器校验结果
*/
- (void)setBLEResult:(int) result ;

/**
*  获取已连接的外设
*/
-(NSArray *)getConnectedPeripherals;

@end


#endif



