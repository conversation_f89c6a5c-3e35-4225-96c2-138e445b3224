#if WITH_MSDK
#if 0//PLATFORM_IOS || PLATFORM_ANDROID

#include "GCloudCrashListener.h"
#include "Misc/Paths.h"
#include "Misc/CoreMisc.h"
#include "Misc/FileHelper.h"
#include "Client/BpToolLib/BusinessHelper.h"
#include "GCloudSDKDelegates.h"
#include <GCloudCore/AString.h>
#include <GCloudCore/ALog.h>
#include "CoreGlobals.h"
#include "Misc/OutputDevice.h"
#include "GenericPlatform/GenericPlatformOutputDevices.h"
#if PLATFORM_ANDROID
#include "Android/AndroidMisc.h"
#endif
#include "Misc/App.h"
#include "HAL/IConsoleManager.h"

int32 GEnableReadLogFileOnCrash = 0;
static FAutoConsoleVariableRef CVarGEnableReadLogFileOnCrash(
	TEXT("s.GEnableReadLogFileOnCrash"),
	GEnableReadLogFileOnCrash,
	TEXT("enable read log file on crash"),
	ECVF_Default
);

int32 GEnableUploadLogFileOnCrash = 1;
static FAutoConsoleVariableRef CVarGEnableUploadLogFileOnCrash(
	TEXT("s.GEnableUploadLogFileOnCrash"),
	GEnableUploadLogFileOnCrash,
	TEXT("enable upload log file on crash"),
	ECVF_Default
);

int32 GEnableUploadSmaps = 0;
static FAutoConsoleVariableRef CVarGEnableUploadSmaps(
	TEXT("s.GEnableUploadSmaps"),
	GEnableUploadSmaps,
	TEXT("enable upload smaps file on crash"),
	ECVF_Default
);

GCloudCrashListener::GCloudCrashListener()
{
}

GCloudCrashListener::~GCloudCrashListener()
{
}

void GCloudCrashListener::SetDelegates(UGCloudSDKDelegates* delegates)
{
	_Delegates = delegates;
}

ABase::AString GCloudCrashListener::OnCrashExtMessageNotify()
{
	XLogInfo("GCloudCrashListener::OnCrashExtMessageNotify Called");

#if !(UE_BUILD_SHIPPING || UE_BUILD_TEST)

#if PLATFORM_ANDROID
	UE_LOG(LogAndroid, Warning, TEXT("App crashed! All log was flushed!"));
#else
	UE_LOG(LogIOS, Warning, TEXT("App crashed! All log was flushed!"));
#endif

	if (GLog)
	{
		GLog->PanicFlushThreadedLogs();
		GLog->SetCurrentThreadAsMasterThread();
		GLog->Flush();
	}
#endif

	FString CrashMessage = "";
	if (GEnableReadLogFileOnCrash)
	{
		FString LogFilePath = (FPaths::ProjectLogDir() + FString(TEXT("ShadowTrackerExtra.log")));
		FString FinalLogFilePath = UBusinessHelper::GetMobileBasePath(LogFilePath);
		XLogInfo("FinalLogFilePath:%s", TCHAR_TO_UTF8(*FinalLogFilePath));

		FILE* fp = fopen(TCHAR_TO_UTF8(*FinalLogFilePath), "r");
		if (fp != nullptr)
		{
			int LastLines = 35;
			char ch(' ');
			int Pos = -1;
			char buffer[256] = {};

			while (LastLines > 0) {
				while (ch != '\n')
				{
					fseek(fp, Pos, SEEK_END);
					ch = fgetc(fp);
					Pos--;
				}
				ch = ' ';

				memset(buffer, 0, 256);
				if (fgets(buffer, 256, fp) != nullptr)
				{
					CrashMessage += buffer;
					Pos--;
				}
				LastLines--;
			}
			fclose(fp);
		}
	}
	if (_Delegates && _Delegates->CrashMessageNotifyDelegate.IsBound())
	{
		CrashMessage = _Delegates->CrashMessageNotifyDelegate.Execute() + CrashMessage;
	}
#if DO_BLUEPRINT_GUARD
	CrashMessage += FString(TEXT("\n BluePrintsStack:\n"));
	FBlueprintExceptionTracker& BlueprintExceptionTracker = FBlueprintExceptionTracker::Get();

	if (BlueprintExceptionTracker.ScriptStack.Num() > 0)
	{
		for (int32 i = BlueprintExceptionTracker.ScriptStack.Num() - 1; i >= 0; --i)
		{
			CrashMessage += FString(TEXT("\t")) + BlueprintExceptionTracker.ScriptStack[i].GetStackDescription() + TEXT("\n");
		}
	}
	CrashMessage += FString(TEXT("\n BluePrintsStack End===\n"));
#endif
#if PLATFORM_ANDROID
	CrashMessage += FString(TEXT("\nShaderCodeHash: ")) + FAndroidMisc::GetShaderCodeHashValue();

	char MemoryStats[100];
	memset(MemoryStats, 0, 100);
	FAndroidMisc::GetCurrentMemoryStats(MemoryStats);
	CrashMessage += FString(TEXT("\nMemoryStats: ")) + ANSI_TO_TCHAR(MemoryStats);
	if (GEnableUploadSmaps)
	{
		FAndroidMisc::DumpProcFile(TEXT("smaps"));
	}
#endif
	if (GEnableUploadLogFileOnCrash)
	{
		FString LastCrashContent = FString::Printf(TEXT("LastCrash_%s"), *FDateTime::Now().ToString(TEXT("%Y.%m.%d-%H.%M.%S:%s")));
		FString LastCrashFile = FPaths::ProjectSavedDir() + FString(TEXT("Crash/LastCrash.txt"));
		FFileHelper::SaveStringToFile(LastCrashContent, *LastCrashFile);
	}
	CrashMessage += FString(TEXT("\nFatal Log: ")) + FApp::GetFatalErrorMsg();
	CrashMessage += FApp::GetGCErrorMsg();
	CrashMessage += FString(TEXT("\nBuglyReport: ============================================\n")) + FApp::GetBuglyReportMsg();
	if (ReportLogEnable && ReportLogSize > 0)
	{
		FOutputDevice* OutputDevice = FGenericPlatformOutputDevices::GetLog();
		if (OutputDevice)
		{
			FString LastLog = OutputDevice->GetLastLog(ReportLogSize);
			CrashMessage += FString(TEXT("\nLatest GameLog: ============================================\n")) + LastLog;
		}
	}
	return TCHAR_TO_UTF8(*CrashMessage);
}

void GCloudCrashListener::EnableReportLog(int32 CrashReportLogSize)
{
	ReportLogEnable = true;
	ReportLogSize = CrashReportLogSize;
}

void GCloudCrashListener::DisableReportLog()
{
	ReportLogEnable = false;
}

#ifdef ANDROID
unsigned char* GCloudCrashListener::OnCrashExtDataNotify()
{
	return nullptr;
}
#endif

 
#endif
#endif