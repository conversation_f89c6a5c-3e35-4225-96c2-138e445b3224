// Fill out your copyright notice in the Description page of Project Settings.

#pragma once

#include "CoreMinimal.h"
#include "Components/ActorComponent.h"
#include "UGCMDropItemMonitorComponent.generated.h"


DECLARE_LOG_CATEGORY_EXTERN(LogUGCMDropItemMonitor, Log, All);


USTRUCT()
struct FUGCMRangeKey
{
	GENERATED_BODY()

	UPROPERTY()
	int32 X;
	UPROPERTY()
	int32 Y;

	// 用于Map排序等
	inline bool operator<(const FUGCMRangeKey& InKey) const
	{
		return X == InKey.X ? Y < InKey.Y : X < InKey.X;
	}
	inline bool operator==(const FUGCMRangeKey& InKey) const
	{
		return X == InKey.X && Y == InKey.Y;
	}

	// 用于计算hash
	friend inline uint32 GetTypeHash(const FUGCMRangeKey& InKey)
	{
		return HashCombine(GetTypeHash(InKey.X), GetTypeHash(InKey.Y));
	}
};

USTRUCT()
struct FUGCMActorList
{
	GENERATED_BODY()

	UPROPERTY()
	TArray<AActor*> Datas;
};


// 掉落物监控组件
// 每个区域掉落物限定一定数量内
UCLASS(BlueprintType, Blueprintable, ClassGroup = (Custom))
class UUGCMDropItemMonitorComponent : public UActorComponent
{
	GENERATED_BODY()

public:
	virtual void BeginPlay() override;
	virtual void EndPlay(const EEndPlayReason::Type EndPlayReason) override;

	UFUNCTION()
	void RegisterDropItemEvents();
	UFUNCTION()
	void UnregisterDropItemEvents();
	UFUNCTION()
	void OnDropItemSpawned(AActor* InItem);
	UFUNCTION()
	void OnDropItemPreDestroy(AActor* InItem);

	// 移除溢出的掉落物
	UFUNCTION()
	void RemoveItem(AActor* InItem);

public:
	UPROPERTY(EditAnywhere)
	int32 CellLength = 10000; // 每个区域长宽

	UPROPERTY(EditAnywhere)
	int32 MaxNumPerCell = 100; // 每个区域最大数量

private:
	UPROPERTY()
	TMap<FUGCMRangeKey, FUGCMActorList> DropItemDatas; // 缓存

};
