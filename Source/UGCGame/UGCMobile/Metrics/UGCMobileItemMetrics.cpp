#include "UGCMobileItemMetrics.h"
#include "UAETableManager.h"
#include "Engine/StaticMesh.h"
#include "Materials/Material.h"
#include "Particles/ParticleSystemComponent.h"

static void OnCalcCostForAllItems_Metrics(const TArray<FString>& Args, UWorld* World, FOutputDevice& Ar)
{
	if (World == nullptr)
	{
		return;
	}
	
	UUAETableManager* TableManager = UUAETableManager::GetInstance();
	if (TableManager == nullptr)
	{
		return;
	}

	UUAEDataTable* Tab = TableManager->GetTablePtr(TEXT("UGCMobileItems"));
	if (Tab == nullptr)
	{
		return;
	}

	TArray<FName> Keys = Tab->GetRowNames();

	for (FName Key : Keys)
	{
		FString BlueprintPath = Tab->GetTableData_String(Key.ToString(), TEXT("BlueprintPath"));

		UClass* BlueprintClass = LoadClass<UObject>(nullptr, *BlueprintPath);

		if (BlueprintClass)
		{
			if (AActor* TempActor = World->SpawnActor(BlueprintClass))
			{
				float Cost = UGCMobileItemMetrics::MeasureActorCost(TempActor);
				
				UE_LOG(LogTemp, Warning, TEXT("[CalcCost] ID=%s, Class=%s, Cost=%.3f%%"), *Key.ToString(), *BlueprintClass->GetName(),  Cost);

				TempActor->Destroy();
			}
		}
	}
}

static FAutoConsoleCommand CalcCostForAllItemsCmd_Metrics(
	TEXT("CalcCostForAllItemsCmd"),
	TEXT(""),
	FConsoleCommandWithWorldArgsAndOutputDeviceDelegate::CreateStatic(&OnCalcCostForAllItems_Metrics)
);

static float GetMaterialGPUCostPerTriangle_Metrics(UMaterial* InMaterial)
{
	if (!InMaterial)
	{
		return 0;
	}

	UMaterial* Material = InMaterial;

	while (Material)
	{
		FString MaterialName = Material->GetName();

		if (MaterialName.Contains(TEXT("M_UGCUI_Icon")))
		{
			return 0.7;
		}
		else if (MaterialName.Contains(TEXT("M_UGC_Object")))
		{
			return 0.125;
		}
		else if (MaterialName.Contains(TEXT("M_UGC_Grass")))
		{
			return 0.12;
		}
		else if (MaterialName.Contains(TEXT("M_UGC_Building_IdeaBake")))
		{
			return 0.08;
		}
		else if (MaterialName.Contains(TEXT("M_UGC_Foliage")))
		{
			return 0.05;
		}
		else if (MaterialName.Contains(TEXT("M_UGC_Rock")))
		{
			return 0.04;
		}
		else if (MaterialName.Contains(TEXT("M_UGC_Module")))
		{
			return 0.03;
		}

		if (Material->GetBaseMaterial() != Material)
		{
			Material = Material->GetBaseMaterial();
		}
		else
		{
			break;
		}
	}

	return 0.08;
}

float UGCMobileItemMetrics::MeasureActorCost(AActor* InActor, bool bIsRoot)
{
	if (!InActor)
	{
		return 0;
	}

	float TotalCost = 0;

	for (UActorComponent* Comp : InActor->GetComponents())
	{
		if (UStaticMeshComponent* SMComp = Cast<UStaticMeshComponent>(Comp))
		{
			TotalCost += MeasureStaticMesh(SMComp);
		}
		else if (UParticleSystemComponent* ParticleComp = Cast<UParticleSystemComponent>(Comp))
		{
			TotalCost += 10;
		}
	}

	if (bIsRoot)
	{
		TArray<AActor*> ChildActors;
		InActor->GetAllChildActors(ChildActors, true);

		for (AActor* ChildActor : ChildActors)
		{
			UE_LOG(LogTemp, Warning, TEXT("[CalcCost]    ChildClass=%s"), ChildActor ? *ChildActor->GetClass()->GetName() : TEXT("NULL"));
		
			TotalCost += MeasureActorCost(ChildActor, false);
		}
	}

	return TotalCost;
}

float UGCMobileItemMetrics::MeasureStaticMesh(UStaticMeshComponent* InComponent)
{
	if (!InComponent)
	{
		return 0;
	}

	UStaticMesh* StaticMesh = InComponent->GetStaticMesh();
	if (!StaticMesh)
	{
		return 0;
	}

	if (StaticMesh->GetNumLODs() < 1)
	{
		return 0;
	}

	float TotalCost = 0;

	const FStaticMeshLODResources& LODResources = StaticMesh->GetLODForExport(0);

	// Loop through each section in LOD0
	for (int32 SectionIndex = 0; SectionIndex < LODResources.Sections.Num(); ++SectionIndex)
	{
		const FStaticMeshSection& Section = LODResources.Sections[SectionIndex];

		// Get the number of triangles in this section
		int32 TriangleCount = Section.NumTriangles;

		// Get the material for this section
		UMaterialInterface* Material = StaticMesh->GetMaterial(Section.MaterialIndex);

		if (Material)
		{
			float CostPerTriangle = GetMaterialGPUCostPerTriangle_Metrics(Material->GetBaseMaterial());

			float Cost = TriangleCount * CostPerTriangle;

			UE_LOG(LogTemp, Warning, TEXT("[CalcCost]    StaticMesh=%s, SectionIndex=%d, Material=%s, TriangleCount=%d, CostPerTriangle=%f, Cost=%f"),
				*StaticMesh->GetName(), SectionIndex, *Material->GetName(), TriangleCount, CostPerTriangle, Cost);

			TotalCost += Cost;
		}
	}

	return TotalCost;
}
