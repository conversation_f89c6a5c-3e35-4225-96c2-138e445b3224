// Fill out your copyright notice in the Description page of Project Settings.

#include "ShadowTrackerExtra.h"
#include "ParachutingControlWidget.h"
#include "Player/STExtraPlayerController.h"
#include "FunctionUtils.h"

void UParachutingControlWidget::HandleCacheMsg(UParachutingControlWidget* InParachutingCW, const FString& InCacheMsg)
{
	if (!InParachutingCW || InParachutingCW->IsPendingKill() || InCacheMsg.IsEmpty())
	{
		return;
	}
#if !(UE_BUILD_SHIPPING || UE_BUILD_TEST)
	UE_LOG(LogTemp, Log, TEXT("UParachutingControlWidget::HandleCacheMsg [%s]"), *InCacheMsg);
#endif
	FName FuncName(*InCacheMsg);
	FFunctionUtils::CallObjectFunction(InParachutingCW, FuncName);
}

void UParachutingControlWidget::HandleCacheGuideMsg(UParachutingControlWidget* InParachutingCW, const FString& InCacheMsg, const bool IsShow, const FC2BPGuideTextParam& TipsParam)
{
	if (!InParachutingCW || InParachutingCW->IsPendingKill() || InCacheMsg.IsEmpty())
	{
		return;
	}
#if !(UE_BUILD_SHIPPING || UE_BUILD_TEST)
	UE_LOG(LogTemp, Log, TEXT("UParachutingControlWidget::HandleCacheGuideMsg [%s, IsShow:%d]"), *InCacheMsg, (int32)IsShow);
#endif
	FName FuncName(*InCacheMsg);
	FFunctionUtils::CallObjectFunction(InParachutingCW, FuncName, IsShow, TipsParam);
}

void UParachutingControlWidget::BP_StartJump_Implementation()
{
	ASTExtraPlayerController* MyPlayerController = Cast<ASTExtraPlayerController>(GetOwningPlayer());
	if (MyPlayerController)
	{
		MyPlayerController->JumpFromPlane();
	}
}

void UParachutingControlWidget::BP_OpenParachute_Implementation()
{
	ASTExtraPlayerController* MyPlayerController = Cast<ASTExtraPlayerController>(GetOwningPlayer());
	if (MyPlayerController)
	{
		MyPlayerController->OpenParachute();
	}
}

void UParachutingControlWidget::BP_ReceiveFollowRequst_Implementation()
{

}

void UParachutingControlWidget::BP_Reconnect_ResetUIByPlayerControllerState_Implementation()
{

}

void UParachutingControlWidget::BP_ShowBtnByPlayerControllerState_Implementation()
{

}

void UParachutingControlWidget::BP_BleParachute_Implementation()
{

}

void UParachutingControlWidget::BP_BleOpenParachute_Implementation()
{

}

void UParachutingControlWidget::BP_ShowBtnByState_Implementation(int32 State)
{

}
void UParachutingControlWidget::BP_ReceiveTransferLeaderRequest_Implementation()
{

}
void UParachutingControlWidget::BP_ReceiveTransferAirCraftDriverRequest_Implementation()
{

}
void UParachutingControlWidget::BP_Show_HideCanOpenTips_Implementation(const bool IsShow, const FC2BPGuideTextParam& TipsParam)
{

}

void UParachutingControlWidget::BP_Show_HideCanJumpTips_Implementation(const bool IsShow, const FC2BPGuideTextParam& TipsParam)
{

}

void UParachutingControlWidget::BP_InitWidget_Implementation(UUAEUserWidget* MyParentWidget)
{

}

void UParachutingControlWidget::NativeTick(const FGeometry& MyGeometry, float InDeltaTime)
{
	Super::NativeTick(MyGeometry, InDeltaTime);
	if ((this->GetVisibility() == ESlateVisibility::SelfHitTestInvisible || this->GetVisibility() == ESlateVisibility::Visible) &&
		InviteCountDownTime > 0.f && bIsCountingDown)
	{
		InviteCountDownTime -= InDeltaTime;

		FString CountDownValueString = FString::FromInt(FMath::CeilToInt(InviteCountDownTime)).AppendChar(TEXT('s'));
		FText CountDownText = FText::FromString(CountDownValueString);
		if (Text_Refuse_TimeLeft)
		{
			Text_Refuse_TimeLeft->SetText(CountDownText);
		}
		if (Text_Agree_TimeLeft)
		{
			Text_Agree_TimeLeft->SetText(CountDownText);
		}
		if (InviteCountDownTime <= 0.f)
		{
			CountDownFinishResponInvite();
		}
	}

}
