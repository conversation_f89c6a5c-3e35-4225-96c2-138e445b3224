// Fill out your copyright notice in the Description page of Project Settings.

#include "ShadowTrackerExtra.h"
#include "Utility/UIDataProcessingFunctionLibrary.h"
#include "Blueprint/WidgetLayoutLibrary.h"
#include "Components/CanvasPanelSlot.h"
#include "ChasingBar.h"

#undef LOCTEXT_NAMESPACE
#define LOCTEXT_NAMESPACE "ChasingBar"
DECLARE_CYCLE_STAT(TEXT("ChasingBar Tick"), STAT_ChasingBar_Tick, STATGROUP_UAEUserWidget);
DECLARE_CYCLE_STAT(TEXT("ChasingBar CalPlayerEscapePercent"), STAT_CalPlayerEscapePercent, STATGROUP_UAEUserWidget);

UChasingBar::UChasingBar(const FObjectInitializer& ObjectInitializer):
	Super(ObjectInitializer)
{
	bCanEverTick = true;
}

float UChasingBar::CalPlayerEscapePercent()
{
	SCOPE_CYCLE_COUNTER(STAT_CalPlayerEscapePercent)
	ASTExtraGameStateBase* GS = Cast<ASTExtraGameStateBase>(UGameplayStatics::GetGameState(this));
	if (!GS)
	{
		return -1;
	}
	if (nullptr == STEPlayerController)
	{
		return -1.f;
	}

	//@todo evenlu GetCurPlayerCharacter 【【赛事OBS】【CG014】OBUI——小地图的选手进圈图标逻辑修复】
	//http://tapd.oa.com/CJGame/prong/stories/view/1020386762864242389
	APawn* CurPawn = STEPlayerController->GetCurPawn();
	if (nullptr == CurPawn)
	{
		return -1.f;
	}
	if (GS->GetCurCircleStatus() == ECircleStatus::ECircleReset ||  IsPlayerInWhiteCircle(CurPawn))
	{
		return 1.f;
	}

	FVector2D BlueCircleCenter = FVector2D(GS->GetOriginBlueCircle());
	float BlueCircleRadius = GS->GetOriginBlueCircle().Z;
	FVector2D WhiteCircleCenter = FVector2D(GS->GetWhiteCircle());
	float WhiteCircleRadius = GS->GetWhiteCircle().Z;
	FVector2D CurPawnLocation(CurPawn->GetActorLocation().X, CurPawn->GetActorLocation().Y);
	float BlueCircleCenterToPlayerDist = FVector2D::Distance(BlueCircleCenter, CurPawnLocation);
	float WhiteCircleCenterToPlayerDist = FVector2D::Distance(WhiteCircleCenter, CurPawnLocation);
	float WhiteCircleCenterToBlueCircleCenter = FVector2D::Distance(BlueCircleCenter, WhiteCircleCenter);
	float EscapePercent = 0.f;
	if (WhiteCircleCenterToBlueCircleCenter != 0)
	{
		//Law of cosines, Calculate the cosine value of angle Pawn-WhiteCenter-BlueCenter
		float CosineValueOfPawnToWhiteCToBlueC = (FMath::Pow(WhiteCircleCenterToBlueCircleCenter, 2) + FMath::Pow(WhiteCircleCenterToPlayerDist, 2)
			- FMath::Pow(BlueCircleCenterToPlayerDist, 2)) / (WhiteCircleCenterToBlueCircleCenter * WhiteCircleCenterToPlayerDist * 2);
		//Distance of BlueCenter to WhiteCenter-Player line
		float BlueCenterToWhitePlayerLine = WhiteCircleCenterToBlueCircleCenter * FMath::Sin(FMath::Acos(CosineValueOfPawnToWhiteCToBlueC));
		//foot point on the line white center to player
		float FLength = WhiteCircleCenterToBlueCircleCenter * CosineValueOfPawnToWhiteCToBlueC - WhiteCircleRadius;
		//White Circle Edge To Blue Circle Edge in the line which contain white center and blue center
		float WEdgeToBEdge = FLength + FMath::Sqrt(FMath::Pow(BlueCircleRadius, 2) - FMath::Pow(BlueCenterToWhitePlayerLine, 2));
		//Player to blue circle edge
		float PlayerToBlueCircleEdge = WEdgeToBEdge - (FMath::Sqrt(FMath::Pow(BlueCircleCenterToPlayerDist, 2)
			- FMath::Pow(BlueCenterToWhitePlayerLine, 2)) + FLength);
		EscapePercent = PlayerToBlueCircleEdge / WEdgeToBEdge;
	}
	else//白圈蓝圈是同心圆
	{
		float TotalDist = BlueCircleRadius - WhiteCircleRadius;
		float EscapedDist = BlueCircleRadius - WhiteCircleCenterToPlayerDist;
		EscapePercent = EscapedDist > 0 ? EscapedDist / TotalDist : 0.f;
	}
	EscapePercent = FMath::Clamp(EscapePercent, 0.f, 1.f);


	//CheckToCleanProgress();
	return EscapePercent;
}

bool UChasingBar::IsPlayerInWhiteCircle(APawn* CurPawn)
{
	ASTExtraGameStateBase* GS = Cast<ASTExtraGameStateBase>(UGameplayStatics::GetGameState(this));
	if (nullptr == GS || nullptr == STEPlayerController)
	{
		return false;
	}
	if (nullptr == CurPawn || GS->GetWhiteCircle().Z <= 0.f)
	{
		return false;
	}
	float CurWhiteCircleRadius = GS->GetWhiteCircle().Z;
	FVector CurPawnToCurWhiteCircleCenterVector = CurPawn->GetActorLocation();
	FVector2D CurPawnToCurWhiteCircleCenterVector2D(
							CurPawnToCurWhiteCircleCenterVector.X - GS->GetWhiteCircle().X,
							CurPawnToCurWhiteCircleCenterVector.Y - GS->GetWhiteCircle().Y);
	float CurPawnToWhiteCircle = CurPawnToCurWhiteCircleCenterVector2D.Size();

	return CurPawnToWhiteCircle > CurWhiteCircleRadius ? false : true;
}

bool UChasingBar::IsPlayerOutBlueCircle(APawn* CurPawn)
{
	ASTExtraGameStateBase* GS = Cast<ASTExtraGameStateBase>(UGameplayStatics::GetGameState(this));
	if (nullptr == GS || nullptr == CurPawn
		|| GS->GetBlueCircle().Z <= 0.f)
	{
		return false;
	}
	FVector2D CurPawnLocation(CurPawn->GetActorLocation().X, CurPawn->GetActorLocation().Y);
	FVector2D BlueCircleCenter(GS->GetBlueCircle().X, GS->GetBlueCircle().Y);
	float BlueCircleRadius = GS->GetBlueCircle().Z;
	float CurPawnToBlueCircleCenter = FVector2D::Distance(CurPawnLocation, BlueCircleCenter);
	return BlueCircleRadius > CurPawnToBlueCircleCenter ? false : true;
}

bool UChasingBar::IsBlueCircleAppearAndCountDown()
{
	ASTExtraGameStateBase* GS = Cast<ASTExtraGameStateBase>(UGameplayStatics::GetGameState(this));
	if (!GS)
	{
		return false;
	}
	if (GS->GetCurCircleStatus() == ECircleStatus::EShowCircle)
	{
		return true;
	}

	if (GS->LeftEscapeTime > 0)
	{
		return true;
	}

	return false;
}

void UChasingBar::BlueCirclePreMoveCountDown()
{
	ASTExtraGameStateBase* GS = Cast<ASTExtraGameStateBase>(UGameplayStatics::GetGameState(this));
	if (!GS)
	{
		return;
	}
	int32 PreCountDownTimeLeft = 0;
	//pve模式召唤直升机后的倒计时
	if (GS->LeftEscapeTime > 0)
	{
		PreCountDownTimeLeft = GS->LeftEscapeTime;
		if (!bIsCountDownTextCritical)
		{
			bIsCountDownTextCritical = true;
			CountDownTextChangeToCritical();
		}
	}
	else
	{
		float TimeElapsed = GS->GetServerWorldTimeSeconds() - GS->GetCircleStatusStartTime();
		PreCountDownTimeLeft = GS->GetCircleStatusTimespan() - TimeElapsed;
	}

	int32 MinutesLeft = FMath::Clamp(PreCountDownTimeLeft / 60, 0, 59);
	int32 SecondsLeft = FMath::Clamp(PreCountDownTimeLeft % 60, 0, 59);

	FString MinutesLeftText = FString::FromInt(MinutesLeft + 100);
	MinutesLeftText = MinutesLeftText.Mid(1, 2);
	FString SecondsLeftText = FString::FromInt(SecondsLeft + 100);
	SecondsLeftText = SecondsLeftText.Mid(1, 2);
	FString MinuteAndSecond = MinutesLeftText.Append(TEXT(":")).Append(SecondsLeftText);
	FText CurMinuteAndSecond = FText::FromString(MinuteAndSecond);
	{
		BlueCircleMovingCountDownText = CurMinuteAndSecond;
		UpdateCountDownText();
	}
}

bool UChasingBar::IsBlueCircleMoving()
{
	ASTExtraGameStateBase* GS = Cast<ASTExtraGameStateBase>(UGameplayStatics::GetGameState(this));
	if (!GS)
	{
		return false;
	}
	if (GS->GetCurCircleStatus() == ECircleStatus::ECircleRun)
	{
		return true;
	}

	return false;
}

void UChasingBar::CheckToCleanProgress()
{
	ASTExtraGameStateBase* GS = Cast<ASTExtraGameStateBase>(UGameplayStatics::GetGameState(this));
	if (!GS)
	{
		return;
	}
	if (GS->GetCurCircleStatus() != ECircleStatus::ECircleRun)
	{
		CleanTheProgressBar();
	}

}

void UChasingBar::NativeTick(const FGeometry& MyGeometry, float InDeltaTime)
{
	{
		accumTickTime += InDeltaTime;

		if (accumTickTime >= TickRate)
		{
			SCOPE_CYCLE_COUNTER(STAT_ChasingBar_Tick)
			
			PlayerEscapeProgress = CalPlayerEscapePercent();
			CheckToCleanProgress();


			if (IsBlueCircleAppearAndCountDown())
			{
				BlueCirclePreMoveCountDown();
			}
			else if (IsBlueCircleMoving())
			{
				OnCountDownTextInvisible();
			}
			else
			{
				OnCircleNotCountDown();
			}

			if (STEPlayerController != nullptr)
			{
				APawn* CurPawn = STEPlayerController->GetCurPawn();
				if (IsPlayerOutBlueCircleCurrently != IsPlayerOutBlueCircle(CurPawn))
				{
					IsPlayerOutBlueCircleCurrently = !IsPlayerOutBlueCircleCurrently;
					OnPlayerEnterExitBlueCircle();
				}
			}

			UUserWidget::NativeTick(MyGeometry, accumTickTime);
			if (!IsOBMode)
			{
				PlayerEscapePercentC();
				ShowCircleDistanceC();
			}
			accumTickTime = 0;
		}
	}
}

void UChasingBar::PlayerEscapePercentC()
{
	if (PlayerIcon != nullptr)
	{
		float X = UKismetMathLibrary::FClamp(PlayerPosition * PlayerEscapeProgress, 0.0f, PlayerPosition);
		FVector2D InPosition = FVector2D(X, 12.54);
		UCanvasPanelSlot* PlayerIconSlot = UWidgetLayoutLibrary::SlotAsCanvasSlot(PlayerIcon);
		if (PlayerIconSlot != nullptr)
		{
			PlayerIconSlot->SetPosition(InPosition);
		}
		
		if (TextBlock_Dist != nullptr)
		{
			UCanvasPanelSlot* TextDistSlot = UWidgetLayoutLibrary::SlotAsCanvasSlot(TextBlock_Dist);
			if (TextDistSlot != nullptr)
			{
				if (PlayerEscapeProgress >= 0.5f)
				{
					FVector2D InTranslation = FVector2D(X - 10.f, 0.f);
					TextDistSlot->SetAlignment(FVector2D(1.0f, 0.f));
					TextBlock_Dist->SetRenderTranslation(InTranslation);
					TextBlock_Dist->SetJustification(ETextJustify::Right);
				}
				else
				{
					FVector2D InTranslation = FVector2D(X + 10.f, 0.f);
					TextDistSlot->SetAlignment(FVector2D(0.0f, 0.f));
					TextBlock_Dist->SetRenderTranslation(InTranslation);
					TextBlock_Dist->SetJustification(ETextJustify::Left);
				}
			}
		}

	}
}

void UChasingBar::ShowCircleDistanceC()
{
	if (IsRefreshCircleDistance)
	{
		ASTExtraPlayerController* PC = Cast<ASTExtraPlayerController>(UGameplayStatics::GetPlayerController(this, 0));
		if (PC != nullptr)
		{
			if (TextBlock_Dist != nullptr)
			{
				int32 Distance = PC->GetWriteCircleDistance();
				if (Distance > 0)
				{
					FText InText = FText::Format(LOCTEXT("ChasingBar", "{0}m"), FText::AsNumber(Distance));
					TextBlock_Dist->SetText(InText);
				}
				else
				{
					TextBlock_Dist->SetText(FText());
				}
			}
		}
	}
}

#undef LOCTEXT_NAMESPACE