/************************************************************************
Author:	yintao
Date:	2018/07/05
Purpose: 
************************************************************************/

#pragma once

#include "CoreMinimal.h"
#include "Interface/PlayerLogicInterface.h"
#include "PlaneBase.h"
#include "CustomComponent/PlaneComponent.h"
#include "PlaneCharacter.generated.h"

DECLARE_LOG_CATEGORY_EXTERN(LogPlaneCharacter, Log, All);

class UCameraAnim;

USTRUCT(BlueprintType)
struct FPlaneBannerInfo
{
	GENERATED_USTRUCT_BODY()

	bool ShouldReplace() const;

	UPROPERTY()
	bool bEnablePlaneBanner = false;

	UPROPERTY()
	bool bPlaneBannerOnlyVisibleToObserver = false;

	UPROPERTY()
	FString HttpPath = TEXT("");

	UPROPERTY(Transient)
	UTexture2D* LoadedTexture = nullptr;
};

UCLASS()
class APlaneCharacter: public APlaneBase, public IPlayerLogicInterface
{
	GENERATED_BODY()

	DECLARE_DYNAMIC_MULTICAST_DELEGATE_OneParam(FOnPlaneBannerInfoReplicated, APlaneCharacter*, Plane);
	DECLARE_DYNAMIC_MULTICAST_DELEGATE_TwoParams(FOnPlaneRequestImgSuccess, APlaneCharacter*, Plane, UTexture2D*, Texture);

protected:
	UPROPERTY(BlueprintReadOnly)
	float DistanceToStartPoint;

	UPROPERTY(BlueprintReadOnly)
	float SimulatedDurationSinceAirLineStart;

	UPROPERTY(EditDefaultsOnly, BlueprintReadOnly)
	float TimeLineRate;

	UPROPERTY(EditDefaultsOnly, BlueprintReadOnly)
	FVector2D CameraSlowlyMoving;

	UPROPERTY(EditDefaultsOnly, BlueprintReadOnly)
	FVector2D CameraSlowlyPutDown;

	UPROPERTY(EditDefaultsOnly, BlueprintReadOnly, meta = (AllowClasses = "CurveFloat"))
	FSoftObjectPath SpringMoveSpeedScalePath;

	UPROPERTY(BlueprintReadOnly)
	UCurveFloat* SpringMoveSpeedScale;

	UPROPERTY(EditDefaultsOnly, ReplicatedUsing = OnRep_FixedRandomSeed)
	int32 FixedRandomSeed;

	UPROPERTY(EditDefaultsOnly, BlueprintReadOnly)
	float SpringArmLength_Escort;

	UPROPERTY(EditDefaultsOnly, BlueprintReadOnly)
	float SpringArmLength_NoEscort;

	UPROPERTY(EditDefaultsOnly, BlueprintReadOnly)
	FVector SpringArmLocationOffset;

	UPROPERTY(EditDefaultsOnly, BlueprintReadOnly)
	FVector SpringArmLocationOffset_NoEscort;

	UPROPERTY(EditDefaultsOnly, BlueprintReadOnly)
	FRotator SpringArmRotation_Escort;

	UPROPERTY(EditDefaultsOnly, BlueprintReadOnly)
	FRotator SpringArmRotation_NoEscort;

	UPROPERTY(EditDefaultsOnly, BlueprintReadOnly)
	FRotator CameraRotation_Escort;

	UPROPERTY(EditDefaultsOnly, BlueprintReadOnly)
	FRotator CameraRotation_NoEscort;

public:
	UPROPERTY(BlueprintReadOnly, ReplicatedUsing = OnRep_bHasEscortPlanes)
	uint8 bHasEscortPlanes : 1;

	UPROPERTY(ReplicatedUsing = OnRep_bFlyToJumpPoint)
	uint8 bFlyToJumpPoint : 1;

public:

	APlaneCharacter(const FObjectInitializer& ObjectInitializer = FObjectInitializer::Get());

	virtual void GetLifetimeReplicatedProps(TArray<FLifetimeProperty>& OutLifetimeProps) const override;
	virtual void PostInitializeComponents() override;
	virtual void BeginPlay() override;
	virtual void TickActor(float DeltaTime, enum ELevelTick TickType, FActorTickFunction& ThisTickFunction) override;
	virtual void CalcCamera(float DeltaTime, struct FMinimalViewInfo& OutResult) override;
	virtual void BecomeViewTarget(APlayerController* PC) override;
	virtual void PostNetReceiveVelocity(const FVector& NewVelocity) override;
	virtual void EndPlay(const EEndPlayReason::Type EndPlayReason) override;
	virtual bool IsNetRelevantFor(const AActor* RealViewer, const AActor* ViewTarget, const FVector& SrcLocation) const override;
	virtual void OnPlayerAttachedToThisActor(class ASTExtraCharacter* Player) override;
	virtual void PostNetReceiveLocationAndRotation() override;
public:
	UFUNCTION(BlueprintNativeEvent, BlueprintPure)
	UCameraComponent* GetPlaneCamera() const;

	UFUNCTION(BlueprintNativeEvent, BlueprintPure)
	USpringArmComponent* GetSpringArm() const;

	UFUNCTION(BlueprintNativeEvent, BlueprintCallable)
		void PlayPlaneSound();

	UFUNCTION()
	void FlyToJumpPoint();

	UFUNCTION(BlueprintImplementableEvent, meta = (DisplayName = "FlyToJumpPoint"))
	void ReceiveFlyToJumpPoint();

	UFUNCTION()
	void OnPlaneHitted();

	UFUNCTION()
	void OnAllPlayerLeavePlane();

	UFUNCTION()
	void ResetPlaneHitted(); 

	UFUNCTION()
	void OnPlaneEnterVolcanoZone();

	UFUNCTION()
	void OnPlaneLeaveVolcanoZone();

	UFUNCTION(BlueprintCallable)
	bool ShouldReplaceBanner() const { return PlaneBannerInfo.ShouldReplace(); };

	UFUNCTION(BlueprintCallable)
	UTexture2D* GetBannerTexture() const { return PlaneBannerInfo.LoadedTexture; };

	UFUNCTION(BlueprintNativeEvent)
	void OnPlayerForceJumpOffPlane(APlayerController* InController);

	UFUNCTION(BlueprintNativeEvent)
	void OnAllPlayerForceJumpOffPlane();

	UFUNCTION(BlueprintNativeEvent)
	bool ModifyPlaneData(FPlaneData& PlaneData);

	UFUNCTION(BlueprintNativeEvent)
	bool CheckForceJump();

	UFUNCTION(BlueprintNativeEvent)
	void OnPlaneClassDetermined(UWorld* World);

protected:
	void AsyncLoadAssets();

	void HandleAsyncLoadAssetsFinished();

	uint8 bEscortAssetsLoaded : 1;
	uint8 bLoadingEscortAssets : 1;

	UFUNCTION()
	void OnRep_IsInVolcanoZone();

	UFUNCTION()
	void OnRep_Hitted();

	UFUNCTION()
	void OnRequestImgSuccess(UTexture2D* Texture, const FString& RequestedURL);

	void AttachBanner();
	void RequestHttpImage(FString& RequestedURL);
	void ReplaceBannerTextureByParameter(FName TextureParameter, UTexture2D* Texture);

	UFUNCTION()
	void OnRep_PlaneBannerInfo();

private:
	UPROPERTY()
	class UFrontendHUD* FrontendHud = nullptr;

	TWeakObjectPtr<class UTeammateInPlaneWidget> InPlaneWidget;
	TWeakObjectPtr<class ASTExtraPlayerState> LocalPlayerState;
	bool bTeammatePassageInfoInitCompleted = false;;
	FTimerHandle WidgetTickHandle;

public:
	UPROPERTY()
	bool bUseCustomActorMove = false;
	UPROPERTY(EditDefaultsOnly, BlueprintReadWrite, Category = "Banner")
	USkeletalMesh* BannerMesh = nullptr;

	UPROPERTY(EditDefaultsOnly, BlueprintReadWrite, Category = "Banner")
	UAnimationAsset* BannerAnim = nullptr;

	UPROPERTY(EditDefaultsOnly, Category = "Banner")
	FName BannerAttachSocket;

	UPROPERTY()
	USkeletalMeshComponent* BannerComp;

	UPROPERTY()
	UMaterialInstanceDynamic* DynamicMaterial = nullptr;

	//飞机横幅数据, feishen, 20210817
	UPROPERTY(ReplicatedUsing = OnRep_PlaneBannerInfo)
	FPlaneBannerInfo PlaneBannerInfo;

	UPROPERTY(BlueprintReadWrite, ReplicatedUsing = OnRep_IsInVolcanoZone)
	bool bIsInVolcanoZone = false;

	UPROPERTY(BlueprintReadWrite, ReplicatedUsing = OnRep_Hitted)
	bool bHitted = false;

	UPROPERTY(EditAnywhere, BlueprintReadWrite, meta = (DisplayName = "飞机颠簸镜头动画"))
	TSoftObjectPtr<UCameraAnim> CameraAnim_Bouncing = nullptr;

	UPROPERTY(EditAnywhere, BlueprintReadWrite, meta = (DisplayName = "飞机被击中镜头动画"))
	TSoftObjectPtr<UCameraAnim> CameraAnim_Hitted = nullptr;

	UPROPERTY(EditAnywhere, BlueprintReadWrite, meta = (DisplayName = "飞机被击中特效"))
	class UParticleSystem* PS_Hitted = nullptr;

	FTimerHandle TimerHandle_ResetHitted;

	UPROPERTY(BlueprintAssignable)
	FOnPlaneRequestImgSuccess OnPlaneRequestImgSuccess;

	UPROPERTY(BlueprintAssignable)
	FOnPlaneBannerInfoReplicated OnPlaneBannerInfoReplicated;

	UPROPERTY(EditDefaultsOnly)
	float RandomProbability = 1.f;

	//////////////////////////////////////////////////////////////////////////
	// 护航表现
protected:
	UFUNCTION(BlueprintCallable, Category = AirForce)
	void OnEscortEnabledChanged( );

	UPROPERTY()
	TSubclassOf<class AAirForceEscort> AirForceEscortClass;

	UPROPERTY(EditDefaultsOnly, Category = AirForce, meta = (MetaClass = "AirForceEscort"))
	FSoftClassPath AirForceEscortClassPath;

	UPROPERTY(EditDefaultsOnly, Category = AirForce)
	FTransform AirForceEscortRelativeTransform;

	UPROPERTY()
	TSubclassOf<UCameraModifier> ShakeModifierClass;

	UPROPERTY(EditDefaultsOnly, Category = AirForce, meta = (MetaClass = "CameraModifier"))
	FSoftClassPath ShakeModifierClassPath;

	UPROPERTY()
	TSubclassOf<class UEscortSpawner> EscortSpawnerClass;

	UPROPERTY(EditDefaultsOnly, Category = AirForce, meta = (MetaClass = "EscortSpawner"))
	FSoftClassPath EscortSpawnerClassPath;

	UPROPERTY(BlueprintReadOnly, Category = AirForce)
	class UEscortSpawner* EscortSpawner;

	UFUNCTION(BlueprintPure)
	FORCEINLINE class AAirForceEscort* GetAirForceEscort() const { return AirForceEscortPtr.Get(); }

	UFUNCTION(BlueprintCallable)
	void AirForceEscort();

	UFUNCTION(BlueprintCallable)
	void AirForceFlyAway();

	UFUNCTION(BlueprintPure)
	bool HasValidEscortConfig() const;

	bool ShouldEscort() const;

	UFUNCTION(BlueprintImplementableEvent, meta = (DisplayName = "UpdateCameraTransform"))
	void ReceiveUpdateCameraTransform(float DeltaTime);

	UFUNCTION()
	void OnRep_FixedRandomSeed();

	UFUNCTION()
	void OnRep_bHasEscortPlanes();

	UFUNCTION()
	void OnRep_bFlyToJumpPoint();

	/*UI*/
	void CreateTeammateInPlaneWidget();
	void LoadInPlaneWidgetClassCompleted();
	UFUNCTION()
	void InitTeammatePassagersInfo();
	UFUNCTION()
	void OnLocalPlayerParachute(ExtraPlayerLiveState LiveState,class APlayerState * SelfPlayerState);
	void ClearInPlaneWidget();
	void RemoveWidgetBinding();
	void TickForInPlaneWidget();
public:
	UFUNCTION(BlueprintCallable)
	void PlayCameraShake();

protected:
	void UpdateEscortPlane();
	void ClearEscortPlanes();
	TWeakObjectPtr<AAirForceEscort> AirForceEscortPtr;
private:
	UFUNCTION()
	bool ShouldCreateInPlaneWidget();

public:
	UFUNCTION(BlueprintCallable)
	bool GetHiddenCharacterInPlane() { return bHiddenCharacterInPlane; }

	UFUNCTION(BlueprintCallable)
	void SetHiddenCharacterInPlane(bool bHide);

	UFUNCTION(BlueprintNativeEvent)
	void OnRep_HiddenCharacterInPlane();

protected:
	UPROPERTY(BlueprintReadOnly, ReplicatedUsing = OnRep_HiddenCharacterInPlane)
	bool bHiddenCharacterInPlane = true;

	virtual bool IfNeedReplace_Implementation() override;
};
