#include "ShadowTrackerExtra.h"
#include "AreaTagManagerActor.h"
#include "UObject/CustomMacros.h"
#include "UAEDataTable.h"
#include "AreaTagFunc.h"
#include "Kismet/GameplayStatics.h"

AAreaTagManagerActor::AAreaTagManagerActor()
{

}

void AAreaTagManagerActor::BeginPlay()
{

}

#pragma region UGLY_DEFINE

#define DO_FINDSTRUCT_UGLY_THING \
if (Active_StructArray.Num() > 0)\
{\
	for (int tempi : Active_StructArray)\
	{\
	 if (FindStructArray.IsValidIndex(tempi))\
		{\
			const auto& FindStruct = FindStructArray[tempi];\
			DoItByFindStruct(FindStruct);\
		}\
	}\
}\
else\
{\
	for (auto& FindStruct : FindStructArray)\
	{\
	 DoItByFindStruct(FindStruct);\
	}\
}\

#pragma endregion UGLY_DEFINE

void AAreaTagManagerActor::GetVolumeAreaTagDetail()
{
	AllSpotDetailByTag.Empty();
	auto DoItByFindStruct = [this](const FAreaTagFindStruct& _FindStruct)->void{
		auto Ret = GetFindStructCompInVolume(_FindStruct);
		FAreaTagManagerContext Context;
		Context.FindSceneComp = Ret;
		Context.OwnerActor = this;
		Context.FindTagName = this->FindTag;
		if (_FindStruct.Type == EAreaTagPossessorType::PossessorType_Spot)
		{
			AreaTagPossessSelector.SpotPossessor.CollectSpotTagByComp(Context,AllSpotDetailByTag);
		}
	};
	
	DO_FINDSTRUCT_UGLY_THING;
	BP_GetVolumeAreaTagDetail();
}

void AAreaTagManagerActor::AddVolumeAreaTag()
{
	auto DoItByFindStruct = [this](const FAreaTagFindStruct& _FindStruct)->void{
		auto Ret = GetFindStructCompInVolume(_FindStruct);
		FAreaTagManagerContext Context;
		Context.FindSceneComp = Ret;
		Context.OwnerActor = this;
		Context.TagNames = AddTags;
		if (_FindStruct.Type == EAreaTagPossessorType::PossessorType_Spot)
		{
			AreaTagPossessSelector.SpotPossessor.AddSpotTagByComp(Context);
		}
	};
	DO_FINDSTRUCT_UGLY_THING;
	BP_AddVolumeAreaTag();
}

void AAreaTagManagerActor::ReplaceVolumeAreaTag()
{
	auto DoItByFindStruct = [this](const FAreaTagFindStruct& _FindStruct)->void{
		auto Ret = GetFindStructCompInVolume(_FindStruct);
		FAreaTagManagerContext Context;
		Context.FindSceneComp = Ret;
		Context.OwnerActor = this;
		Context.TagNames = ReplaceTags;
		if (_FindStruct.Type == EAreaTagPossessorType::PossessorType_Spot)
		{
			AreaTagPossessSelector.SpotPossessor.ReplaceSpotTagByComp(Context);
		}
	};
	DO_FINDSTRUCT_UGLY_THING;
	BP_ReplaceVolumeAreaTag();
}

void AAreaTagManagerActor::RemoveVolumeAreaTag()
{
	auto DoItByFindStruct = [this](const FAreaTagFindStruct& _FindStruct)->void{
		auto Ret = GetFindStructCompInVolume(_FindStruct);
		FAreaTagManagerContext Context;
		Context.FindSceneComp = Ret;
		Context.OwnerActor = this;
		Context.TagNames = RemoveTags;
		if (_FindStruct.Type == EAreaTagPossessorType::PossessorType_Spot)
		{
			AreaTagPossessSelector.SpotPossessor.RemoveSpotTagByComp(Context);
		}
	};
	DO_FINDSTRUCT_UGLY_THING;
	BP_RemoveVolumeAreaTag();
}



void AAreaTagManagerActor::CheckAndAddVolumeMustHaveTag()
{
	auto DoItByFindStruct = [this](const FAreaTagFindStruct& _FindStruct)->void{
		auto Ret = GetFindStructCompInVolume(_FindStruct);
		FAreaTagManagerContext Context;
		Context.FindSceneComp = Ret;
		Context.OwnerActor = this;
		
		Context.TagNames.Add(MustHaveTag);
		if (_FindStruct.Type == EAreaTagPossessorType::PossessorType_Spot)
		{
			AreaTagPossessSelector.SpotPossessor.AddSpotTagByComp(Context);
		}
	};
	DO_FINDSTRUCT_UGLY_THING;
}

TArray<USceneComponent*> AAreaTagManagerActor::GetFindStructCompInVolume(FAreaTagFindStruct FindStruct)
{
	auto tempRet = GetAllCompByFindStruct(FindStruct);
	auto Ret = GetCompInVolume(tempRet);
	return Ret;
}

TArray<USceneComponent*> AAreaTagManagerActor::GetAllCompByFindStruct(FAreaTagFindStruct FindStruct)
{
	const auto& _ActorClass = FindStruct.ActorClass;
	TArray<AActor*> OutActors;
	UGameplayStatics::GetAllActorsOfClass(this,_ActorClass,OutActors);
	TArray<USceneComponent*> OutComp;
	for (AActor* actor : OutActors)
	{
		if (actor)
		{
			for (auto compclass : FindStruct.FindComponent)
			{
				TArray<UActorComponent*> tempComps = actor->GetComponentsByClass(compclass);
				for (UActorComponent* temp : tempComps)
				{
					if (USceneComponent* _sceneComp = Cast<USceneComponent>(temp))
					{
						OutComp.Add(_sceneComp);
					}
				}
			}
		}

	}
	return OutComp;
}

TArray<USceneComponent*> AAreaTagManagerActor::GetCompInVolume(TArray<USceneComponent*> AllComp)
{
	TArray<USceneComponent*> OutComp;
	for (USceneComponent* Comp : AllComp)
	{
		if (Comp)
		{
			for (TSoftObjectPtr<AAreaTagVolume> volumeptr : FindVolumes)
			{
				if (volumeptr.Get())
				{
					AAreaTagVolume* tempVolume = volumeptr.Get();
					bool bis = tempVolume->BPCheckPointInVolume(Comp->GetComponentLocation());
					if (bis)
					{
						OutComp.Add(Comp);
						break;
					}
				}
			}
		}

	}
	return OutComp;
}

void AAreaTagManagerActor::GetWorldSpotDetail()
{
	WorldSpotDetail.Empty();
	auto DoItByFindStruct = [this](const FAreaTagFindStruct& _FindStruct)->void{
		auto Ret = GetAllCompByFindStruct(_FindStruct);
		FAreaTagManagerContext Context;
		Context.FindSceneComp = Ret;
		Context.OwnerActor = this;
		Context.FindTagName = this->FindTag;
		
		if (_FindStruct.Type == EAreaTagPossessorType::PossessorType_Spot)
		{
			AreaTagPossessSelector.SpotPossessor.CollectWorldSpotDetail(Context,this->WorldSpotDetail);
		}
	};
	DO_FINDSTRUCT_UGLY_THING;

}


void AAreaTagManagerActor::OutPutWorldSpotDetailToCSV()
{
	OutPutWorldSpotDetailToCSV_BySpotDetail(WorldSpotDetail);
}



void AAreaTagManagerActor::OutPutWorldSpotDetailToCSV_BySpotDetail(const TArray<FWorldAreaSpotDetail>& Details)
{
#if WITH_EDITOR || WITH_UGC_EDITOR

#define AREAMANAGER_SPOT_TAG(x) \
	if (detail.Tags.Num() > 0 && detail.Tags.IsValidIndex(x))\
	{\
		XPASTE(tempDetail.Tag_,x) =  detail.Tags[x];\
	};\

	int ID = 0;
	for (auto detail : Details)
	{
		ID++;
		FAreaSpotWorldDetailOutPut tempDetail;
		tempDetail.ID = ID;
		tempDetail.SpotLocation = detail.Loc;
		tempDetail.FullName = FName(*detail.CompSpotName);
		tempDetail.BisHaveParent = detail.BisHaveGroup;
		UEnum* pEnum = FindObject<UEnum>(ANY_PACKAGE, TEXT("ESpotType"));
		FString SpotEnumName = pEnum->GetNameStringByIndex(static_cast<uint8>(detail.SpotType));
		UEnum* pEnum2 = FindObject<UEnum>(ANY_PACKAGE, TEXT("ESpotGroupType"));
		FString SpotEnumName2 = pEnum2->GetNameStringByIndex(static_cast<uint8>(detail.SpotGroupType));
		tempDetail.MySpotType = SpotEnumName;
		tempDetail.ParentGroupType = SpotEnumName2;
		AREAMANAGER_SPOT_TAG(0);
		AREAMANAGER_SPOT_TAG(1);
		AREAMANAGER_SPOT_TAG(2);
		AREAMANAGER_SPOT_TAG(3);
		AREAMANAGER_SPOT_TAG(4);
		AREAMANAGER_SPOT_TAG(5);
		AREAMANAGER_SPOT_TAG(6);
		AREAMANAGER_SPOT_TAG(7);
		AREAMANAGER_SPOT_TAG(8);
		AREAMANAGER_SPOT_TAG(9);
		FName RowName = FName(*(FString::FromInt(ID)));
		SpotWorldDetailTable->AddRow(RowName,tempDetail);

	}
	FString FileStrTemp = SpotWorldDetailTable->GetTableAsString();
	FString FileStr2 = FileStrTemp.Replace(TEXT(",Y"),TEXT(" Y"));
	FString FileStr = FileStr2.Replace(TEXT(",Z"),TEXT(" Z"));
	FString FileTempName = TEXT("OutPut_Type1.csv");
	if (GetWorld())
	{
		UWorld* World = GetWorld();
		AWorldSettings* WorldSetting = World->GetWorldSettings();
		if (WorldSetting)
		{
			FileTempName = WorldSetting->DefaultGameMode->GetName();
			FileTempName.Append(TEXT("_Type1.csv"));
		}

		
	}


	//FString FileName = FPaths::ProjectDir() / FileTempName;

	FString FilePath = FPaths::Combine(*FPaths::ProjectSavedDir(), TEXT("SpotOutPutFolder"), FileTempName);

	if (FFileHelper::SaveStringToFile(FileStr,*FilePath))
	{
		
	}
	else
	{
		FPlatformMisc::MessageBoxExt(EAppMsgType::Ok,*FilePath, TEXT("有文件正在打开无法保存！"));
	}

#undef  AREAMANAGER_SPOT_TAG

#endif
}

void AAreaTagManagerActor::DoSpotTagCSV()
{
#if WITH_EDITOR || WITH_UGC_EDITOR	
	CSV_AllSpotDetailByTag.Empty();
	auto DoItByFindStruct = [this](const FAreaTagFindStruct& _FindStruct)->void{
		auto Ret = GetAllCompByFindStruct(_FindStruct);

		FAreaTagManagerContext Context;
		Context.FindSceneComp = Ret;
		Context.OwnerActor = this;
	//	Context.TagNames = RemoveTags;
		if (_FindStruct.Type == EAreaTagPossessorType::PossessorType_Spot)
		{
			AreaTagPossessSelector.SpotPossessor.CollectSpotTagByComp(Context,CSV_AllSpotDetailByTag);
		}

	};
	DO_FINDSTRUCT_UGLY_THING;

	DoSpotStructDataToCSV(CSV_AllSpotDetailByTag);
#endif
}

#define AREATAGSPOTNO_PARENT_STR "*"

void AAreaTagManagerActor::DoSpotStructDataToCSV(TMap<FName, FAreaTagDataDetail> InSpotDetailByTag)
{


	// First Time , Find ALL SpotGroup And Spot Type combination
	TSet<FString> XlsSpotType;

	TMap<FString,FAreaTagSpotNameToNumStruct> TagNameToSpotDetail;
	for (auto Detail : InSpotDetailByTag)
	{
		FString TagName =	(Detail.Key).ToString();
		FAreaTagDataDetail& SpotDetail = Detail.Value;
		FAreaTagSpotNameToNumStruct& tempTagDetail = TagNameToSpotDetail.FindOrAdd(TagName);
		int32& _TotalNum = tempTagDetail.TotalNum;


		// 统计下有爹的
		auto& HaveGroupDetail = SpotDetail.GroupSpotDetail;
		for (auto elem : HaveGroupDetail)
		{
			TEnumAsByte<ESpotGroupType> GroupType = elem.Key;
			FString GroupTypeStr = UTagAreaFunctionLibrary::GetCSVNeedNameByGroupSpotType(GroupType);
			auto& HaveGroupDetail =  elem.Value.SpotTypeDetail;
			for (auto _SubElem : HaveGroupDetail)
			{
				TEnumAsByte<ESpotType> _tempSpotType =	_SubElem.Key;
				int32 Num = _SubElem.Value;
				FString SpotTypeStr = UTagAreaFunctionLibrary::GetCSVNeedNameBySpotType(_tempSpotType);
				FString RetTypeStr = GroupTypeStr + SpotTypeStr;
				XlsSpotType.Add(RetTypeStr);
				auto& tempNum = tempTagDetail.SpotTypeNameToNum.FindOrAdd(RetTypeStr);
				tempNum += Num;
				_TotalNum += Num;	
			}
			
		}



		// 统计下没爹的
		auto& NoGroupDetail = SpotDetail.NoGroupSpotDetail;
		for (auto elem : NoGroupDetail.SpotTypeDetail)
		{
			TEnumAsByte<ESpotType> SpotType = elem.Key;
			int32 Num = elem.Value;
			FString SpotTypeStr = UTagAreaFunctionLibrary::GetCSVNeedNameBySpotType(SpotType);
			FString RetTypeStr = AREATAGSPOTNO_PARENT_STR + SpotTypeStr;
			XlsSpotType.Add(RetTypeStr);
			auto& tempNum = tempTagDetail.SpotTypeNameToNum.FindOrAdd(RetTypeStr);
			tempNum += Num;
			_TotalNum += Num;
		}
	}
	OutPutSpotDetailByTagToCSV_BySpotDetailByTag(TagNameToSpotDetail,XlsSpotType);
}

void AAreaTagManagerActor::OutPutSpotDetailByTagToCSV_BySpotDetailByTag(const TMap<FString, FAreaTagSpotNameToNumStruct>& SpotDataMap, TSet<FString>& SpotNameSet)
{

	FString NameLine = "ID,AreaTag,TotalNum";

	SpotNameSet.Sort([](const FString& A, const FString& B){
		if (A.Len() != B.Len())
		{
			return A.Len() < B.Len();
		}
		else
		{
			return A < B;
		}
	});

	for (auto str : SpotNameSet)
	{
		NameLine.Append(",");
		NameLine.Append(str);
	}
	TArray<FString> Rets;
	Rets.Add(NameLine);

	int32 ID = 0;
	for (auto elem : SpotDataMap)
	{
		ID++;

		FString TagStr = FString::FromInt(ID);
		FString TagName = elem.Key;
		TagStr.Append(",");
		TagStr.Append(TagName);

		TagStr.Append(",");
		TagStr.Append(FString::FromInt(elem.Value.TotalNum));
		
		auto& SpotTypeMap = elem.Value.SpotTypeNameToNum;
		for (const auto Type :  SpotNameSet)
		{
			const FString& SpotTypeName = Type;
			TagStr.Append(",");
			if (SpotTypeMap.Contains(Type))
			{
				TagStr.Append(FString::FromInt(SpotTypeMap[Type]));
			}
			else
			{
				TagStr.Append("0");
			}
		}
		Rets.Add(TagStr);
	}


	FString FileTempName = TEXT("OutPut_Type2.csv");
	if (GetWorld())
	{
		UWorld* World = GetWorld();
		AWorldSettings* WorldSetting = World->GetWorldSettings();
		if (WorldSetting)
		{
			FileTempName = WorldSetting->DefaultGameMode->GetName();
			FileTempName.Append(TEXT("_Type2.csv"));
		}

		
	}
	FString FilePath = FPaths::Combine(*FPaths::ProjectSavedDir(), TEXT("SpotOutPutFolder"), FileTempName);
	if (FFileHelper::SaveStringArrayToFile(Rets, *FilePath))
	{

	}
	else
	{
		FPlatformMisc::MessageBoxExt(EAppMsgType::Ok,*FilePath, TEXT("有文件正在打开无法保存！"));
	}
}

