// Fill out your copyright notice in the Description page of Project Settings.

#include "ShadowTrackerExtra.h"
#include "GameModeActor.h"
#include "Event/LevelEventCenter.h"
#include "SubSystem/GMComponentManager.h"
#include "Misc/Paths.h"

#if UE_EDITOR
#include "Interfaces/ITargetPlatform.h"
#endif
#include "UAEGameInstance.h"
#include "Character/STExtraBaseCharacter.h"
#include "LevelInstance/LevelInstanceMgr.h"


#ifndef GameModeActor_ISCLIENTORSERVER
#define GameModeActor_ISCLIENTORSERVER ((GetNetMode() == NM_DedicatedServer) ? TEXT("S") : TEXT("C"))
#endif 


DEFINE_LOG_CATEGORY(LogGameModeActor)

AGameModeActor::AGameModeActor() 
	:Super()
{
	PrimaryActorTick.bCanEverTick = true;
	PrimaryActorTick.bAllowTickOnDedicatedServer = true;
	SetReplicates(true);

	GameModeActorStateActive = CreateDefaultSubobject<UGameModeActorState>(TEXT("GameModeActorStateActive"));
	GameModeActorStateReady = CreateDefaultSubobject<UGameModeActorState>(TEXT("GameModeActorStateReady"));
	GameModeActorStateFighting = CreateDefaultSubobject<UGameModeActorState>(TEXT("GameModeActorStateFighting"));
	GameModeActorStateFinished = CreateDefaultSubobject<UGameModeActorState>(TEXT("GameModeActorStateFinished"));
	CurTickFreq = 0.f;
}

void AGameModeActor::GetLifetimeReplicatedProps(TArray<FLifetimeProperty> & OutLifetimeProps) const
{
	Super::GetLifetimeReplicatedProps(OutLifetimeProps);

	DOREPLIFETIME(AGameModeActor, StateInfo);
	DOREPLIFETIME(AGameModeActor, ForceExitState);
	DOREPLIFETIME_CONDITION(AGameModeActor, ReplayPawnList, COND_ReplayOnly);
}

void AGameModeActor::BeginPlay()
{
	Super::BeginPlay();

	SpawnActorList.Empty();
	UE_LOG(LogGameModeActor, Log, TEXT("[%s][%s]BeginPlay, Loc=%s, Rot=%s"), GameModeActor_ISCLIENTORSERVER, *GetName(), *GetActorLocation().ToStringShort(), *GetActorRotation().ToStringShort());
}

void AGameModeActor::EndPlay(const EEndPlayReason::Type EndPlayReason)
{
	Super::EndPlay(EndPlayReason);

	if (GetNetMode() == NM_DedicatedServer)
	{
		for (int32 i = 0; i < SpawnActorList.Num(); i++)
		{
			AActor* Actor = SpawnActorList[i];
			if (Actor)
			{
				UE_LOG(LogGameModeActor, Log, TEXT("[%s][%s]EndPlay, Destroy Actor, Actor=%s, Loc=%s, Rot=%s"), GameModeActor_ISCLIENTORSERVER,
				*GetName(), *Actor->GetName(),
				*Actor->GetActorLocation().ToStringShort(), *Actor->GetActorRotation().ToStringShort());
				Actor->Destroy();
			}
		}

		if (!IsMultiZoneActor && UGMComponentManager::GetInstance())
		{
			UGMComponentManager::GetInstance()->InvokeFunction(ESubSystemType::ESS_GameModeActorMgr, TEXT("NotifyDestoryGameModeActor"), this);
		}
	}

	if (bUseLevelInstance)
	{
		DestructLevelInstances();
	}
}

void AGameModeActor::AddDynamicSpawnedActor(AActor* Actor)
{
	if (GetNetMode() == NM_DedicatedServer)
	{
		if (Actor)
		{
			SpawnActorList.Add(Actor);
			UE_LOG(LogGameModeActor, Log, TEXT("[%s][%s]AddDynamicSpawnedActor, Add Spawned Actor to destroy, Actor=%s, Loc=%s, Rot=%s"), GameModeActor_ISCLIENTORSERVER,
			*GetName(), *Actor->GetName(),
				*Actor->GetActorLocation().ToStringShort(), *Actor->GetActorRotation().ToStringShort());
		}
	}
}

int32 AGameModeActor::GetNumPlayers() const
{
	return PCList.Num();
}

void AGameModeActor::ConstructLevelInstances()
{
	UE_LOG(LogGameModeActor, Log, TEXT("[%s][%s] ConstructLevelInstances"), GameModeActor_ISCLIENTORSERVER, *GetName());
	
	ALevelInstanceMgr* LevelInstanceMgr = ALevelInstanceMgr::Get(this);
	if (!LevelInstanceMgr)
	{
		UE_LOG(LogGameModeActor, Error, TEXT("ConstructLevelInstances LevelInstanceMgr is null!"));
		return;
	}

	LevelInstanceInfos.Empty();
	
	InstancedLevels.Empty();

	for (int32 i = 0; i < LevelInstancePackageNames.Num(); i++)
	{
		FLevelInstanceInfo LevelInfo(LevelInstancePackageNames[i], ConvertToLevelInstanceName(LevelInstancePackageNames[i]));
		LevelInfo.Trans = GetActorTransform();
		LevelInstanceInfos.Add(LevelInfo);
		UE_LOG(LogGameModeActor, Log, TEXT("ConstructLevelInstances LevelInstanceInfo [%s][%s][%s]"), *LevelInfo.LevelName, *LevelInfo.UniqueName, *LevelInfo.Trans.ToString());
	}

	LevelInstanceMgr->ToggleLevelInstances(LevelInstanceInfos, true, true);

	for (int32 i = 0; i < LevelInstanceInfos.Num(); i++)
	{
#if UE5_OR_LATER
		if (ULevelStreamingDynamic* LevelInstance = LevelInstanceMgr->FindLevelInstance(LevelInstanceInfos[i]))
#else
		if (ULevelStreamingKismet* LevelInstance = LevelInstanceMgr->FindLevelInstance(LevelInstanceInfos[i]))
#endif
		{
			InstancedLevels.AddUnique(LevelInstance->GetLoadedLevel());
		}
	}
}

void AGameModeActor::DestructLevelInstances()
{
	UE_LOG(LogGameModeActor, Log, TEXT("[%s][%s] DestructLevelInstances"), GameModeActor_ISCLIENTORSERVER, *GetName());
	
	ALevelInstanceMgr* LevelInstanceMgr = ALevelInstanceMgr::Get(this);
	if (!LevelInstanceMgr)
	{
		UE_LOG(LogGameModeActor, Error, TEXT("DestructLevelInstances LevelInstanceMgr is null!"));
		return;
	}

	LevelInstanceMgr->ToggleLevelInstances(LevelInstanceInfos, false);

	LevelInstanceInfos.Empty();
	
	InstancedLevels.Empty();
}

FString AGameModeActor::ConvertToLevelInstanceName(const FString& PackageName) const
{
	const FVector Location = GetActorLocation();
	const int32 X_Kilometers = FMath::RoundToInt(Location.X / 1000.0f);
	const int32 Y_Kilometers = FMath::RoundToInt(Location.Y / 1000.0f);
	const FString LevelInstanceName = FString::Printf(TEXT("%s_%dk_%dk"), *PackageName, X_Kilometers, Y_Kilometers);
	return LevelInstanceName;
}

void AGameModeActor::GotoActiveState()
{
	UE_LOG(LogGameModeActor, Log, TEXT("[%s][%s]GotoActiveState"), GameModeActor_ISCLIENTORSERVER, *GetName());
	if (GetNetMode() == NM_DedicatedServer)
	{
		StatePtr = GameModeActorStateActive;
		if (StatePtr)
		{
			StatePtr->Enter();
			StateInfo.State = EGameModeActorState::Active;
			StateInfo.EnterTime = StatePtr->GetEnterStateTime();
		}
	}
}

void AGameModeActor::GotoReadyState()
{
	UE_LOG(LogGameModeActor, Log, TEXT("[%s][%s]GotoReadyState"), GameModeActor_ISCLIENTORSERVER, *GetName());
	if (GetNetMode() == NM_DedicatedServer)
	{
		if (StatePtr)
		{
			StatePtr->Exit();
			if (!StatePtr->IsTimeout())
			{
				ForceExitState = StateInfo.State;
				UE_LOG(LogGameModeActor, Log, TEXT("[%s][%s]GotoReadyState, ForceExit, from %d"), GameModeActor_ISCLIENTORSERVER, *GetName(), (int32)ForceExitState);
			}
			else
			{
				UE_LOG(LogGameModeActor, Log, TEXT("[%s][%s]GotoReadyState, from %d"), GameModeActor_ISCLIENTORSERVER, *GetName(), (int32)StateInfo.State);
			}
			StatePtr = nullptr;
		}

		StatePtr = GameModeActorStateReady;
		if (StatePtr)
		{
			StatePtr->Enter();
			StateInfo.State = EGameModeActorState::Ready;
			StateInfo.EnterTime = StatePtr->GetEnterStateTime();
		}
	}
}

void AGameModeActor::GotoFightingState()
{
	UE_LOG(LogGameModeActor, Log, TEXT("[%s][%s]GotoFightingState"), GameModeActor_ISCLIENTORSERVER,*GetName());
	if (GetNetMode() == NM_DedicatedServer)
	{
		if (StatePtr == GameModeActorStateFighting)
		{
			return;
		}
		if (StatePtr)
		{
			StatePtr->Exit();
			if (!StatePtr->IsTimeout())
			{
				ForceExitState = StateInfo.State;
				UE_LOG(LogGameModeActor, Log, TEXT("[%s][%s]GotoFightingState, ForceExit, from %d"), GameModeActor_ISCLIENTORSERVER,*GetName(), (int32)ForceExitState);
			}
			else
			{
				UE_LOG(LogGameModeActor, Log, TEXT("[%s][%s]GotoFightingState, from %d"), *GetName(), GameModeActor_ISCLIENTORSERVER, (int32)StateInfo.State);
			}
			StatePtr = nullptr;
		}

		StatePtr = GameModeActorStateFighting;
		if (StatePtr)
		{
			StatePtr->Enter();
			StateInfo.State = EGameModeActorState::Fighting;
			StateInfo.EnterTime = StatePtr->GetEnterStateTime();
		}
	}
}

void AGameModeActor::GotoFinishedState()
{
	UE_LOG(LogGameModeActor, Log, TEXT("[%s][%s]GotoFinishedState"), GameModeActor_ISCLIENTORSERVER, *GetName());
	if (GetNetMode() == NM_DedicatedServer)
	{
		if (StatePtr)
		{
			StatePtr->Exit();
			if (!StatePtr->IsTimeout())
			{
				ForceExitState = StateInfo.State;
				UE_LOG(LogGameModeActor, Log, TEXT("[%s][%s]GotoFinishedState, ForceExit, from %d"), GameModeActor_ISCLIENTORSERVER, *GetName(), (int32)ForceExitState);
			}
			else
			{
				UE_LOG(LogGameModeActor, Log, TEXT("[%s][%s]GotoFinishedState, from %d"), GameModeActor_ISCLIENTORSERVER, *GetName(), (int32)StateInfo.State);
			}
			StatePtr = nullptr;
		}

		StatePtr = GameModeActorStateFinished;
		if (StatePtr)
		{
			StatePtr->Enter();
			StateInfo.State = EGameModeActorState::Finished;
			StateInfo.EnterTime = StatePtr->GetEnterStateTime();
		}
	}
}

void AGameModeActor::Tick(float DeltaTime)
{
	Super::Tick(DeltaTime);

	if (StatePtr && (StateInfo.State != EGameModeActorState::Invalid))
	{
		if (StatePtr)
		{
			PassedTime = StatePtr->GetStatePassedTime();
			StatePtr->Execute(DeltaTime);
		}

		if (GetNetMode() == NM_DedicatedServer)
		{
			if (StateInfo.State == EGameModeActorState::Finished)
			{
				if (StatePtr->IsTimeout())
				{
					StatePtr->Exit();
					StatePtr = nullptr;
				}
			}
			else
			{
				if (StatePtr->IsTimeout())
				{
					GotoNextState();
				}
			}
		}
		else
		{
			if ((StateInfo.State == EGameModeActorState::Finished) && StatePtr->IsTimeout())
			{
				StatePtr->Exit();
				StatePtr = nullptr;
			}
		}
	}

	if (GetNetMode() == NM_DedicatedServer)
	{
		if (SpawnActorInfoList.Num() > 0)
		{
			TickSpawnActor();
		}
	}
	
	CurTickFreq += DeltaTime;
	if (CurTickFreq < TickFreq)
	{
		return;
	}
	CurTickFreq -= TickFreq;

	if (GetNetMode() != NM_DedicatedServer)
	{
		if (!LocalPlayerPawnLoaded)
		{
			TickLocalPlayerPawnLoaded();
		}

		if (!IsClientLoadingCompleted)
		{
			CheckClientLoadingCompleted();
		}
	}

	LuaTick();
}

void AGameModeActor::GotoNextState()
{
	if (GetNetMode() != NM_DedicatedServer)
	{
		UE_LOG(LogGameModeActor, Error, TEXT("[%s][%s]GotoNextState, GetNetMode() != NM_DedicatedServer"), GameModeActor_ISCLIENTORSERVER, *GetName());
		return;
	}

	if (StateInfo.State == EGameModeActorState::Invalid)
	{
		UE_LOG(LogGameModeActor, Error, TEXT("[%s][%s]GotoNextState, StateInfo not inited"), GameModeActor_ISCLIENTORSERVER, *GetName());
		return;
	}

	if (StatePtr)
	{
		StatePtr->Exit();
		if (!StatePtr->IsTimeout())
		{
			ForceExitState = StateInfo.State;
			UE_LOG(LogGameModeActor, Log, TEXT("[%s][%s]GotoNextState, ForceExit, from %d"), GameModeActor_ISCLIENTORSERVER, *GetName(), (int32)ForceExitState);
		}
		else
		{
			UE_LOG(LogGameModeActor, Log, TEXT("[%s][%s]GotoNextState, from %d"), GameModeActor_ISCLIENTORSERVER, *GetName(), (int32)StateInfo.State);
		}
		StatePtr = nullptr;
	}

	switch (StateInfo.State)
	{
	case EGameModeActorState::Active:
		StatePtr = GameModeActorStateReady;
		StateInfo.State = EGameModeActorState::Ready;
		break;
	case EGameModeActorState::Ready:
		StatePtr = GameModeActorStateFighting;
		StateInfo.State = EGameModeActorState::Fighting;
		break;
	case EGameModeActorState::Fighting:
		StatePtr = GameModeActorStateFinished;
		StateInfo.State = EGameModeActorState::Finished;
		break;
	default:
		UE_LOG(LogGameModeActor, Log, TEXT("[%s][%s]GotoNextState, to %d"), GameModeActor_ISCLIENTORSERVER, *GetName(), (int32)StateInfo.State);
		StatePtr = nullptr;
		break;
	}

	if (StatePtr)
	{
		StatePtr->Enter();
		StateInfo.EnterTime = StatePtr->GetEnterStateTime();
		StateTime = StatePtr->GetStateTime();
		UE_LOG(LogGameModeActor, Log, TEXT("[%s][%s]GotoNextState, to %d"), GameModeActor_ISCLIENTORSERVER, *GetName(), (int32)StateInfo.State);
	}
}

void AGameModeActor::GotoNextScene()
{
	if (!IsMultiZoneActor && GetNetMode() == NM_DedicatedServer)
	{
		UE_LOG(LogGameModeActor, Log, TEXT("[%s][%s]GotoNextScene, TeamID=%d"), GameModeActor_ISCLIENTORSERVER, *GetName(), TeamID);
		bool Success = UGMComponentManager::GetInstance()->InvokeFunctionWithReturn<bool>(ESubSystemType::ESS_GameModeActorMgr, TEXT("GoToNextScene"), TeamID);
		if (!Success)
		{
			UE_LOG(LogGameModeActor, Log, TEXT("[%s][%s]GotoNextScene failed, TeamID=%d"), GameModeActor_ISCLIENTORSERVER, *GetName(), TeamID);
		}
	}
}

bool AGameModeActor::NeedGotoNextScene()
{
	bool Need = false;
	if (!IsMultiZoneActor && GetNetMode() == NM_DedicatedServer)
	{
		UE_LOG(LogGameModeActor, Log, TEXT("[%s][%s]NeedGotoNextScene, TeamID=%d"), GameModeActor_ISCLIENTORSERVER, *GetName(), TeamID);
		Need = UGMComponentManager::GetInstance()->InvokeFunctionWithReturn<bool>(ESubSystemType::ESS_GameModeActorMgr, TEXT("NeedGotoNextScene"), TeamID);
		if (!Need)
		{
			UE_LOG(LogGameModeActor, Log, TEXT("[%s][%s]NeedGotoNextScene, TeamID=%d, Need==false"), GameModeActor_ISCLIENTORSERVER, *GetName(), TeamID);
		}
	}
	return Need;
}

int32 AGameModeActor::GetNextSceneIndex()
{
	int32 NextIndex = -1;
	if (!IsMultiZoneActor && GetNetMode() == NM_DedicatedServer)
	{
		UE_LOG(LogGameModeActor, Log, TEXT("[%s][%s]GetNextSceneIndex, TeamID=%d"), GameModeActor_ISCLIENTORSERVER, *GetName(), TeamID);
		NextIndex = UGMComponentManager::GetInstance()->InvokeFunctionWithReturn<int32>(ESubSystemType::ESS_GameModeActorMgr, TEXT("GetNextSceneIndex"), TeamID);
		if (NextIndex < 0)
		{
			UE_LOG(LogGameModeActor, Log, TEXT("[%s][%s]GetNextSceneIndex, TeamID=%d, Invalid NextIndex"), GameModeActor_ISCLIENTORSERVER, *GetName(), TeamID);
		}
	}
	return NextIndex;
}

void AGameModeActor::OnRep_State()
{
	if (LocalState == StateInfo.State)
	{
		return;
	}

	if (StatePtr)
	{
		StatePtr->Exit();
		StatePtr = nullptr;
	}

	UE_LOG(LogGameModeActor, Log, TEXT("[%s][%s]OnRep_State, LocalState=%d, State=%d, EnterTime=%d"), GameModeActor_ISCLIENTORSERVER, *GetName(),
	(int32)LocalState, (int32)StateInfo.State, StateInfo.EnterTime);
	LocalState = StateInfo.State;
	switch (LocalState)
	{
	case EGameModeActorState::Active:
		StatePtr = GameModeActorStateActive;
		break;
	case EGameModeActorState::Ready:
		StatePtr = GameModeActorStateReady;
		break;
	case EGameModeActorState::Fighting:
		StatePtr = GameModeActorStateFighting;
		break;
	case EGameModeActorState::Finished:
		StatePtr = GameModeActorStateFinished;
		break;
	default:
		StatePtr = nullptr;
		break;
	}

	if (StatePtr)
	{
		StatePtr->Enter();
		if (StatePtr->GetEnterStateTime() != StateInfo.EnterTime)
		{
			StatePtr->RegulateStateEnterTime(StateInfo.EnterTime);
			UE_LOG(LogGameModeActor, Log, TEXT("[%s][%s]RegulateStateEnterTime"), GameModeActor_ISCLIENTORSERVER, *GetName());
		}
		StateTime = StatePtr->GetStateTime();
	}
}

void AGameModeActor::OnRep_ForceExit()
{
	if (StatePtr && LocalState == ForceExitState)
	{
		StatePtr->Exit();
		StatePtr = nullptr;
		UE_LOG(LogGameModeActor, Log, TEXT("[%s][%s]OnRep_ForceExit, ForceExitState=%d"), GameModeActor_ISCLIENTORSERVER, *GetName(), (int32)ForceExitState);
	}
}

void AGameModeActor::Login(ASTExtraPlayerController* PC)
{
	if (PC)
	{
		PC->GameModeActor = this;
		for (int32 i = PCList.Num() - 1; i >= 0; i--)
		{
			if (PCList[i] == nullptr)
			{
				PCList.RemoveAt(i);
				continue;
			}

			if (PCList[i]->PlayerName == PC->PlayerName)
			{
				PCList.RemoveAt(i);
				break;
			}
		}

		PCList.Add(PC);
		LuaLogin(PC);

		if (IsNeedReplay)
		{
			ReplayPawnList.AddUnique(PC->GetCurPawn());
		}

		if (IsRelevantToObserver)
		{
			TArray<ASTExtraPlayerController*> ObserversList;
			PC->GetObserversList(ObserversList);
			for (auto Observer : ObserversList)
			{
				if (Observer)
				{
					UE_LOG(LogGameModeActor, Log, TEXT("[%s][%s]Login, Observer=%s"), GameModeActor_ISCLIENTORSERVER, *GetName(), *Observer->PlayerName);
					ObserverPCList.AddUnique(Observer);
					if (IsNeedReplay)
					{
						ReplayPawnList.AddUnique(Observer->GetCurPawn());
					}
				}
			}
			PC->OnMyObserversChangeDelegate.AddDynamic(this, &AGameModeActor::OnMyObserversChangeDelegate);
		}

		UE_LOG(LogGameModeActor, Log, TEXT("[%s][%s]Login, Player=%s"), GameModeActor_ISCLIENTORSERVER, *GetName(), *PC->PlayerName);
	}
}

void AGameModeActor::Logout(ASTExtraPlayerController* PC)
{
	if (PC)
	{	
		PC->GameModeActor = nullptr;
		LuaLogout(PC);
		for (int32 i = PCList.Num() - 1; i >= 0; i--)
		{
			if (PCList[i] == nullptr)
			{
				PCList.RemoveAt(i);
				continue;
			}

			if (PCList[i]->PlayerName == PC->PlayerName)
			{
				PCList.RemoveAt(i);
				break;
			}

			if (IsNeedReplay)
			{
				ReplayPawnList.Remove(PC->GetCurPawn());
			}

			if (IsRelevantToObserver)
			{
				TArray<ASTExtraPlayerController*> ObserversList;
				PC->GetObserversList(ObserversList);
				for (auto Observer : ObserversList)
				{
					if (Observer)
					{
						UE_LOG(LogGameModeActor, Log, TEXT("[%s][%s]Logout, Observer=%s"), GameModeActor_ISCLIENTORSERVER, *GetName(), *Observer->PlayerName);
						ObserverPCList.Remove(Observer);
						if (IsNeedReplay)
						{
							ReplayPawnList.Remove(Observer->GetCurPawn());
						}
					}
				}
				PC->OnMyObserversChangeDelegate.RemoveDynamic(this, &AGameModeActor::OnMyObserversChangeDelegate);
			}
		}

		UE_LOG(LogGameModeActor, Log, TEXT("[%s][%s]Logout, Player=%s"), GameModeActor_ISCLIENTORSERVER, *GetName(), *PC->PlayerName);
	}
}

bool AGameModeActor::IsNetRelevantFor(const AActor* RealViewer, const AActor* ViewTarget, const FVector& SrcLocation) const
{
	for (int32 i = 0; i < PCList.Num(); i++)
	{
		if (PCList[i] && RealViewer && PCList[i] == RealViewer)
		{
			return true;
		}
	}

	if (IsRelevantToObserver)
	{
		// 给观战的玩家同步
		for (int32 i = 0; i < ObserverPCList.Num(); i++)
		{
			if (ObserverPCList[i] && RealViewer && ObserverPCList[i] == RealViewer)
			{
				return true;
			}
		}

		// 处理变鸡的情况下就按距离同步
		const ASTExtraPlayerController* RealViewerController = Cast<ASTExtraPlayerController>(RealViewer);
		if (RealViewerController && RealViewerController->IsOnOBTransformState())
		{
			return Super::IsNetRelevantFor(RealViewer, ViewTarget, SrcLocation);
		}
	}

	if (IsNeedReplay)
	{
		const ASTExtraPlayerController* RealViewerController = Cast<ASTExtraPlayerController>(RealViewer);
		if (RealViewerController && RealViewerController->IsDemoPlaySpectator())
		{
			return ReplayPawnList.Contains(RealViewerController->GetCurPawn());
		}
	}
	
	if(OnlyNetRelevantForPlayer)
	{
		return false;
	}
	return Super::IsNetRelevantFor(RealViewer, ViewTarget, SrcLocation);
}

void AGameModeActor::AddActorFromLuaTable(float X, float Y, float Z, float Roll, float Pitch, float Yaw, const FString& BPPath)
{
	FSpawnActorInfo Info;
	Info.BPPath = BPPath;
	Info.Loc = GetActorLocation();
	Info.Loc.X += X;
	Info.Loc.Y += Y;
	Info.Loc.Z += Z;
	Info.Rot = GetActorRotation();
	Info.Rot.Roll += Roll;
	Info.Rot.Pitch += Pitch;
	Info.Rot.Yaw += Yaw;
	SpawnActorInfoList.Add(Info);

	UE_LOG(LogGameModeActor, Log, TEXT("[%s][%s]AddActorFromLuaTable: Loc=%s, Rot=%s, BPPath=%s"), GameModeActor_ISCLIENTORSERVER,
		*GetName(), *Info.Loc.ToStringShort(), *Info.Rot.ToStringShort(), *Info.BPPath);
}

void AGameModeActor::TickSpawnActor()
{
	UWorld* World = GetWorld();
	if (World)
	{
		int32 SpawnCount = 0;
		for (int32 i = SpawnActorInfoList.Num() - 1; i >= 0; i--)
		{
			SpawnCount++;
			if (SpawnCount >= 30)
			{
				break;
			}

			UClass* Class = LoadClass<UObject>(nullptr, *SpawnActorInfoList[i].BPPath);
			if (Class == nullptr)
			{
				UE_LOG(LogGameModeActor, Error, TEXT("[%s][%s]TickSpawnActor Failed, Class == nullptr, BPPath=%s"), GameModeActor_ISCLIENTORSERVER,
				*GetName(), *SpawnActorInfoList[i].BPPath);
				SpawnActorInfoList.RemoveAt(i);
				continue;
			}

			FTransform Trans = UKismetMathLibrary::MakeTransform(SpawnActorInfoList[i].Loc, SpawnActorInfoList[i].Rot, FVector::OneVector);
			FActorSpawnParameters SpawnInfo;
			SpawnInfo.SpawnCollisionHandlingOverride = ESpawnActorCollisionHandlingMethod::AlwaysSpawn;
			UUAEGameInstance* const GameInstance = GetWorld()->GetGameInstance<UUAEGameInstance>();
			if (GameInstance)
			{
				SpawnInfo.ActorFeatureSetConfig = GameInstance->GetFeatureSetForActorClass(Class, EFeatureSetType::DedicateServer);
			}
			AActor *  Actor = World->SpawnActor<AActor>(Class, Trans, SpawnInfo);
			if (Actor)
			{
				AddDynamicSpawnedActor(Actor);
				UE_LOG(LogGameModeActor, Log, TEXT("[%s][%s]TickSpawnActor Success, Actor=%s, Loc=%s, Rot=%s"),GameModeActor_ISCLIENTORSERVER,
				*GetName(), *Actor->GetName(),
				*Actor->GetActorLocation().ToStringShort(), *Actor->GetActorRotation().ToStringShort());
			}
			else
			{
				UE_LOG(LogGameModeActor, Error, TEXT("[%s][%s]TickSpawnActor Failed, SpawnActor, BPPath=%s"), GameModeActor_ISCLIENTORSERVER,
				*GetName(), *SpawnActorInfoList[i].BPPath);
			}

			SpawnActorInfoList.RemoveAt(i);
		}
	}
}

void AGameModeActor::TickLocalPlayerPawnLoaded()
{
	UE_LOG(LogGameModeActor, Log, TEXT("[%s][%s]TickLocalPlayerPawnLoaded..."), GameModeActor_ISCLIENTORSERVER, *GetName());
	if (GetWorld() && !LocalPlayerPawnLoaded)
	{
		ASTExtraPlayerController* PC = Cast<ASTExtraPlayerController>(UGameplayStatics::GetPlayerController(GetWorld(), 0));
		if (PC)
		{
			UE_LOG(LogGameModeActor, Log, TEXT("[%s][%s]TickLocalPlayerPawnLoaded PC=%s"), GameModeActor_ISCLIENTORSERVER,
			*GetName(), *PC->GetName());
			ASTExtraBaseCharacter* CH = PC->GetPlayerCharacterSafety();
			if (CH)
			{
				UE_LOG(LogGameModeActor, Log, TEXT("[%s][%s]TickLocalPlayerPawnLoaded CH=%s, bHasInitOnClientWithGameState=%d, STExtraPlayerState=%s"), 
				GameModeActor_ISCLIENTORSERVER,
				*GetName(), *CH->GetName(), (int32)CH->bHasInitOnClientWithGameState, (CH->STExtraPlayerState == nullptr)?TEXT("nullptr"):(*CH->STExtraPlayerState->GetName()));
				if (CH->bHasInitOnClientWithGameState && CH->STExtraPlayerState)
				{
					UE_LOG(LogGameModeActor, Log, TEXT("[%s][%s]TickLocalPlayerPawnLoaded LocalPlayerPawnLoaded=true CH=%s"), GameModeActor_ISCLIENTORSERVER,
					*GetName(), *CH->GetName());
					LocalPlayerPawnLoaded = true;
				}
			}
		}
	}
}

FString AGameModeActor::GetLuaTableFullPath(int32 Idx)
{
	for (int32 i = 0; i < ExportedActorLuaTableList.Num(); i++)
	{
		if (i == Idx)
		{
			FString FilePath = FString::Printf(TEXT("%sGeneratorData/LevelActorTable/%s"), *FPaths::ProjectContentDir(), *ExportedActorLuaTableList[i]);
			UE_LOG(LogGameModeActor, Log, TEXT("[%s][%s]GetLuaTableFullPath FilePath=%s"), GameModeActor_ISCLIENTORSERVER, *GetName(), *FilePath);
			return FilePath;
		}
	}

	return TEXT("");
}

void AGameModeActor::NotifyClientLoadingComplete(ASTExtraPlayerController* PC)
{
	if (PC && !LocalPlayerPawnLoaded)
	{
		LocalPlayerPawnLoaded = true;
		GotoActiveState();
		UE_LOG(LogGameModeActor, Log, TEXT("[%s][%s]NotifyClientLoadingComplete, PlayerName=%s"), GameModeActor_ISCLIENTORSERVER, *GetName(), *PC->PlayerName);
	}
	Lua_ServerNotifyClientLoadingComplete(PC);
}

void AGameModeActor::CheckClientLoadingCompleted()
{
	UE_LOG(LogGameModeActor, Log, TEXT("[%s][%s]CheckClientLoadingCompleted..."), GameModeActor_ISCLIENTORSERVER, *GetName());
	if (!Lua_CheckClientLoadingCompleted())
	{
		return;
	}

	if (!LocalPlayerPawnLoaded)
	{
		return;
	}

	if (GetWorld())
	{
		ASTExtraPlayerController* PC = Cast<ASTExtraPlayerController>(UGameplayStatics::GetPlayerController(GetWorld(), 0));
		if (PC)
		{
			IsClientLoadingCompleted = true;
			PC->RPC_NotifyClientLoadingComplete();
			Lua_OnClientLoadingCompleted();
			UE_LOG(LogGameModeActor, Log, TEXT("[%s][%s]CheckClientLoadingCompleted"), GameModeActor_ISCLIENTORSERVER, *GetName());
		}
	}
}

void AGameModeActor::OnMyObserversChangeDelegate(ASTExtraPlayerController* ObserverController, bool bIsEnter)
{
	if (!ObserverController)
	{
		return;
	}

	UE_LOG(LogGameModeActor, Log, TEXT("[%s][%s]OnMyObserversChangeDelegate, Observer=%s, bIsEnter=%d"), GameModeActor_ISCLIENTORSERVER, *GetName(), *ObserverController->PlayerName, bIsEnter);
	if (bIsEnter)
	{
		ObserverPCList.AddUnique(ObserverController);
		if (IsNeedReplay)
		{
			ReplayPawnList.AddUnique(ObserverController->GetCurPawn());
		}
	}
	else
	{
		ObserverPCList.Remove(ObserverController);
		if (IsNeedReplay)
		{
			ReplayPawnList.Remove(ObserverController->GetCurPawn());
		}
	}
}

void AGameModeActor::BroadcastInitPlayer_Implementation(AActor* Player)
{

}

void AGameModeActor::SetInCheatMode_Implementation(AActor* Player)
{

}

#if WITH_EDITOR
void AGameModeActor::PreSave(const class ITargetPlatform* TargetPlatform)
{
	UBlueprint* BP = Cast<UBlueprint>(GetClass()->ClassGeneratedBy);
	if (BP == nullptr)
	{
		Super::PreSave(TargetPlatform);
		return;
	}

	USimpleConstructionScript* SCS = BP->SimpleConstructionScript;
	if (SCS == nullptr)
	{
		Super::PreSave(TargetPlatform);
		return;
	}

	if (IsRunningCommandlet())
	{
		bool IsDS = (TargetPlatform && TargetPlatform->IsServerOnly());
		SerializePlayerStartTransform(IsDS);
	}
	else
	{
		SerializePlayerStartTransform(true);
	}

	CalculateGameTotalTime();
	Super::PreSave(TargetPlatform);
}

void AGameModeActor::SerializePlayerStartTransform(bool IsDS)
{
	UClass* MyClass = GetClass();
	if (MyClass == nullptr)
	{
		return;
	}

	UObject* MyCDO = nullptr;
	UBlueprint* BP = nullptr;
	USimpleConstructionScript* SimpleConstructionScript = nullptr;

	MyCDO = MyClass->GetDefaultObject();
	if (MyCDO == nullptr)
	{
		UE_LOG(LogGameModeActor, Warning, TEXT("SerializePlayerStartTransform: GetDefaultObject failed, ClassName=%s"), *MyClass->GetName());
		return;
	}

	if (!IsDS)
	{
		FProperty* Property = MyClass->FindPropertyByName(FName(TEXT("PlayerStartTransformList")));
		if (Property != nullptr)
		{
			Property->SetPropertyFlags(CPF_SkipSerialization);
		}
		UE_LOG(LogGameModeActor, Warning, TEXT("SerializePlayerStartTransform: CPF_SkipSerialization PlayerStartTransformList"));
		return;
	}

	BP = Cast<UBlueprint>(MyClass->ClassGeneratedBy);
	if (BP == nullptr)
	{
		UE_LOG(LogGameModeActor, Warning, TEXT("SerializePlayerStartTransform: Cast<UBlueprint> failed, ClassName=%s"), *MyClass->GetName());
		return;
	}

	SimpleConstructionScript = BP->SimpleConstructionScript;
	if (SimpleConstructionScript == nullptr)
	{
		UE_LOG(LogGameModeActor, Warning, TEXT("SerializePlayerStartTransform: Test SimpleConstructionScript failed, ClassName=%s"), *MyClass->GetName());
		return;
	}

	PlayerStartTransformList.Empty();
	for (USCS_Node* Node : SimpleConstructionScript->GetAllNodes())
	{
		if (Node && Node->ComponentTemplate && Node->ComponentTemplate->IsA(UChildActorComponent::StaticClass()))
		{
			UChildActorComponent * ChildActorComponent = Cast<UChildActorComponent>(Node->ComponentTemplate);
			if (ChildActorComponent && ChildActorComponent->GetName().Contains(TEXT("PlayerStart")))
			{
				FTransform Trans = ChildActorComponent->GetRelativeTransform();
				PlayerStartTransformList.Add(Trans);
			}
		}
	}
}

void AGameModeActor::CalculateGameTotalTime()
{
	GameTotalTime = 0;
	if (GameModeActorStateActive)
	{
		GameTotalTime += GameModeActorStateActive->GetStateTime();
	}

	if (GameModeActorStateReady)
	{
		GameTotalTime += GameModeActorStateReady->GetStateTime();
	}

	if (GameModeActorStateFighting)
	{
		GameTotalTime += GameModeActorStateFighting->GetStateTime();
	}

	if (GameModeActorStateFinished)
	{
		GameTotalTime += GameModeActorStateFinished->GetStateTime();
	}
}
#endif
