#include "ShadowTrackerExtra.h"
#include "AI/Controller/BasicAIController.h"
#include "BTDecorator_IsInYearBeastMagicCircle.h"
#include "BehaviorTree/BlackboardComponent.h"
#include "BehaviorTree/Blackboard/BlackboardKeyType_Object.h"
#include "AI/Assist/AIWorldVolume.h"
#include "GameMode/BattleRoyaleGameModeBase.h"

UBTDecorator_IsInYearBeastMagicCircle::UBTDecorator_IsInYearBeastMagicCircle(const FObjectInitializer& ObjectInitializer)
	: Super(ObjectInitializer)
{
	NodeName = TEXT("IsInYearBeastMagicCircle");

	OutYearBeastMagicCircle.AddObjectFilter(this, GET_MEMBER_NAME_CHECKED(UBTDecorator_IsInYearBeastMagicCircle, OutYearBeastMagicCircle), AActor::StaticClass());
}

void UBTDecorator_IsInYearBeastMagicCircle::InitializeFromAsset(UBehaviorTree& Asset)
{
	Super::InitializeFromAsset(Asset);

	UBlackboardData* BBAsset = GetBlackboardAsset();
	if (BBAsset)
	{
		OutYearBeastMagicCircle.ResolveSelectedKey(*BBAsset);
	}
}

void UBTDecorator_IsInYearBeastMagicCircle::PostLoad()
{
	Super::PostLoad();
	bNotifyTick = (FlowAbortMode != EBTFlowAbortMode::None);
}

bool UBTDecorator_IsInYearBeastMagicCircle::CalculateRawConditionValue(UBehaviorTreeComponent& OwnerComp, uint8* NodeMemory) const
{
	TNodeMemory* MyMemory = (TNodeMemory*)NodeMemory;
	const bool bResult = CalcConditionImpl(OwnerComp, NodeMemory);
	if (MyMemory)
	{
		MyMemory->bLastResult = bResult;
	}
	return bResult;
}

void UBTDecorator_IsInYearBeastMagicCircle::TickNode(UBehaviorTreeComponent& OwnerComp, uint8* NodeMemory, float DeltaSeconds)
{
	TNodeMemory* MyMemory = (TNodeMemory*)NodeMemory;
	const bool bResult = CalcConditionImpl(OwnerComp, NodeMemory);
	if (MyMemory && bResult != MyMemory->bLastResult)
	{
		MyMemory->bLastResult = bResult;
		OwnerComp.RequestExecution(this);
	}
}

bool UBTDecorator_IsInYearBeastMagicCircle::CalcConditionImpl(UBehaviorTreeComponent& OwnerComp, uint8* NodeMemory) const
{
	ABasicAIController* OwnerController = Cast<ABasicAIController>(OwnerComp.GetAIOwner());
	if (!OwnerController) { return false; }
	AActor* OwnerActor = OwnerController->GetPawn();
	if (!OwnerActor) { return false; }
	ABattleRoyaleGameModeBase* MyGameMode = GetWorld()->GetAuthGameMode<ABattleRoyaleGameModeBase>();
	if (!MyGameMode || !MyGameMode->AIWorldVolume) { return false; }
	UBlackboardComponent* BBComp = OwnerComp.GetBlackboardComponent();
	if (!BBComp) { return false; }
	TNodeMemory* MyMemory = (TNodeMemory*)NodeMemory;
	if (!MyMemory) { return false; }

	const FVector OwnerLoc = OwnerActor->GetActorLocation();
	const TArray<TWeakObjectPtr<AActor>>& YearBeastMagicCircles = MyGameMode->AIWorldVolume->GetYearBeastMagicCircles();

#if !UE_BUILD_SHIPPING && !UE_BUILD_TEST
	if (YearBeastMagicCircles.Num() <= 0)
	{
		UE_LOG(LogTemp, Log, TEXT("No YearBeastMagicCircles, Owner: %s"), *GetNameSafe(OwnerActor));
	}
#endif

	AActor* RetMagicCircle = nullptr;
	for (auto& MagicCirclePtr : YearBeastMagicCircles)
	{
		AActor* MagicCircle = MagicCirclePtr.Get();
		if (!MagicCircle)
		{
#if !UE_BUILD_SHIPPING && !UE_BUILD_TEST
			UE_LOG(LogTemp, Log, TEXT("MagicCircle Is invalid, Owner: %s"), *GetNameSafe(OwnerActor));
#endif
			continue;
		}
		const FVector MagicCircleLoc = MagicCircle->GetActorLocation();
		float CurDistSq = FVector::DistSquared(OwnerLoc, MagicCircleLoc);
#if !UE_BUILD_SHIPPING && !UE_BUILD_TEST
		if (OwnerController->GetLogDebugInfoTime() > 0.f)
		{
			UE_LOG(LogTemp, Log, TEXT("MagicCircle %s, Loc: %s, OwnerLoc: %s, DistSq: %f, TestDist: %s"),
				*GetNameSafe(MagicCircle), *MagicCircleLoc.ToString(), *OwnerLoc.ToString(),
				CurDistSq, *TestDistanceRange.ToString());
		}
#endif
		if (CurDistSq >= FMath::Square(TestDistanceRange.X) && CurDistSq <= FMath::Square(TestDistanceRange.Y))
		{
			RetMagicCircle = MagicCircle;
			break;
		}
	}
	if (RetMagicCircle != MyMemory->LastMagicCircle.Get())
	{
#if !UE_BUILD_SHIPPING && !UE_BUILD_TEST
		UE_LOG(LogTemp, Log, TEXT("Find a New MagicCircle, Owner: %s, MagicCircle: %s"), *GetNameSafe(OwnerActor), *GetNameSafe(RetMagicCircle));
#endif
		MyMemory->LastMagicCircle = RetMagicCircle;
		BBComp->SetValue<UBlackboardKeyType_Object>(OutYearBeastMagicCircle.GetSelectedKeyID(), RetMagicCircle);
	}
#if !UE_BUILD_SHIPPING && !UE_BUILD_TEST
	if (OwnerController->GetLogDebugInfoTime() > 0.f)
	{
		UE_LOG(LogTemp, Log, TEXT("RetMagicCircle %s, Execute Index: %d, Owner: %s, OwnerLoc: %s"),
			*GetNameSafe(RetMagicCircle), GetExecutionIndex(), *GetNameSafe(OwnerActor), *OwnerLoc.ToString());
	}
#endif
	
	return RetMagicCircle != nullptr;
}