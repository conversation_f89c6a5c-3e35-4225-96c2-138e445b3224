// Fill out your copyright notice in the Description page of Project Settings.

#pragma once

#include "CoreMinimal.h"
#include "BehaviorTree/BTDecorator.h"
#include "AI/AIEnvConfig.h"
#include "BTDecorator_CheckTargetType.generated.h"

/**
*
*/

UENUM()
enum class ETargetEnemyType : uint8
{
	TargetEnemyType_Player               UMETA(DisplayName = "人类型"),
	TargetEnemyType_Animal               UMETA(DisplayName = "动物类型"),
};

UCLASS(meta = (DisplayName = "[NEW]UBTDecorator_CheckTargetType"))
class UBTDecorator_CheckTargetType : public UBTDecorator
{
	GENERATED_UCLASS_BODY()

public:
	virtual bool CalculateRawConditionValue(UBehaviorTreeComponent& OwnerComp, uint8* NodeMemory) const;

	UPROPERTY(EditAnywhere, meta = (DisplayName = "目标类型,满足任意一种类型返回true"), Category = Config)
	TArray<ETargetEnemyType> TargetEnemyTypes;
};