// Fill out your copyright notice in the Description page of Project Settings.

#include "ShadowTrackerExtra.h"
#include "BTTask_TeleportToSpecLoc.h"
#include "BehaviorTree/Blackboard/BlackboardKeyType_Object.h"
#include "BehaviorTree/Blackboard/BlackboardKeyType_Vector.h"
#include "AI/Controller/FakePlayerAIController.h"
#include "GameMode/BattleRoyaleGameMode.h"
#include "AI/Assist/AIActingComponent.h"
#include "AI/Assist/AIWorldVolume.h"
#include "GameMode/GamemodeAIDataAsset.h"
#include "Player/STExtraPlayerCharacter.h"


UBTTask_TeleportToSpecLoc::UBTTask_TeleportToSpecLoc(const FObjectInitializer& ObjectInitializer)
	: Super(ObjectInitializer)
{
	BlackboardKey.AddObjectFilter(this, GET_MEMBER_NAME_CHECKED(UBTTask_TeleportToSpecLoc, BlackboardKey), AActor::StaticClass());
	BlackboardKey.AddVectorFilter(this, GET_MEMBER_NAME_CHECKED(UBTTask_TeleportToSpecLoc, BlackboardKey));
}

EBTNodeResult::Type UBTTask_TeleportToSpecLoc::ExecuteTask(UBehaviorTreeComponent& OwnerComp, uint8* NodeMemory)
{
	ABaseAIController* AIController = Cast<ABaseAIController>(OwnerComp.GetOwner());
	AFakePlayerAIController* FPAIC = AIController ? Cast<AFakePlayerAIController>(AIController) : nullptr;
	if (!FPAIC)
	{
		return EBTNodeResult::Failed;
	}

#if UE_BUILD_DEVELOPMENT
	UE_LOG(LogTemp, Warning, TEXT("UBTTask_TeleportToSpecLoc::ExecuteTask, UID:%llu"), FPAIC->PlayerUID);
#endif
	
	const UBlackboardComponent* MyBlackboard = OwnerComp.GetBlackboardComponent();
	ASTExtraBaseCharacter* Pawn = AIController ? Cast<ASTExtraBaseCharacter>(AIController->GetPawn()) : nullptr;
	if (MyBlackboard == nullptr || Pawn == nullptr || Pawn->IsAlive() == false || Pawn->IsNearDeath())
	{
		FPAIC->OnNotifyAITeleportFailed(EBTTeleportFailedReason::OTHER);
		return EBTNodeResult::Failed;
	}

	UWorld* CurrentWorld = GetWorld();
	ABattleRoyaleGameMode* CurGameMode = CurrentWorld ? CurrentWorld->GetAuthGameMode<ABattleRoyaleGameMode>() : nullptr;
	UAIActingComponent* AIActing = CurGameMode ? CurGameMode->AIActingComp : nullptr;
	if (AIActing == nullptr)
	{
		UE_LOG(LogTaskTeleportTo, Warning, TEXT("UBTTask_TeleportToSpecLoc::ExecuteTask AIActingComp is null"));
		FPAIC->OnNotifyAITeleportFailed(EBTTeleportFailedReason::OTHER);
		return EBTNodeResult::Failed;
	}

	UGamemodeAIDataAsset* GameModeAIDataAsset = CurGameMode->GameModeAIDataAsset;
	if (!GameModeAIDataAsset)
	{
		UE_LOG(LogTaskTeleportTo, Warning, TEXT("UBTTask_TeleportToSpecLoc::ExecuteTask GameModeAIDataAsset is null"));
		FPAIC->OnNotifyAITeleportFailed(EBTTeleportFailedReason::OTHER);
		return EBTNodeResult::Failed;
	}

	if (AIController->IsInSpawnEquipProtectTime())
	{
		FPAIC->OnNotifyAITeleportFailed(EBTTeleportFailedReason::AI_IN_SPAWN_EQUIP_PROTECT_TIME);
		return EBTNodeResult::Succeeded;
	}

	if (FPAIC->PreyedPlayerKey > 0)
	{
		UE_LOG(LogTemp, Log, TEXT("UBTTask_TeleportToSpecLoc::ExecuteTask AIPlayerKey:%u is preyed by player:%u"), FPAIC->PlayerKey, FPAIC->PreyedPlayerKey);
		FPAIC->OnNotifyAITeleportFailed(EBTTeleportFailedReason::AI_PREYED_BY_PLAYER);
		return EBTNodeResult::Succeeded;
	}

	TArray<ASTExtraPlayerCharacter *> AllPlayerCharacters;
	AIActing->CachedCharcterPool.GetAllPlayerCharacters(AllPlayerCharacters);

    bool isUseNewAI = UseNewAI(AIController->GetWorld()->GetName());

	float CurOriginCheckSquare = isUseNewAI ? (bForMLAI ? GameModeAIDataAsset->OriginCheckSquare_MLAI : GameModeAIDataAsset->OriginCheckSquare) : OriginCheckSquare;
	for (int32 i = 0; i < AllPlayerCharacters.Num(); ++i)
	{
		if (AllPlayerCharacters[i] == nullptr)
		{
			continue;
		}
		float DistSquare = FVector::DistSquaredXY(AllPlayerCharacters[i]->GetActorLocation(), Pawn->GetActorLocation());
		if (DistSquare < CurOriginCheckSquare)
		{
			FPAIC->OnNotifyAITeleportFailed(EBTTeleportFailedReason::AI_PLAYERS_DIST_CHECK);
			return  bForceSuccess ? EBTNodeResult::Succeeded :EBTNodeResult::Failed;
		}
	}


	FVector TargetLoc = FVector::ZeroVector;
	if (BlackboardKey.SelectedKeyType == UBlackboardKeyType_Vector::StaticClass())
	{
		TargetLoc = MyBlackboard->GetValue<UBlackboardKeyType_Vector>(BlackboardKey.GetSelectedKeyID());
	}
	else if (BlackboardKey.SelectedKeyType == UBlackboardKeyType_Object::StaticClass())
	{
		UObject* KeyValue = MyBlackboard->GetValue<UBlackboardKeyType_Object>(BlackboardKey.GetSelectedKeyID());
		AActor* TargetActor = Cast<AActor>(KeyValue);
		if (TargetActor)
		{
			TargetLoc = TargetActor->GetActorLocation();
		}
	}

	if (TargetLoc.IsZero() == false)
	{
		float DisSquare = FVector::DistSquared2D(TargetLoc, Pawn->GetActorLocation());
		float CurRangeMax = isUseNewAI ? (bForMLAI ? GameModeAIDataAsset->RangeMax_MLAI : GameModeAIDataAsset->RangeMax) : RangeMax;
		if (DisSquare < CurRangeMax * CurRangeMax)
		{
			FPAIC->OnNotifyAITeleportFailed(EBTTeleportFailedReason::AI_DELIVERY_TARGET_DIST_CHECK);
			return EBTNodeResult::Succeeded;
		}

		float RandRad = FMath::FRandRange(0, PI * 2);
		float CurRangeMin = isUseNewAI ? (bForMLAI ? GameModeAIDataAsset->RangeMin_MLAI : GameModeAIDataAsset->RangeMin) : RangeMin;
		float Radius  = FMath::RandRange(CurRangeMin, CurRangeMax);
		FVector CachedTargetLoc = TargetLoc;
		TargetLoc.X = TargetLoc.X + Radius * FMath::Cos(RandRad);
		TargetLoc.Y = TargetLoc.Y + Radius * FMath::Sin(RandRad);

		FHitResult OutHitResult;
		FCollisionResponseParams ResponseParam;

		FVector EndLoc = FVector(TargetLoc.X, TargetLoc.Y, -10000.f);
		TargetLoc.Z = isUseNewAI ? (bForMLAI ? GameModeAIDataAsset->TraceHeight_MLAI : GameModeAIDataAsset->TraceHeight) : TraceHeight;
		bool Ret = GetWorld()->LineTraceSingleByChannel(OutHitResult, TargetLoc, EndLoc, COLLISION_TestHeight, FCollisionQueryParams::DefaultQueryParam, ResponseParam);
		//if (OutHitResult.GetComponent())
		if (Ret)
		{
			TargetLoc = OutHitResult.Location;
			TargetLoc.Z += 200.f;

            float CheckSquare = isUseNewAI ? (bForMLAI ? GameModeAIDataAsset->DestCheckSquare_MLAI : GameModeAIDataAsset->DestCheckSquare) : DestCheckSquare;
			for (int32 i = 0; i < AllPlayerCharacters.Num(); ++i)
			{
				float CurDistSquare = FVector::DistSquaredXY(AllPlayerCharacters[i]->GetActorLocation(), TargetLoc);
                if (CurDistSquare < CheckSquare)
				{
                	FPAIC->OnNotifyAITeleportFailed(EBTTeleportFailedReason::TARGET_POINT_PLAYERS_DIST_CHECK);
					return EBTNodeResult::Succeeded;
				}
			}

            AAIWorldVolume* AIWorldVolume = CurGameMode ? CurGameMode->AIWorldVolume : nullptr;
            if (nullptr == AIWorldVolume || AIWorldVolume->IsInAITeleportForbiddenAreaXY(TargetLoc))
            {
            	FPAIC->OnNotifyAITeleportFailed(EBTTeleportFailedReason::TARGET_POINT_IN_TELEPORT_FORBIDDEN_AREA);
                return EBTNodeResult::Succeeded;
            }

			if (AIActing->AdjustPosIsInDeliveryForbiddenArea(TargetLoc))
			{
				FPAIC->OnNotifyAITeleportFailed(EBTTeleportFailedReason::TARGET_POINT_IN_DELIVERY_FORBIDDEN_AREA);
				return EBTNodeResult::Succeeded;
			}

			if (!AIActing->GetSpawnMapBounds().IsInside(FVector2D(TargetLoc.X, TargetLoc.Y)))
			{
				FPAIC->OnNotifyAITeleportFailed(EBTTeleportFailedReason::TARGET_POINT_IN_SPAWN_AREA);
				return EBTNodeResult::Succeeded;
			}

			//新搜索方式
			{
				float Dist = MAX_FLT;
				float DistAI = MAX_FLT;
				const int MaxAllowNum = 20;
				float SensedRadius = FMath::Sqrt(CheckSquare);
				const ASTExtraBaseCharacter* Characters[MaxAllowNum] = { nullptr };
				int32 Cnt = AIWorldVolume->QueryCharacters(TargetLoc, SensedRadius, Characters, MaxAllowNum);
				for (int i = 0; i < Cnt; i++)
				{
					const ASTExtraBaseCharacter* CurCharacter = Characters[i];
					if (!CurCharacter || CurCharacter->bIsAI)
					{
						continue;
					}

					if (CurCharacter != Pawn)
					{
						UE_LOG(LogTaskTeleportTo, Warning, TEXT("UBTTask_TeleportToSpecLoc has player! MyName=%s TargetPos=%s, BlockedPlayerName=%s, BlockedPlayerPos=%s, BlockedPlayerIsAlive=%d"),
							*Pawn->PlayerName, *TargetLoc.ToString(), *CurCharacter->PlayerName, *CurCharacter->GetActorLocation().ToString(), (int)CurCharacter->IsAlive());
						UE_LOG(LogTaskTeleportTo, Warning, TEXT("UBTTask_TeleportToSpecLoc has player! CachedTargetLoc=%s"), *CachedTargetLoc.ToString());

#if UE_SERVER || UE_EDITOR
						FCoreDelegates::AddStringMsgToTLogDelegate.ExecuteIfBound(TEXT("KeyCount"), TEXT("LogError"), "TeleportToSpecLocHasPlayer", 1, false, 0);
#endif
						FPAIC->OnNotifyAITeleportFailed(EBTTeleportFailedReason::TARGET_POINT_HAS_PLAYER);
						return EBTNodeResult::Succeeded;
					}
				}
			}

            //开始传送
			Pawn->TeleportTo(TargetLoc, Pawn->GetActorRotation());
			AIController->RestartProtectTime();
			AIController->OnAITeleportToSpecLoc(TargetLoc);
			UE_LOG(LogTaskTeleportTo, Log, TEXT("UBTTask_TeleportToSpecLoc Name=%s x= %f, y = %f, z = %f"), *Pawn->PlayerName ,TargetLoc.X, TargetLoc.Y, TargetLoc.Z);
			UE_LOG(LogTaskTeleportTo, Log, TEXT("UBTTask_TeleportToSpecLoc Player x= %f, y = %f, z = %f"), CachedTargetLoc.X, CachedTargetLoc.Y, CachedTargetLoc.Z);
			return EBTNodeResult::Succeeded;
		}
		else
		{
			UE_LOG(LogTaskTeleportTo, Log, TEXT("UBTTask_TeleportToSpecLoc::ExecuteTask Null!!!x=%f, y=%f"), TargetLoc.X, TargetLoc.Y);
		}
	}
	
	FPAIC->OnNotifyAITeleportFailed(EBTTeleportFailedReason::OTHER);
	return EBTNodeResult::Failed;
}
