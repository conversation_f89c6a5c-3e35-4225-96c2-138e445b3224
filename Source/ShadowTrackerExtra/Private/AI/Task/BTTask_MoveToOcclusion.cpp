// Fill out your copyright notice in the Description page of Project Settings.

#include "ShadowTrackerExtra.h"
#include "BTTask_MoveToOcclusion.h"
#include "BehaviorTree/BlackboardComponent.h"
#include "AI/AIEnvConfig.h"
#include "AIController.h"
#include "BehaviorTree/Blackboard/BlackboardKeyType_Object.h"
#include "BehaviorTree/Blackboard/BlackboardKeyType_Vector.h"
#include "AI/Assist/UAEAIOcclusion.h"
#include "AI/Assist/AIWorldVolume.h"
#include "GameMode/BattleRoyaleGameMode.h"
#include "AI/Controller/BaseAIController.h"
#include "AI/Controller/BasicAIController.h"
#include "AI/Component/AIHoleUpComponent.h"
#include "CustomComponent/CircleMgrComponent.h"
#include "CustomComponent/STBuildingOcclusionPointComponent.h"
#include "Character/STExtraCharacter.h"

#define QUERY_BUILDING 1


UBTTask_MoveToOcclusion::UBTTask_MoveToOcclusion(const FObjectInitializer& ObjectInitializer)
	: Super(ObjectInitializer)
{
	NodeName = "MoveToOcclusion";
	bNotifyTick = true;
	bNotifyTaskFinished = true;

	EnemyBlackboardKey.AddObjectFilter(this, GET_MEMBER_NAME_CHECKED(UBTTask_MoveToOcclusion, EnemyBlackboardKey), AActor::StaticClass());
}

void UBTTask_MoveToOcclusion::InitializeFromAsset(UBehaviorTree& Asset)
{
	Super::InitializeFromAsset(Asset);

	UBlackboardData* BBAsset = GetBlackboardAsset();
	if (BBAsset)
	{
		EnemyBlackboardKey.ResolveSelectedKey(*BBAsset);
	}
	else
	{
		UE_LOG(LogBehaviorTree, Warning, TEXT("Can't initialize task: %s, make sure that behavior tree specifies blackboard asset!"), *GetName());
	}
}

EBTNodeResult::Type UBTTask_MoveToOcclusion::ExecuteTask(UBehaviorTreeComponent& OwnerComp, uint8* NodeMemory)
{
	EBTNodeResult::Type NodeResult = EBTNodeResult::Failed;

	ABasicAIController* AIController = Cast<ABasicAIController>(OwnerComp.GetAIOwner());
	
	do 
	{
		UBlackboardComponent* MyBlackboard = OwnerComp.GetBlackboardComponent();
		if (nullptr == MyBlackboard)
		{
			break;
		}

		if (nullptr != AIController)
		{
			ASTExtraCharacter* ControlledPawn = AIController ? Cast<ASTExtraCharacter>(AIController->GetPawn()) : nullptr;
			if (ControlledPawn)
			{
				FVector TargetLocation = FVector::ZeroVector;
				do 
				{
					if (nullptr == AIController)
					{
						break;
					}
					UAIHoleUpComponent* AIHoleUpComp = AIController->FindComponentByClass<UAIHoleUpComponent>();
					if (nullptr == AIHoleUpComp)
					{
						UE_LOG(LogBehaviorTree, Error, TEXT("UBTTask_MoveToOcclusion::ExecuteTask AIController is not UAIHoleUpComponent! Please add UAIHoleUpComponent!"));
						break;
					}

					// 查找场景静态掩体点
					const FAIOcclusionPointData* P = FindNearestOcclusionPoint(OwnerComp, NodeMemory);
					if (nullptr != P) {
						TargetLocation.X = P->Location[0];
						TargetLocation.Y = P->Location[1];
						TargetLocation.Z = P->Location[2];

						AIHoleUpComp->AIDataTable.OcclusionPointData = P;
						break;
					}

					// 查找场景动态掩体点
					/*bool bRet = FindBestBuildingOcclusionPoint(OwnerComp
						, NodeMemory
						, &(AIHoleUpComp->AIDataTable.BuildingOcclusionPointData));
					if (bRet) {
						TargetLocation = AIHoleUpComp->AIDataTable.BuildingOcclusionPointData.Location;
						break;
					}*/
					return EBTNodeResult::Failed;
				} while (false);
				
				// 朝向点移动
				{
					float DistSq = FVector::DistSquared(TargetLocation, ControlledPawn->GetActorLocation());

					if (ABaseAIController::bAILogOpen)
					{
						UE_LOG(LogTemp, Log, TEXT("%p MoveToOcclusion::ExecuteTask, P:%s, DistSq:%f")
							, AIController, *TargetLocation.ToString(), DistSq);
					}

					if (DistSq < FMath::Square(10))
					{
						NodeResult = EBTNodeResult::Succeeded;
						break;
					}

					if (BlackboardKey.SelectedKeyType == UBlackboardKeyType_Vector::StaticClass())
					{
						MyBlackboard->SetValue<UBlackboardKeyType_Vector>(BlackboardKey.GetSelectedKeyID(), TargetLocation);

						NodeResult = Super::ExecuteTask(OwnerComp, NodeMemory);

						if (NodeResult == EBTNodeResult::InProgress)
						{
							ASTExtraBaseCharacter* baseCharacter = Cast<ASTExtraBaseCharacter>(ControlledPawn);
							if (nullptr != baseCharacter)
							{
								baseCharacter->SwitchPoseState(ESTEPoseState::Sprint);
							}
						}
					}
				}
			}
		}
	} while (false);

	if (ABaseAIController::bAILogOpen)
	{
		UE_LOG(LogTemp, Log, TEXT("%p MoveToOcclusion::ExecuteTask, NodeResult:%d")
			, AIController, (int)NodeResult);
	}

	return NodeResult;
}

void UBTTask_MoveToOcclusion::OnTaskFinished(UBehaviorTreeComponent& OwnerComp, uint8* NodeMemory, EBTNodeResult::Type TaskResult)
{
	Super::OnTaskFinished(OwnerComp, NodeMemory, TaskResult);

	ABasicAIController* AIController = Cast<ABasicAIController>(OwnerComp.GetAIOwner());

	if (ABaseAIController::bAILogOpen)
	{
		UE_LOG(LogTemp, Log, TEXT("%p MoveToOcclusion::OnTaskFinished, TaskResult:%d")
			, AIController, (int)TaskResult);
	}

	if (TaskResult == EBTNodeResult::Type::Succeeded && nullptr != AIController)
	{
		UAIHoleUpComponent* AIHoleUpComp = AIController->FindComponentByClass<UAIHoleUpComponent>();
		if (nullptr == AIHoleUpComp)
		{
			UE_LOG(LogBehaviorTree, Error, TEXT("UBTTask_MoveToOcclusion::OnTaskFinished AIController is not UAIHoleUpComponent! Please add UAIHoleUpComponent!"));
		}
		// Set rotation and pose
		if (nullptr != AIHoleUpComp && nullptr != AIHoleUpComp->AIDataTable.OcclusionPointData)
		{
			const FAIOcclusionPointData* P = AIHoleUpComp->AIDataTable.OcclusionPointData;
			float Angle = (P->BlockAngle[0] + P->BlockAngle[1]) * 0.5f;
			ASTExtraCharacter* ControlledPawn = AIController ? Cast<ASTExtraCharacter>(AIController->GetPawn()) : nullptr;
			if (nullptr != ControlledPawn)
			{
				FRotator NewRot = ControlledPawn->GetActorRotation();
				NewRot.Yaw = Angle;
				ControlledPawn->SetActorRotation(NewRot);

				ASTExtraBaseCharacter* baseCharacter = Cast<ASTExtraBaseCharacter>(ControlledPawn);
				if (nullptr != baseCharacter)
				{
					if (bForceStand)
					{
						baseCharacter->SwitchPoseState(ESTEPoseState::Stand);
					}
					else
					{
						baseCharacter->SwitchPoseState(ESTEPoseState::Crouch);
					}
				}
			}
		}
	}
}

const struct FAIOcclusionPointData* UBTTask_MoveToOcclusion::FindNearestOcclusionPoint(UBehaviorTreeComponent& OwnerComp, uint8* NodeMemory)
{
	const UBlackboardComponent* MyBlackboard = OwnerComp.GetBlackboardComponent();
	if (nullptr == MyBlackboard)
	{
		return nullptr;
	}

	ABasicAIController* AIController = Cast<ABasicAIController>(OwnerComp.GetAIOwner());
	if (nullptr == AIController)
	{
		return nullptr;
	}

	FVector EnemyTargetLoc = FVector::ZeroVector;
	AActor* EnemyActor = nullptr;
	if (EnemyBlackboardKey.SelectedKeyType == UBlackboardKeyType_Object::StaticClass())
	{
		UObject* KeyValue = MyBlackboard->GetValue<UBlackboardKeyType_Object>(EnemyBlackboardKey.GetSelectedKeyID());
		AActor* TargetActor = Cast<AActor>(KeyValue);
		if (TargetActor)
		{
			EnemyActor = TargetActor;
			EnemyTargetLoc = TargetActor->GetActorLocation();
		}
		else
		{
			if (bChoosePointByEnemy)
			{
				UE_LOG(LogTemp, Log, TEXT("%p MoveToOcclusion ChoosePointByEnemy, But No Enemy."), AIController);
				return nullptr;
			}
		}
	}
	else
	{
		return nullptr;
	}

	APawn* ControlledPawn = AIController ? Cast<APawn>(AIController->GetPawn()) : nullptr;
	if (nullptr == ControlledPawn)
	{
		return nullptr;
	}

	UAIHoleUpComponent* AIHoleUpComp = AIController->FindComponentByClass<UAIHoleUpComponent>();
	if (nullptr == AIHoleUpComp)
	{
		UE_LOG(LogBehaviorTree, Error, TEXT("UBTTask_MoveToOcclusion::FindNearestOcclusionPoint AIController is not UAIHoleUpComponent! Please add UAIHoleUpComponent!"));
		return nullptr;
	}

	// 优先检测上一次的点是否有效，避免重复搜索
	if (bChoosePointByEnemy && nullptr != AIHoleUpComp->AIDataTable.OcclusionPointData)
	{
		const FAIOcclusionPointData* P = AIHoleUpComp->AIDataTable.OcclusionPointData;
		FVector PLoc(P->Location[0], P->Location[1], P->Location[2]);
		float DistSq = FVector::DistSquared(PLoc, ControlledPawn->GetActorLocation());
		if (DistSq < FMath::Square(UseLastPointRadius)
			&& AAIWorldVolume::CheckOcclusionPoint(*P, EnemyTargetLoc))
		{
			return P;
		}
	}

	const FVector ControlledPawnLoc = ControlledPawn->GetActorLocation();

	// 搜索新的躲避点
	UWorld* CurrentWorld = GetWorld();
	ABattleRoyaleGameMode* CurGameMode = CurrentWorld ? CurrentWorld->GetAuthGameMode<ABattleRoyaleGameMode>() : nullptr;
	if (CurGameMode)
	{
		UCircleMgrComponent* CircleMgr = CurGameMode->GetCircleMgrComponent();
		AAIWorldVolume* AIWorldVolume = CurGameMode ? CurGameMode->AIWorldVolume : nullptr;
		if (nullptr != AIWorldVolume && nullptr != CircleMgr)
		{
			float CurrentCirclePain = CircleMgr->GetCurrentPain(ControlledPawn);
			if (ControlledPawn)
			{
				FVector PawnLoc = ControlledPawn->GetActorLocation();
				const int32 MaxAllowPoints = 20;
				const FAIOcclusionPointData* ValidPoints[MaxAllowPoints] = { nullptr };

				if (bChoosePointByEnemy)
				{
					int Cnt = AIWorldVolume->QuerySafeOcclusionPoints(
						ControlledPawn, PawnLoc
						, SearchRadius, EnemyActor
						, EnemyTargetLoc, ValidPoints
						, MaxAllowPoints);

					if (ABaseAIController::bAILogOpen)
					{
						UE_LOG(LogTemp, Log,
							TEXT("%p MoveToOcclusion::FindNearestOcclusionPoint, ChoosePointByEnemy Cnt:%d, PawnLoc: %s, EnemyLoc: %s, Radius: %f"),
							AIController, Cnt, *PawnLoc.ToString(), *EnemyTargetLoc.ToString(), SearchRadius);
					}

					if (Cnt > 0)
					{
						if (CurrentCirclePain >= CirclePainThreshold)
						{
							for (int i = 0; i < Cnt; ++i)
							{
								FVector Loc(ValidPoints[i]->Location[0], ValidPoints[i]->Location[1], ValidPoints[i]->Location[2]);
								if (CircleMgr->IsInBlueCircle(Loc) && !CircleMgr->IsInInnerCircle(Loc))
								{
									return ValidPoints[i];
								}
							}
						}
						else
						{
							return ValidPoints[0];
						}
					}
				}
				else
				{
					// 随机选一个在范围内的点
					int Cnt = AIWorldVolume->QueryOcclusionPoints(
						  PawnLoc
						, SearchRadius
						, ValidPoints
						, MaxAllowPoints);

					if (ABaseAIController::bAILogOpen)
					{
						UE_LOG(LogTemp, Log, TEXT("%p MoveToOcclusion::FindNearestOcclusionPoint, ChoosePointRandom Cnt:%d")
							, AIController, Cnt);
					}

					if (Cnt > 0)
					{
						if (CurrentCirclePain >= CirclePainThreshold)
						{
							int32 InCircleValidPointCount = 0;
							const FAIOcclusionPointData* InCircleValidPoints[MaxAllowPoints] = { nullptr };
							for (int i = 0; i < Cnt; ++i)
							{
								FVector Loc(ValidPoints[i]->Location[0], ValidPoints[i]->Location[1], ValidPoints[i]->Location[2]);
								if (CircleMgr->IsInBlueCircle(Loc) && !CircleMgr->IsInInnerCircle(Loc))
								{
									InCircleValidPoints[InCircleValidPointCount++] = ValidPoints[i];
								}
							}
							if (InCircleValidPointCount > 0)
							{
								int Index = FMath::RandRange(0, InCircleValidPointCount - 1);
								return InCircleValidPoints[Index];
							}
						}
						else
						{
							int Index = FMath::RandRange(0, Cnt - 1);
							return ValidPoints[Index];
						}
					}
				}
			}
		}
	}

	return nullptr;
}

bool UBTTask_MoveToOcclusion::FindBestBuildingOcclusionPoint(UBehaviorTreeComponent& OwnerComp
	, uint8* NodeMemory
	, FBuildingOcclusionPointData* data)
{
	const UBlackboardComponent* MyBlackboard = OwnerComp.GetBlackboardComponent();
	if (nullptr == MyBlackboard)
	{
		return false;
	}

	ABasicAIController* AIController = Cast<ABasicAIController>(OwnerComp.GetAIOwner());
	if (nullptr == AIController)
	{
		return false;
	}

	FVector EnemyTargetLoc = FVector::ZeroVector;
	AActor* EnemyActor = nullptr;
	if (EnemyBlackboardKey.SelectedKeyType == UBlackboardKeyType_Object::StaticClass())
	{
		UObject* KeyValue = MyBlackboard->GetValue<UBlackboardKeyType_Object>(EnemyBlackboardKey.GetSelectedKeyID());
		AActor* TargetActor = Cast<AActor>(KeyValue);
		if (TargetActor)
		{
			EnemyActor = TargetActor;
			EnemyTargetLoc = TargetActor->GetActorLocation();
		}
		else
		{
			return false;
		}
	}
	else
	{
		return false;
	}

	UAIHoleUpComponent* AIHoleUpComp = AIController->FindComponentByClass<UAIHoleUpComponent>();
	if (nullptr == AIHoleUpComp)
	{
		UE_LOG(LogBehaviorTree, Error, TEXT("UBTTask_MoveToOcclusion::FindBestBuildingOcclusionPoint AIController is not UAIHoleUpComponent! Please add UAIHoleUpComponent!"));
		return false;
	}
	// 检查掩体是否被破话或者无效
	if (AIHoleUpComp->AIDataTable.BuildingOcclusionPointData.Building.IsValid())
	{
		FBuildingOcclusionPointData* P = &(AIHoleUpComp->AIDataTable.BuildingOcclusionPointData);
		if (!USTBuildingOcclusionPointComponent::CheckOcclusionVisiable(EnemyTargetLoc
			, P->Location, P->Rotation, P->HorizionAngle, P->VerticalAngle)) {
			if (data) {
				data->Location = P->Location;
				data->Rotation = P->Rotation;
				data->HorizionAngle = P->HorizionAngle;
				data->VerticalAngle = P->VerticalAngle;
			}
			return true;
		}
	}

	APawn* ControlledPawn = AIController ? Cast<APawn>(AIController->GetPawn()) : nullptr;
	if (nullptr == ControlledPawn)
	{
		return false;
	}

	const FVector ControlledPawnLoc = ControlledPawn->GetActorLocation();

	// 搜索新的躲避点
	UWorld* CurrentWorld = GetWorld();
	ABattleRoyaleGameMode* CurGameMode = CurrentWorld ? CurrentWorld->GetAuthGameMode<ABattleRoyaleGameMode>() : nullptr;
	if (CurGameMode)
	{
		UCircleMgrComponent* CircleMgr = CurGameMode->GetCircleMgrComponent();
		AAIWorldVolume* AIWorldVolume = CurGameMode ? CurGameMode->AIWorldVolume : nullptr;
		if (nullptr != AIWorldVolume)
		{
			float CurrentCirclePain = CircleMgr? CircleMgr->GetCurrentPain(ControlledPawn) : 0;
			if (ControlledPawn)
			{
				FVector PawnLoc = ControlledPawn->GetActorLocation();
				
				const int32 MaxPointNum = 10;
				FBuildingOcclusionPointData OcclusionPoints[MaxPointNum];
				int Cnt = AIWorldVolume->QueryBuildingOcclusionPoints(PawnLoc
					, SearchRadius, EnemyTargetLoc
					, OcclusionPoints, MaxPointNum);

				FBuildingOcclusionPointData* ValidPoint = nullptr;
				if (Cnt > 0)
				{
					if (CurrentCirclePain >= CirclePainThreshold
						&& CircleMgr)
					{
						for (int i = 0; i < Cnt; ++i)
						{
							if (CircleMgr->IsInBlueCircle(OcclusionPoints[i].Location))
							{
								ValidPoint = &OcclusionPoints[i];
							}
						}
					}
					else
					{
						ValidPoint = &OcclusionPoints[0];
					}
				}

				if (ValidPoint) {
					if (data) {
						data->Building = ValidPoint->Building;
						data->Location = ValidPoint->Location;
						data->Rotation = ValidPoint->Rotation;
						data->HorizionAngle = ValidPoint->HorizionAngle;
						data->VerticalAngle = ValidPoint->VerticalAngle;
					}
					return true;
				}
			}
		}
	}
	return false;
}
