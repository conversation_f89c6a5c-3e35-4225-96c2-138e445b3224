#include "ShadowTrackerExtra.h"
#include "BTTask_Mob_RushTo.h"
#include "BehaviorTree/BlackboardComponent.h"
#include "BehaviorTree/Blackboard/BlackboardKeyType_Object.h"
#include "AI/Controller/MobAIController.h"
#include "Character/STExtraSimpleCharacter.h"
#include "CustomComponent/STExtraSimpleCharacterPhysics.h"

UBTTask_Mob_RushTo::UBTTask_Mob_RushTo(const FObjectInitializer& ObjectInitializer)
	: Super(ObjectInitializer)
{
	RushSpeed = 1000.f;
	HeightOffset = 0.f;
	AcceptableRadius = 5.f;
	bReachTestIncludesAgentRadius = true;
	bReachTestIncludesGoalRadius = true;

	bNotifyTick = true;
	bNotifyTaskFinished = true;

	InRushTarget.AddObjectFilter(this, GET_MEMBER_NAME_CHECKED(UBTTask_Mob_RushTo, InRushTarget), AActor::StaticClass());
}

void UBTTask_Mob_RushTo::InitializeFromAsset(UBehaviorTree& Asset)
{
	Super::InitializeFromAsset(Asset);

	UBlackboardData* BBAsset = GetBlackboardAsset();
	if (BBAsset)
	{
		InRushTarget.ResolveSelectedKey(*BBAsset);
	}
}

EBTNodeResult::Type UBTTask_Mob_RushTo::ExecuteTask(UBehaviorTreeComponent& OwnerComp, uint8* NodeMemory)
{
	AMobAIController* MyController = Cast<AMobAIController>(OwnerComp.GetAIOwner());
	if (!MyController) { return EBTNodeResult::Failed; }
	ASTExtraSimpleCharacter* MyCharacter = Cast<ASTExtraSimpleCharacter>(MyController->GetPawn());
	if (!MyCharacter) { return EBTNodeResult::Failed; }
	USTExtraSimpleCharacterPhysics* SimpleCharacterPhysics = MyCharacter->GetSimpleCharacterPhysics();
	if (!SimpleCharacterPhysics) { return EBTNodeResult::Failed; }

	UBlackboardComponent* BBComp = OwnerComp.GetBlackboardComponent();
	if (!BBComp) { return EBTNodeResult::Failed; }
	APawn* Target = Cast<APawn>(BBComp->GetValue<UBlackboardKeyType_Object>(InRushTarget.GetSelectedKeyID()));
	if (!Target) { return EBTNodeResult::Failed; }
	FVector TargetLoc = Target->GetNavAgentLocation();
	TargetLoc.Z += HeightOffset;

	FVector MyLoc = MyCharacter->GetActorLocation();
	FVector RushDir = (TargetLoc - MyLoc).GetSafeNormal();
	FVector RushVel = RushDir * RushSpeed;
	SimpleCharacterPhysics->StartImpulseSimulate(RushVel, bPreventPenetration);

	return EBTNodeResult::InProgress;
}

void UBTTask_Mob_RushTo::TickTask(UBehaviorTreeComponent& OwnerComp, uint8* NodeMemory, float DeltaSeconds)
{
	Super::TickTask(OwnerComp, NodeMemory, DeltaSeconds);

	AMobAIController* MyController = Cast<AMobAIController>(OwnerComp.GetAIOwner());
	if (!MyController)
	{
		return FinishLatentTask(OwnerComp, EBTNodeResult::Failed);
	}
	ASTExtraSimpleCharacter* MyCharacter = Cast<ASTExtraSimpleCharacter>(MyController->GetPawn());
	if (!MyCharacter)
	{
		return FinishLatentTask(OwnerComp, EBTNodeResult::Failed);
	}
	USTExtraSimpleCharacterPhysics* SimpleCharacterPhysics = MyCharacter->GetSimpleCharacterPhysics();
	if (!SimpleCharacterPhysics)
	{
		return FinishLatentTask(OwnerComp, EBTNodeResult::Failed);
	}
	if (SimpleCharacterPhysics->GetSimulatePhysicsType() == ESimulatePhysicsType::NONE)
	{
		return FinishLatentTask(OwnerComp, EBTNodeResult::Succeeded);
	}

	UBlackboardComponent* BBComp = OwnerComp.GetBlackboardComponent();
	if (!BBComp)
	{
		return FinishLatentTask(OwnerComp, EBTNodeResult::Failed);
	}
	APawn* Target = Cast<APawn>(BBComp->GetValue<UBlackboardKeyType_Object>(InRushTarget.GetSelectedKeyID()));
	if (!Target)
	{
		return FinishLatentTask(OwnerComp, EBTNodeResult::Failed);
	}
	FVector TargetLoc = Target->GetNavAgentLocation();

	float AgentRadius = 0.f;
	if (bReachTestIncludesAgentRadius)
	{
		AgentRadius = MyCharacter->GetSimpleCollisionRadius();
	}
	float GoalRadius = 0.f;
	if (bReachTestIncludesGoalRadius)
	{
		GoalRadius = Target->GetSimpleCollisionRadius();
	}

	if (HasReachedTarget(MyCharacter->GetActorLocation(), Target->GetNavAgentLocation(), AgentRadius, GoalRadius))
	{
		return FinishLatentTask(OwnerComp, EBTNodeResult::Succeeded);
	}
}

bool UBTTask_Mob_RushTo::HasReachedTarget(const FVector& AgentLoc, const FVector& GoalLoc, float AgentRadius, float GoalRadius) const
{
	const FVector ToGoal = GoalLoc - AgentLoc;

	const float Dist2DSq = ToGoal.SizeSquared2D();
	const float UseRadius = AgentRadius + GoalRadius + AcceptableRadius;
	if (Dist2DSq > FMath::Square(UseRadius))
	{
		return false;
	}

	return true;
}

void UBTTask_Mob_RushTo::OnTaskFinished(UBehaviorTreeComponent& OwnerComp, uint8* NodeMemory, EBTNodeResult::Type TaskResult)
{
	Super::OnTaskFinished(OwnerComp, NodeMemory, TaskResult);

	AMobAIController* MyController = Cast<AMobAIController>(OwnerComp.GetAIOwner());
	ASTExtraSimpleCharacter* MyCharacter = MyController ? Cast<ASTExtraSimpleCharacter>(MyController->GetPawn()) : nullptr;
	USTExtraSimpleCharacterPhysics* SimpleCharacterPhysics = MyCharacter ? MyCharacter->GetSimpleCharacterPhysics() : nullptr;
	if (SimpleCharacterPhysics)
	{
		SimpleCharacterPhysics->StopImpulseSimulate();
	}
}