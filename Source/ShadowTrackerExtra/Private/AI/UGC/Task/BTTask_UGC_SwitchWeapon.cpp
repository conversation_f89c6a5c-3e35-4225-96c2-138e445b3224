#include "ShadowTrackerExtra.h"
#include "BTTask_UGC_SwitchWeapon.h"
#include "Character/STExtraBaseCharacter.h"
#include "AIController.h"

UBTTask_UGC_SwitchWeapon::UBTTask_UGC_SwitchWeapon(const FObjectInitializer& ObjectInitializer)
	: Super(ObjectInitializer)
{
	NodeName = TEXT("切换武器");

	bNotifyTick = true;
}

EBTNodeResult::Type UBTTask_UGC_SwitchWeapon::ExecuteTask(UBehaviorTreeComponent& OwnerComp, uint8* NodeMemory)
{
	AAIController* MyController = OwnerComp.GetAIOwner();
	if (!MyController)
	{
		return EBTNodeResult::Failed;
	}
	ASTExtraBaseCharacter* MyCharacter = Cast<ASTExtraBaseCharacter>(MyController->GetPawn());
	if (!My<PERSON>haracter)
	{
		return EBTNodeResult::Failed;
	}

	FBTTaskUGCSwitchWeaponMemory* MyMemory = (FBTTaskUGCSwitchWeaponMemory*)NodeMemory;
	if (!MyMemory)
	{
		return EBTNodeResult::Failed;
	}

	MyCharacter->SwitchWeaponBySlot(WeaponSlot, bUseAnimation, bForceFinishPreviousSwitch);
	if (MyCharacter->HasState(EPawnState::SwitchWeapon))
	{
		MyMemory->CurSwitchTime = SwitchTimeout;
		return EBTNodeResult::InProgress;
	}

	return EBTNodeResult::Failed;
}

void UBTTask_UGC_SwitchWeapon::TickTask(UBehaviorTreeComponent& OwnerComp, uint8* NodeMemory, float DeltaSeconds)
{
	AAIController* MyController = OwnerComp.GetAIOwner();
	if (!MyController)
	{
		return FinishLatentTask(OwnerComp, EBTNodeResult::Failed);
	}
	ASTExtraBaseCharacter* MyCharacter = Cast<ASTExtraBaseCharacter>(MyController->GetPawn());
	if (!MyCharacter)
	{
		return FinishLatentTask(OwnerComp, EBTNodeResult::Failed);
	}

	FBTTaskUGCSwitchWeaponMemory* MyMemory = (FBTTaskUGCSwitchWeaponMemory*)NodeMemory;
	if (!MyMemory)
	{
		return FinishLatentTask(OwnerComp, EBTNodeResult::Failed);
	}

	MyMemory->CurSwitchTime -= DeltaSeconds;
	if (!MyCharacter->HasState(EPawnState::SwitchWeapon))
	{
		MyMemory->CurSwitchTime = FMath::Min(MyMemory->CurSwitchTime, StateEndDelayTime);
	}

	if (MyMemory->CurSwitchTime <= 0.f)
	{
		return FinishLatentTask(OwnerComp, EBTNodeResult::Succeeded);
	}
}