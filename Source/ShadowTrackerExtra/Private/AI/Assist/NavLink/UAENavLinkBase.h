// Fill out your copyright notice in the Description page of Project Settings.

#pragma once

#include "CoreMinimal.h"
#include "UAENavLinkDefine.h"
#include "AI/Navigation/NavigationSystem.h"


class SHADOWTRACKEREXTRA_API AUAENavLinkBase
{
public:
    AUAENavLinkBase();
    virtual ~AUAENavLinkBase();
	

public:
    /** Check if link allows path finding
     *  Querier is usually an AIController trying to find path
     */
    virtual bool IsLinkPathfindingAllowed(const UObject* Querier) const { return true; }

	virtual void SetNavLinkData(uint8* InData, ENavLinkType LinkType) { Data = InData; NavLinkType = LinkType; }
	uint8* GetNavLinkData() { return Data; }
	ENavLinkType GetNavLinkType() const { return NavLinkType; }

    void ClearNavLinkData() { Data = nullptr; };

    bool OnLinkMoveStarted(UPathFollowingComponent* PathComp, const FVector& DestPoint);
    void OnLinkMoveFinished(UPathFollowingComponent* PathComp);

	FVector TransformPosition(const FVector& ActorLocation, const FRotator& ActorRotation, const FVector& ActorScale
		, const FVector& V) const;

	FVector TransformPositionNoScale(const FVector& ActorLocation, const FRotator& ActorRotation
		, const FVector& V) const;

protected:
    virtual bool OnNotifyLinkMoveStarted(AActor* MovingActor, const FVector& DestPoint) { return false; };
    //virtual void OnNotifyLinkMoveFinished(AActor* MovingActor);

protected:
	uint8* Data = nullptr;

	ENavLinkType NavLinkType = ENavLinkType::ENavLinkType_NavLinkProxy;

    /** list of agents moving though this link */
    TArray<TWeakObjectPtr<UPathFollowingComponent> > MovingAgents;
};