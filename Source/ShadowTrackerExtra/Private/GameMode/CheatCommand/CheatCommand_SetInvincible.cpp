// Fill out your copyright notice in the Description page of Project Settings.

#include "ShadowTrackerExtra.h"
#include "Player/STExtraPlayerCharacter.h"
#include "Player/STExtraPlayerController.h"
#include "CheatCommand_SetInvincible.h"


bool UCheatCommand_SetInvincible::IsPassCommandParamCheck_Implementation(const FCheatCommandInfo& InCommandInfo, ASTExtraPlayerController* InCommandTriggerController)
{
	return (InCommandInfo.CommandContent_int32Type.Num() > 0);
}

ECheatCommandResultType UCheatCommand_SetInvincible::DoExecuteCommand_Implementation(const FCheatCommandInfo& InCommandInfo, ASTExtraPlayerController* InCommandTriggerController)
{
	check(InCommandTriggerController);
#if !(UE_BUILD_SHIPPING || UE_BUILD_TEST)
	UE_LOG(LogCheatCommand, Log, TEXT("UCheatCommand_SetInvincible::DoExecuteCommand 【CommandTrigger:%s】CommandInfo:%s"), *InCommandTriggerController->PlayerName, *InCommandInfo.ToString());
#endif

	ASTExtraBaseCharacter* TriggerChar = InCommandTriggerController->GetPlayerCharacterSafety();
	if (!TriggerChar)
	{
#if !(UE_BUILD_SHIPPING || UE_BUILD_TEST)
		UE_LOG(LogCheatCommand, Log, TEXT("UCheatCommand_SetInvincible::DoExecuteCommand 【Invalid TriggerChar!!!, CommandTrigger:%s】CommandInfo:%s"), *InCommandTriggerController->PlayerName, *InCommandInfo.ToString());
#endif
		return ECheatCommandResultType::ECCRT_Failed_Unknown;
	}

	bool bIsInvincible = UnPackCommandParams(InCommandInfo);
	if (TriggerChar->IsInvincible() != bIsInvincible)
	{
		TriggerChar->SetInvincible(bIsInvincible);
	}

	return ECheatCommandResultType::ECCRT_Success;
}

void UCheatCommand_SetInvincible::PackCommandParams(FCheatCommandInfo& InCommandInfo, const int32 InIsInvincible)
{
	check(InCommandInfo.CommandContent_int32Type.Num() == 0);

	InCommandInfo.CommandType = ECheatCommandType::ECCT_SetInvincible;
	InCommandInfo.CommandContent_int32Type.Add(InIsInvincible);
}

bool UCheatCommand_SetInvincible::UnPackCommandParams(const FCheatCommandInfo& InCommandInfo)
{
	check(InCommandInfo.CommandType == ECheatCommandType::ECCT_SetInvincible);
	check(InCommandInfo.CommandContent_int32Type.Num() > 0);

	bool bInvincible = false;
	if (InCommandInfo.CommandContent_int32Type.Num())
	{
		bInvincible = (InCommandInfo.CommandContent_int32Type[0] != 0);
	}

	return bInvincible;
}
