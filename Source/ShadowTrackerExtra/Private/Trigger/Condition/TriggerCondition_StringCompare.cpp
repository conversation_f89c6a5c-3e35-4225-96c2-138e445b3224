// Fill out your copyright notice in the Description page of Project Settings.

#include "ShadowTrackerExtra.h"
#include "TriggerCondition_StringCompare.h"
#include "Assit/UAETriggerParam.h"


UTriggerCondition_StringCompare::UTriggerCondition_StringCompare(const FObjectInitializer& ObjectInitializer)
	:Super(ObjectInitializer)
{

}

bool UTriggerCondition_StringCompare::IsSatisfy(const UTriggerEvent* Event)
{
	FString LeftCompareValue = TEXT("");
	bool Result = GetParamValueSafe<FString>(ConditionParam_Param1, LeftCompareValue);
	if (!Result)
	{
		UE_LOG(LogLevelTrigger, Warning, TEXT("UTriggerCondition_StringCompare::IsSatisfy     ##########  Can't get param [%s]   ########## "), ConditionParam_Param1);
		return false;
	}

	FString RightCompareValue = TEXT("");
	Result = GetParamValueSafe<FString>(ConditionParam_Param2, RightCompareValue);
	if (!Result)
	{
		UE_LOG(LogLevelTrigger, Warning, TEXT("UTriggerCondition_StringCompare::IsSatisfy     ##########  Can't get param [%s]   ########## "), ConditionParam_Param2);
		return false;
	}

	UAETParamCompareType::Type CompareType = UAETParamCompareType::NotEqual;
	Result = GetParamValueSafe<UAETParamCompareType::Type>(ConditionParam_ParamOperator, CompareType);
	if (!Result)
	{
		UE_LOG(LogLevelTrigger, Warning, TEXT("UTriggerCondition_StringCompare::IsSatisfy     ##########  Can't get param [%s]   ########## "), ConditionParam_ParamOperator);
		return false;
	}

	switch (CompareType)
	{
	case UAETParamCompareType::Equal:
		return LeftCompareValue == RightCompareValue;

	case UAETParamCompareType::NotEqual:
		return LeftCompareValue != RightCompareValue;

	default:
		;
	}

	return false;
}

