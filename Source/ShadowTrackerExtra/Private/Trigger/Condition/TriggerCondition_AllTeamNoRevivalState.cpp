// Fill out your copyright notice in the Description page of Project Settings.

#include "ShadowTrackerExtra.h"
#include "TriggerCondition_AllTeamNoRevivalState.h"
#include "SubSystem/GMComponentManager.h"

UTriggerCondition_AllTeamNoRevivalState::UTriggerCondition_AllTeamNoRevivalState(const FObjectInitializer& ObjectInitializer)
	:Super(ObjectInitializer)
{

}

bool UTriggerCondition_AllTeamNoRevivalState::IsSatisfy(const UTriggerEvent* Event)
{
	int32 CurrentTeamID = -1;
	if (!GetParamValueSafe<int32>(ActionParam_TeamId, CurrentTeamID))
	{
		UE_LOG(LogLevelTrigger, Warning, TEXT("UTriggerCondition_AllTeamNoRevivalState::IsSatisfy     ##########  Can't get param [%s]   ########## "), ActionParam_TeamId);
		return false;
	}

	bool bResult = UGMComponentManager::GetInstance()->InvokeFunctionWithReturn<bool>(ESubSystemType::ESS_RecallTeammate, TEXT("IsAllTeamWithoutRevivalState"), CurrentTeamID);
#if UE_BUILD_DEVELOPMENT
	UE_LOG(LogLevelTrigger, Log, TEXT("UTriggerCondition_AllTeamNoRevivalState::IsSatisfy     ##########  IsAllTeamWithoutRevivalSign return %d  ########## "), int32(bResult));
#endif
	return bResult;
}



