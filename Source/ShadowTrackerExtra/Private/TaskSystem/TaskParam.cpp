// Copyright 1998-2017 Epic Games, Inc. All Rights Reserved.
#include "ShadowTrackerExtra.h"
#include "TaskParam.h"
#include "UAEPlayerState.h"
#include "UniversalTask.h"
#include "UniversalTaskNode.h"
#include "UAEGameState.h"
#include "UAEPlayerState.h"

class UUniversalTask* UTaskParamBase::GetTaskObject()
{
	UUniversalTaskNode* OwnerNode = Cast<UUniversalTaskNode>(GetOuter());

	if (OwnerNode)
	{
		return OwnerNode->GetOwner();
	}

	return nullptr;
}


bool UTaskParam_Input::GetParamValue(int32& OutValue)
{
	OutValue = FCString::Atoi(*Value);
	return true;
}

bool UTaskParam_Input::GetParamValue(uint32& OutValue)
{
	OutValue = FCString::Atoi64(*Value);
	return true;
}

bool UTaskParam_Input::GetParamValue(float& OutValue)
{
	OutValue = FCString::Atof(*Value);
	return true;
}

bool UTaskParam_Input::GetParamValue(bool& OutValue)
{
	OutValue = FCString::ToBool(*Value);
	return true;
}

bool UTaskParam_Input::GetParamValue(FString& OutValue)
{
	OutValue = Value;
	return true;
}

bool UTaskParam_Input::GetParamValue(FName& OutValue)
{
	OutValue = FName(OutValue);
	return true;
}

bool UTaskParam_Input::GetParamValue(FVector& OutValue)
{
	OutValue.InitFromString(Value);
	return true;
}

bool UTaskParam_Input::GetParamValue(FRotator& OutValue)
{
	OutValue.InitFromString(Value);
	return true;
}

UClass* UTaskParam_EventParam::GetEventFuncLibraryClass()
{
	//ITriggerEventWrapperInterface* OwnerInterface = Cast<ITriggerEventWrapperInterface>(GetOuter());
	ITriggerEventWrapperInterface* OwnerInterface = Cast<ITriggerEventWrapperInterface>(GetTaskObject());
	if (OwnerInterface)
	{
		return OwnerInterface->GetEventFuncLibraryClass();
	}

	return ITriggerEventWrapperInterface::GetEventFuncLibraryClass();
}

FName UTaskParam_EventParam::GetUsedEventFunctionName()
{
	UUniversalTask* TaskObj = GetTaskObject();

	if (TaskObj)
	{
		return TaskObj->GetUsedEventFunctionName();
	}

	return ITriggerEventWrapperInterface::GetUsedEventFunctionName();
}

bool UTaskParam_EventParam::GetParamValue(UObject* OutValue)
{
	UUniversalTask* TaskObject = GetTaskObject();

	if (TaskObject)
	{
		TaskObject->GetEventParameterObject(ParamKeySelector.SelectedKeyName, OutValue);
		return true;
	}

	return false;
}

bool UTaskParam_EventParam::GetParamValue(int32& OutValue)
{
	UUniversalTask* TaskObject = GetTaskObject();

	if (TaskObject)
	{
		TaskObject->GetEventParameterInt32(ParamKeySelector.SelectedKeyName, OutValue);
		return true;
	}

	return false;
}

bool UTaskParam_EventParam::GetParamValue(uint32& OutValue)
{
	UUniversalTask* TaskObject = GetTaskObject();

	if (TaskObject)
	{
		TaskObject->GetEventParameterUInt32(ParamKeySelector.SelectedKeyName, OutValue);
		return true;
	}

	return false;
}

bool UTaskParam_EventParam::GetParamValue(float& OutValue)
{
	UUniversalTask* TaskObject = GetTaskObject();

	if (TaskObject)
	{
		TaskObject->GetEventParameterFloat(ParamKeySelector.SelectedKeyName, OutValue);
		return true;
	}

	return false;
}

bool UTaskParam_EventParam::GetParamValue(bool& OutValue)
{
	UUniversalTask* TaskObject = GetTaskObject();

	if (TaskObject)
	{
		TaskObject->GetEventParameterBool(ParamKeySelector.SelectedKeyName, OutValue);
		return true;
	}

	return false;
}

bool UTaskParam_EventParam::GetParamValue(FString& OutValue)
{
	UUniversalTask* TaskObject = GetTaskObject();

	if (TaskObject)
	{
		TaskObject->GetEventParameterString(ParamKeySelector.SelectedKeyName, OutValue);
		return true;
	}

	return false;
}

bool UTaskParam_EventParam::GetParamValue(FVector& OutValue)
{
	UUniversalTask* TaskObject = GetTaskObject();

	if (TaskObject)
	{
		TaskObject->GetEventParameterVector(ParamKeySelector.SelectedKeyName, OutValue);
		return true;
	}

	return false;
}

// void UTaskParam_CacheBlackboardParam::FetchBlackboardParamNameList(TArray<FName>& NameList)
// {
// #if UE_EDITOR
// 	UUniversalTask* TaskObj = GetTaskObject();
// 
// 	if (TaskObj)
// 	{
// 		TaskObj->FetchBlackboardParamNameList(NameList);
// 	}
// #endif
// }

bool UTaskParam_CacheBlackboardParam::GetParamValue(UObject* OutValue)
{
	UUniversalTask* TaskObject = GetTaskObject();

	if (TaskObject && TaskObject->GetTaskBlackboard())
	{
		//黑板名称不会重复
		if (TaskObject->GetTaskBlackboard()->IsExistObject(BBKeySelector.SelectedKeyName))
		{
			OutValue = TaskObject->GetTaskBlackboard()->GetValueAsObject(BBKeySelector.SelectedKeyName);
		}
		//弱引用
		if (TaskObject->GetTaskBlackboard()->IsExistWeakObject(BBKeySelector.SelectedKeyName))
		{
			OutValue = TaskObject->GetTaskBlackboard()->GetValueAsWeakObject(BBKeySelector.SelectedKeyName);
		}
		return true;
	}
	return false;
}

bool UTaskParam_CacheBlackboardParam::GetParamValue(int32& OutValue)
{
	UUniversalTask* TaskObject = GetTaskObject();
	
	if (TaskObject && TaskObject->GetTaskBlackboard())
	{
		if (TaskObject->GetTaskBlackboard()->IsExistInt(BBKeySelector.SelectedKeyName))
		{
			OutValue = TaskObject->GetTaskBlackboard()->GetValueAsInt(BBKeySelector.SelectedKeyName);
		}
		return true;
	}
	return false;
}

bool UTaskParam_CacheBlackboardParam::GetParamValue(uint32& OutValue)
{
	UUniversalTask* TaskObject = GetTaskObject();

	if (TaskObject && TaskObject->GetTaskBlackboard())
	{
		if (TaskObject->GetTaskBlackboard()->IsExistUInt(BBKeySelector.SelectedKeyName))
		{
			OutValue = TaskObject->GetTaskBlackboard()->GetValueAsUInt(BBKeySelector.SelectedKeyName);
		}
		return true;
	}
	return false;
}

bool UTaskParam_CacheBlackboardParam::GetParamValue(float& OutValue)
{
	UUniversalTask* TaskObject = GetTaskObject();

	if (TaskObject && TaskObject->GetTaskBlackboard())
	{
		if (TaskObject->GetTaskBlackboard()->IsExistFloat(BBKeySelector.SelectedKeyName))
		{
			OutValue = TaskObject->GetTaskBlackboard()->GetValueAsFloat(BBKeySelector.SelectedKeyName);
		}
		return true;
	}
	return false;
}

bool UTaskParam_CacheBlackboardParam::GetParamValue(bool& OutValue)
{
	UUniversalTask* TaskObject = GetTaskObject();

	if (TaskObject && TaskObject->GetTaskBlackboard())
	{
		if (TaskObject->GetTaskBlackboard()->IsExistBool(BBKeySelector.SelectedKeyName))
		{
			OutValue = TaskObject->GetTaskBlackboard()->GetValueAsBool(BBKeySelector.SelectedKeyName);
		}
		return true;
	}
	return false;
}

bool UTaskParam_CacheBlackboardParam::GetParamValue(FString& OutValue)
{
	UUniversalTask* TaskObject = GetTaskObject();

	if (TaskObject && TaskObject->GetTaskBlackboard())
	{
		if (TaskObject->GetTaskBlackboard()->IsExistString(BBKeySelector.SelectedKeyName))
		{
			OutValue = TaskObject->GetTaskBlackboard()->GetValueAsString(BBKeySelector.SelectedKeyName);
		}
		return true;
	}
	return false;
}

bool UTaskParam_CacheBlackboardParam::GetParamValue(FName& OutValue)
{
	UUniversalTask* TaskObject = GetTaskObject();

	if (TaskObject && TaskObject->GetTaskBlackboard())
	{
		if (TaskObject->GetTaskBlackboard()->IsExistName(BBKeySelector.SelectedKeyName))
		{
			OutValue = TaskObject->GetTaskBlackboard()->GetValueAsName(BBKeySelector.SelectedKeyName);
		}
		return true;
	}
	return false;
}

bool UTaskParam_CacheBlackboardParam::GetParamValue(FVector& OutValue)
{
	UUniversalTask* TaskObject = GetTaskObject();

	if (TaskObject && TaskObject->GetTaskBlackboard())
	{
		if (TaskObject->GetTaskBlackboard()->IsExistVector(BBKeySelector.SelectedKeyName))
		{
			OutValue = TaskObject->GetTaskBlackboard()->GetValueAsVector(BBKeySelector.SelectedKeyName);
		}
		return true;
	}
	return false;
}

bool UTaskParam_CacheBlackboardParam::GetParamValue(FRotator& OutValue)
{
	UUniversalTask* TaskObject = GetTaskObject();

	if (TaskObject && TaskObject->GetTaskBlackboard())
	{
		if (TaskObject->GetTaskBlackboard()->IsExistRotator(BBKeySelector.SelectedKeyName))
		{
			OutValue = TaskObject->GetTaskBlackboard()->GetValueAsRotator(BBKeySelector.SelectedKeyName);
		}
		return true;
	}
	return false;
}
bool UTaskParam_TableParam::GetParamValue(int32& OutValue)
{
	UUniversalTask* TaskObject = GetTaskObject();

	if (TaskObject)
	{
		return TaskObject->GetTableParameterInt32(OutValue, TaskTableParamType);
	}

	return false;
}

bool UTaskParam_TableParam::GetParamValue(float& OutValue)
{
	int32 IntValue = 0;
	if (GetParamValue(IntValue))
	{
		OutValue = (float)IntValue;
		return true;
	}
	return false;
}

bool UTaskParam_TableParam::GetParamValue(FString& OutValue)
{
	UUniversalTask* TaskObject = GetTaskObject();

	if (TaskObject)
	{
		return TaskObject->GetTableParameterString(OutValue, TaskTableParamType);
	}

	return false;
}


void UTaskParam_ObjectCacheChildProperty::GetBlackboardNameList(TArray<FName>& NameList)
{
#if UE_EDITOR
	UUniversalTask* TaskObj = GetTaskObject();

	if (TaskObj)
	{
		TaskObj->GetBlackboardNameList(NameList);
	}
#endif
}

bool UTaskParam_ObjectCacheChildProperty::GetParamValue(UObject* OutValue)
{
	void* PropertyMemory = GetSelectedPropertyMemory();

	if (PropertyMemory)
	{
		OutValue = (UObject*)PropertyMemory;
		return true;
	}
	return false;
}

bool UTaskParam_ObjectCacheChildProperty::GetParamValue(int32& OutValue)
{
	void* PropertyMemory = GetSelectedPropertyMemory();

	if (PropertyMemory)
	{
		OutValue = *(int32*)PropertyMemory;
		return true;
	}

	return false;
}

bool UTaskParam_ObjectCacheChildProperty::GetParamValue(uint32& OutValue)
{
	if (ObjectCacheChildPropertyType == ETaskParamType::EPT_PlayerKey)
	{
		if (AUAEPlayerState* PS = Cast<AUAEPlayerState>(GetSelectedObjectCache()))
		{
			OutValue = PS->PlayerKey;
			return true;
		}
// 		else if (AUAEPlayerController* PC = Cast<AUAEPlayerController>(GetSelectedObjectCache()))
// 		{
// 			OutValue = PC->PlayerKey;
// 			return true;
// 		}
	}

	void* PropertyMemory = GetSelectedPropertyMemory();

	if (PropertyMemory)
	{
		OutValue = *(uint32*)PropertyMemory;
		return true;
	}

	return false;
}

bool UTaskParam_ObjectCacheChildProperty::GetParamValue(float& OutValue)
{
	void* PropertyMemory = GetSelectedPropertyMemory();

	if (PropertyMemory)
	{
		OutValue = *(float*)PropertyMemory;
		return true;
	}

	return false;
}

bool UTaskParam_ObjectCacheChildProperty::GetParamValue(bool& OutValue)
{
	void* PropertyMemory = GetSelectedPropertyMemory();

	if (PropertyMemory)
	{
		OutValue = *(bool*)PropertyMemory;
		return true;
	}

	return false;
}

bool UTaskParam_ObjectCacheChildProperty::GetParamValue(FString& OutValue)
{
	void* PropertyMemory = GetSelectedPropertyMemory();

	if (PropertyMemory)
	{
		OutValue = *(FString*)PropertyMemory;
		return true;
	}

	return false;
}

bool UTaskParam_ObjectCacheChildProperty::GetParamValue(FName& OutValue)
{
	void* PropertyMemory = GetSelectedPropertyMemory();

	if (PropertyMemory)
	{
		OutValue = *(FName*)PropertyMemory;
		return true;
	}

	return false;
}

bool UTaskParam_ObjectCacheChildProperty::GetParamValue(FVector& OutValue)
{
	void* PropertyMemory = GetSelectedPropertyMemory();

	if (PropertyMemory)
	{
		OutValue = *(FVector*)PropertyMemory;
		return true;
	}

	return false;
}

bool UTaskParam_ObjectCacheChildProperty::GetParamValue(FRotator& OutValue)
{
	void* PropertyMemory = GetSelectedPropertyMemory();

	if (PropertyMemory)
	{
		OutValue = *(FRotator*)PropertyMemory;
		return true;
	}

	return false;
}

UObject* UTaskParam_ObjectCacheChildProperty::GetSelectedObjectCache()
{
	UUniversalTask* TaskObject = GetTaskObject();

	if (TaskObject == nullptr && GetOuter())
	{
		UUniversalTaskNode* OwnerNode = Cast<UUniversalTaskNode>(GetOuter()->GetOuter());
		if (OwnerNode)
		{
			TaskObject = OwnerNode->GetOwner();
		}
	}
	if (TaskObject && TaskObject->GetTaskBlackboard())
	{
		//return TaskObject->GetTaskBlackboard()->GetValueAsObject(BBKeySelector.SelectedKeyName);
		return TaskObject->GetTaskBlackboard()->GetValueAsWeakObject(BBKeySelector.SelectedKeyName);
	}
	return nullptr;
}

UClass* UTaskParam_ObjectCacheChildProperty::GetSelectedObjectCacheClass()
{
	UObject* ClassPackage = ANY_PACKAGE;

	return FindObject<UClass>(ClassPackage, *ObjectCacheClassSelector.SelectedKeyName.ToString());
}

FProperty* UTaskParam_ObjectCacheChildProperty::GetSelectedObjectCacheProperty()
{
	UClass* ObjectClass = GetSelectedObjectCacheClass();

	for (FProperty* Property : TFieldRange<FProperty>(ObjectClass, EFieldIteratorFlags::ExcludeSuper))
	{
		if (Property && Property->GetName() == *ObjectCachePropertySelector.SelectedKeyName.ToString())
		{
			return Property;
		}
	}

	return nullptr;
}


void* UTaskParam_ObjectCacheChildProperty::GetSelectedPropertyMemory()
{
	FProperty* Property = GetSelectedObjectCacheProperty();
	UObject* ObjectCache = GetSelectedObjectCache();

	if (Property && ObjectCache)
	{
		return Property->ContainerPtrToValuePtr<void>(ObjectCache);
	}
	return nullptr;
}

#if UE_EDITOR
TArray<FName> UTaskParam_ObjectCacheChildProperty::GetSelectablePropertyList(FProperty* StructProperty)
{

	TArray<FName> SelectableList;

	if (StructProperty)
	{
		if (StructProperty->GetName() == TEXT("ObjectCacheClassSelector"))
		{
			GetObjectCacheClassSelectableList(SelectableList);
		}
		else if (StructProperty->GetName() == TEXT("ObjectCachePropertySelector"))
		{
			GetObjectCachePropertySelectableList(SelectableList);
		}
	}

	return SelectableList;
}

UClass* UTaskParam_ObjectCacheChildProperty::GetBaseClass()
{
	UClass* BaseClass = nullptr;

	if (TaskObjectCacheType == ETaskObjectCacheType::PlayerState)
	{
		BaseClass = AUAEPlayerState::StaticClass();
	}
	if (TaskObjectCacheType == ETaskObjectCacheType::GameState)
	{
		BaseClass = AUAEGameState::StaticClass();
	}

	return BaseClass;
}

FFieldClass* UTaskParam_ObjectCacheChildProperty::GetPropertyClass()
{
	if (ObjectCacheChildPropertyType == ETaskParamType::EPT_Object)
	{
		return FObjectProperty::StaticClass();
	}
	else if (ObjectCacheChildPropertyType == ETaskParamType::EPT_Int)
	{
		return FIntProperty::StaticClass();
	}
	else if (ObjectCacheChildPropertyType == ETaskParamType::EPT_UInt)
	{
		return FUInt32Property::StaticClass();
	}
	else if (ObjectCacheChildPropertyType == ETaskParamType::EPT_Float)
	{
		return FFloatProperty::StaticClass();
	}
	else if (ObjectCacheChildPropertyType == ETaskParamType::EPT_Bool)
	{
		return FBoolProperty::StaticClass();
	}
	else if (ObjectCacheChildPropertyType == ETaskParamType::EPT_String)
	{
		return FStrProperty::StaticClass();
	}
	else if (ObjectCacheChildPropertyType == ETaskParamType::EPT_Name)
	{
		return FNameProperty::StaticClass();
	}
	else if (ObjectCacheChildPropertyType == ETaskParamType::EPT_Vector)
	{
		return FStructProperty::StaticClass();
	}
	else if (ObjectCacheChildPropertyType == ETaskParamType::EPT_Rotator)
	{
		return FStructProperty::StaticClass();
	}
	return nullptr;
}

void UTaskParam_ObjectCacheChildProperty::GetObjectCacheClassSelectableList(TArray<FName>& OutData)
{
	UClass* BaseClass = GetBaseClass();

	if (BaseClass)
	{
		OutData.Add(BaseClass->GetFName());

		TArray<UClass*> DerivedClassList;
		GetDerivedClasses(BaseClass, DerivedClassList);

		for (UClass* DerivedClass : DerivedClassList)
		{
			if (DerivedClass)
			{
				OutData.Add(DerivedClass->GetFName());
			}
		}
	}
}


void UTaskParam_ObjectCacheChildProperty::GetObjectCachePropertySelectableList(TArray<FName>& OutData)
{
	UClass* ObjectClass = GetSelectedObjectCacheClass();

	FFieldClass* PropertyClass = GetPropertyClass();

	if (ObjectClass && PropertyClass)
	{
		for (FProperty* Property : TFieldRange<FProperty>(ObjectClass, EFieldIteratorFlags::ExcludeSuper))
		{
			if (Property)
			{
#if UE5_OR_LATER
				if (!Property->GetClass()->HasAllCastFlags(PropertyClass->GetCastFlags()))
#else
				if (!Property->GetClass()->HasAllCastFlags(PropertyClass->ClassCastFlags))
#endif
					break;

				if (ObjectCacheChildPropertyType == ETaskParamType::EPT_Vector)
				{
					if (Property->GetCPPType() != TEXT("FVector"))
						break;
				}
				else if (ObjectCacheChildPropertyType == ETaskParamType::EPT_Rotator)
				{
					if (Property->GetCPPType() != TEXT("FRotator"))
						break;
				}

				OutData.AddUnique(Property->GetFName());
			}
		}
	}
}

#endif
