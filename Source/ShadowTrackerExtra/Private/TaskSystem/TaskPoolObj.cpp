// Fill out your copyright notice in the Description page of Project Settings.

#include "ShadowTrackerExtra.h"
#include "TaskPoolObj.h"
#include "Event/LevelEventCenter.h"
#include "CustomComponent/DynamicSpawnComponent.h"

ATaskPoolObj::ATaskPoolObj(const FObjectInitializer& ObjectInitializer)
	:Super(ObjectInitializer)
{
	PrimaryActorTick.bCanEverTick = false;

	bReplicates = false;
	bNetLoadOnClient = false;	
}

void ATaskPoolObj::EndPlay(const EEndPlayReason::Type EndPlayReason)
{
	Super::EndPlay(EndPlayReason);
}

bool ATaskPoolObj::GetIntParametersFromDataTable(int32 InTaskID, int32 ParamIndex, int32& ParamValue)
{
	if (!TableParamCache.Contains(InTaskID))
	{
		if (!CacheTableParameter(InTaskID))
		{
			return false;
		}
	}

	if (TableParamCache.Contains(InTaskID) && ParamIndex < TableParamCache[InTaskID].IntParamList.Num())
	{
		ParamValue = TableParamCache[InTaskID].IntParamList[ParamIndex];
		return true;
	}

	return false;
}

bool ATaskPoolObj::GetStringParametersFromDataTable(int32 InTaskID, int32 ParamIndex, FString& ParamValue)
{
	if (!TableParamCache.Contains(InTaskID))
	{
		if (!CacheTableParameter(InTaskID))
		{
			return false;
		}
	}
	if (TableParamCache.Contains(InTaskID) && ParamIndex < TableParamCache[InTaskID].StringParamList.Num())
	{
		ParamValue = TableParamCache[InTaskID].StringParamList[ParamIndex];
		return true;
	}
	return false;
}

bool ATaskPoolObj::CacheTableParameter(int32 InTaskID)
{
	TArray<int32> IntParams;
	TArray<FString> StringParams;
	int32 IntParam1 = 0, IntParam2 = 0, IntParam3 = 0;
	if (GetTaskParamsFromDataTableBlueprint(InTaskID, IntParams, StringParams))
	{
		FTaskParameterCache CacheItem;
		CacheItem.IntParamList.Append(IntParams);
		CacheItem.StringParamList.Append(StringParams);
		TableParamCache.Add(InTaskID, CacheItem);
		return true;
	}
	return false;
}