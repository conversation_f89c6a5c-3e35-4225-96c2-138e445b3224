#include "ShadowTrackerExtra.h"
#include "UAESkillAction_StopSkill.h"
#include "Player/STExtraPlayerCharacter.h"

#if WITH_UGC_EDITOR
void UUAESkillAction_StopSkill::LocalizeUgcUProperty()
{
	Super::LocalizeUgcUProperty();
	StaticClass()->FindPropertyByName(TEXT("IsStopAllSkill"))->SetMetaData(TEXT("DisplayName"), *NSLOCTEXT("IsStopAllSkill", "UgcSkillEditor", "是否停止所有技能").ToString());
	StaticClass()->FindPropertyByName(TEXT("IsStopSelf"))->SetMetaData(TEXT("DisplayName"), *NSLOCTEXT("IsStopSelf", "UgcSkillEditor", "是否停止此技能自身").ToString());
	StaticClass()->FindPropertyByName(TEXT("OtherSkillClassName"))->SetMetaData(TEXT("DisplayName"), *NSLOCTEXT("OtherSkillClassName", "UgcSkillEditor", "需要停止的其他技能类名").ToString());
	StaticClass()->FindPropertyByName(TEXT("StopReason"))->SetMetaData(TEXT("DisplayName"), *NSLOCTEXT("StopReason", "UgcSkillEditor", "停止的原因").ToString());
}
#endif

bool UUAESkillAction_StopSkill::RealDoAction_Internal()
{
	AActor*	P = GetOwnerPawn();
	if (!P)
	{
		return false;
	}

	auto&& OwnerSkill = MakeWeakObjectPtr(GetOwnerSkill());

	if (!OwnerSkill.IsValid())
	{
		return false;
	}

	if (P->Role != ROLE_Authority && !OwnerSkill->IsOwnerLocallyControlled(GetOwnerSkillManager()))
	{
		return false;
	}

	UUAESkillManagerComponent* pManagerComponent = Cast<UUAESkillManagerComponent>(P->FindComponentByClass(UUAESkillManagerComponent::StaticClass()));
	if (!pManagerComponent)
	{
		return false;
	}
	if (IsStopAllSkill)
	{
		if (bButThisSkill)
		{
			pManagerComponent->StopAllSkillButThis(GetOwnerSkill(), StopReason, bStopPassiveSkill);
		}
		else
		{
			pManagerComponent->StopSkillAll(StopReason, bStopPassiveSkill);
		}
	}
	else if (IsStopSelf)
	{
		pManagerComponent->StopSkillSpecific(GetOwnerSkill(), StopReason);
	}
	else if (AUTSkill* Skill = pManagerComponent->GetSkillByClassName(OtherSkillClassName))
	{
		pManagerComponent->StopSkillSpecific(Skill, StopReason);
	}

	return true;
}