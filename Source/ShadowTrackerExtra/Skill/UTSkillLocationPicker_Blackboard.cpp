// Fill out your copyright notice in the Description page of Project Settings.

#include "ShadowTrackerExtra.h"
#include "UTSkillLocationPicker_Blackboard.h"


bool UUTSkillLocationPicker_Blackboard::PickLocation(AActor* OwnerPawn, FVector& OutLocation)
{
// 	UUAEBlackboard* Blackboard = GetUAEBlackboard();
// 
// 	if (!Blackboard)
// 	{
// 		return false;
// 	}

	UUTSkillManagerComponent* SkillManager = GetOwnerSkillManager();

	if (!SkillManager)
	{
		return false;
	}

	if (IsExistVector(LocationKey))
	{
		OutLocation = GetValueAsVector(LocationKey);
	}

	if (IsExistObject(LocationKey))
	{
		AActor* target = Cast<AActor>(GetValueAsObject(LocationKey));
		if (target)
		{
			OutLocation = target->GetActorLocation();
		}
	}
	else if (IsExistWeakObject(LocationKey))
	{
		AActor* target = Cast<AActor>(GetValueAsWeakObject(LocationKey));
		if (target)
		{
			OutLocation = target->GetActorLocation();
		}
	}
	UE_LOG(LogTaggedLog, Log, TEXT("SkillTag UTSkillLocationPicker_Blackboard::PickLocation Key:%s, Loc:%s")
		, *(LocationKey.SelectedKeyName.ToString()), *OutLocation.ToString());

	return true;
}

