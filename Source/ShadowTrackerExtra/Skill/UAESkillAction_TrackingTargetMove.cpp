#include "ShadowTrackerExtra.h"
#include "UAESkillAction_TrackingTargetMove.h"

#include "UAESkillManagerComponent.h"
#include "CustomComponent/STExtraSimpleCharacterPhysics.h"

bool UUAESkillAction_TrackingTargetMove::RealDoAction_Internal()
{
	Super::RealDoAction_Internal();

	APawn* OwnerCharacter = Cast<APawn>(GetOwnerPawn());
	if (OwnerCharacter == nullptr) return false;
	if (OwnerCharacter->GetNetMode() != NM_DedicatedServer) return false;
	
	FSkillAction_TrackingTargetMove_Data* pMem = GetNodeMemory<FSkillAction_TrackingTargetMove_Data>(GetInstancedNodeContainer());
	if (!pMem)
	{
		return false;
	}

	if (!TargetPicker) return false;

	SCOPED_SKILL_CONTEXT(TargetPicker, GetOwnerComponent());
	TargetPicker->InitSkill(GetOwnerSkill());
	if (!TargetPicker->PickTargets() || !TargetPicker->GetFirstTarget())
	{
		return false;
	}
	
	const FVector TargetLoc = TargetPicker->GetFirstTarget()->GetActorLocation(); 
	const FRotator LookAtVector = FRotationMatrix::MakeFromX(TargetLoc - OwnerCharacter->GetActorLocation()).Rotator();
	FVector Velocity = MoveSpeed * LookAtVector.Vector();
	if (bMaintainHorizontalGroundVelocity)
	{
		Velocity.Z = 0;
	}
	
	USTExtraSimpleCharacterPhysics* pPhys = Cast<USTExtraSimpleCharacterPhysics>(OwnerCharacter->GetComponentByClass(USTExtraSimpleCharacterPhysics::StaticClass()));
	if (pPhys)
	{
		const bool bSuccess = pPhys->StartSkillSimulate(Velocity, bCheckPawnCollision);
		if (bSuccess)
		{
			pMem->IsMoving = true;
			pMem->TargetActor = TargetPicker->GetFirstTarget();

			OwnerCharacter->SetActorRotation(LookAtVector);
		}

		return bSuccess;
	}

	return false;
}

void UUAESkillAction_TrackingTargetMove::UpdateAction_Internal(float DeltaSeconds)
{
	Super::UpdateAction_Internal(DeltaSeconds);

	APawn* OwnerCharacter = Cast<APawn>(GetOwnerPawn());
	if (OwnerCharacter == nullptr) return;
	if (OwnerCharacter->GetNetMode() != NM_DedicatedServer) return;

	FSkillAction_TrackingTargetMove_Data* pMem = GetNodeMemory<FSkillAction_TrackingTargetMove_Data>(GetInstancedNodeContainer());
	if (!pMem || !pMem->IsMoving || !pMem->TargetActor.IsValid())
	{
		return;
	}

	const FVector& CurTargetLoc = pMem->TargetActor->GetActorLocation();
	const FVector& CurLoc = OwnerCharacter->GetActorLocation();
	const float Dis = (CurTargetLoc - CurLoc).Size();

	USTExtraSimpleCharacterPhysics* pPhys = Cast<USTExtraSimpleCharacterPhysics>(OwnerCharacter->GetComponentByClass(USTExtraSimpleCharacterPhysics::StaticClass()));
	if (!pPhys) return;

	if (pMem->AccumulatedTime > MaxDuration || Dis < AcceptanceRadius)
	{
		pPhys->StopSkillSimulate();
		pMem->IsMoving = false;

		TriggerStopEvent();

		UE_LOG(LogSkillAction, Log, TEXT("TrackingTargetMove Done! AccumulatedTime: %f, MaxDuration: %f, CurDistance: %f, AcceptanceRadius: %f"), pMem->AccumulatedTime, MaxDuration, Dis, AcceptanceRadius);
		return;
	}
	pMem->AccumulatedTime += DeltaSeconds;

	const FVector& CurVel = pPhys->GetVelocity();
	const FRotator& TargetRot = FRotationMatrix::MakeFromX(CurTargetLoc - CurLoc).Rotator();
	const FRotator& NewRot = FMath::RInterpConstantTo(CurVel.Rotation(), TargetRot, DeltaSeconds, RotationRate);
	FVector NewVel = NewRot.Vector() * MoveSpeed;
	if (bMaintainHorizontalGroundVelocity)
	{
		NewVel.Z = 0;
	}

	pPhys->SetVelocity(NewVel, ESimulatePhysicsType::SKILL);

	if (KeepLookAtTarget)
	{
		// 朝向目标
		OwnerCharacter->SetActorRotation(TargetRot);
	}
	else
	{
		// 朝向前进方向
		OwnerCharacter->SetActorRotation(NewRot);
	}
}

void UUAESkillAction_TrackingTargetMove::UndoAction_Internal()
{
	Super::UndoAction_Internal();

	const APawn* OwnerCharacter = Cast<APawn>(GetOwnerPawn());
	if (OwnerCharacter == nullptr) return;
	if (OwnerCharacter->GetNetMode() != NM_DedicatedServer) return;

	USTExtraSimpleCharacterPhysics* pPhys = Cast<USTExtraSimpleCharacterPhysics>(OwnerCharacter->GetComponentByClass(USTExtraSimpleCharacterPhysics::StaticClass()));
	if (pPhys)
	{
		pPhys->StopSkillSimulate();
	}

	Reset_Internal();
}

void UUAESkillAction_TrackingTargetMove::Reset_Internal()
{
	Super::Reset_Internal();

	FSkillAction_TrackingTargetMove_Data* pMem = GetNodeMemory<FSkillAction_TrackingTargetMove_Data>(GetInstancedNodeContainer());
	if (!pMem)
	{
		return;
	}

	if (pMem->IsMoving)
	{
		const APawn* OwnerCharacter = Cast<APawn>(GetOwnerPawn());
		if (!OwnerCharacter) return;
		
		USTExtraSimpleCharacterPhysics* pPhys = Cast<USTExtraSimpleCharacterPhysics>(OwnerCharacter->GetComponentByClass(USTExtraSimpleCharacterPhysics::StaticClass()));
		if (pPhys)
		{
			pPhys->StopSkillSimulate();
		}

		pMem->IsMoving = false;
	}

	pMem->Reset();
}

UScriptStruct* UUAESkillAction_TrackingTargetMove::GetInstanceNodeStruct() const
{
	return FSkillAction_TrackingTargetMove_Data::StaticStruct();
}

void UUAESkillAction_TrackingTargetMove::TriggerStopEvent() const
{
	if (EventNameOnMoveStop.IsEmpty()) return;

	UUAESkillManagerComponent* SkillManager = Cast<UUAESkillManagerComponent>(GetOwnerSkillManager());
	if (!SkillManager)
	{
		if (const AActor* Owner = GetOwnerPawn())
		{
			SkillManager = Cast<UUAESkillManagerComponent>(Owner->FindComponentByClass<UUTSkillManagerComponent>());
		}
	}

	if (SkillManager)
	{
		if (AUTSkill* Skill = GetOwnerSkill())
		{
			SkillManager->TriggerStringEvent(SkillManager->GetSkillIndex(Skill), EventNameOnMoveStop);
		}
	}
}
