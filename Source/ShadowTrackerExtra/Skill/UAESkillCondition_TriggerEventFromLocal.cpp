// Fill out your copyright notice in the Description page of Project Settings.

#include "ShadowTrackerExtra.h"
#include "UAESkillCondition_TriggerEventFromLocal.h"
#include "UAESkillManagerComponent.h"

bool UUAESkillCondition_TriggerEventFromLocal::IsOK_Internal_Implementation()
{
	UUAESkillManagerComponent* uaeSkillManger = Cast<UUAESkillManagerComponent>(GetOwnerSkillManager());
	if (uaeSkillManger && uaeSkillManger->OwnerActor && uaeSkillManger->TriggerSkillEventFromRole == uaeSkillManger->OwnerActor->Role)
	{
		return true;
	}
	else
	{
		return false;
	}
}


