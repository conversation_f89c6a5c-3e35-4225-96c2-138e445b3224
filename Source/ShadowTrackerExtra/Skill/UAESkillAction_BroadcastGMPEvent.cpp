// Fill out your copyright notice in the Description page of Project Settings.

#include "ShadowTrackerExtra.h"
#if defined(GMP_API)
#include "GMPHelper.h"
#endif
#include "UAESkillAction_BroadcastGMPEvent.h"
#include "STBaseBuffManager.h"


bool UUAESkillAction_BroadcastGMPEvent::RealDoAction_Internal()
{
	Super::RealDoAction_Internal();

	Broadcast(MsgKey_Do);

	return true;
}

void UUAESkillAction_BroadcastGMPEvent::Reset_Internal()
{
	Super::Reset_Internal();

	Broadcast(MsgKey_Reset);
}

void UUAESkillAction_BroadcastGMPEvent::UndoAction_Internal()
{
	Super::UndoAction_Internal();

	Broadcast(MsgKey_Undo);
}

void UUAESkillAction_BroadcastGMPEvent::UpdateAction_Internal(float DeltaSeconds)
{
	Super::UpdateAction_Internal(DeltaSeconds);

	Broadcast(MsgKey_Update);
}

void UUAESkillAction_BroadcastGMPEvent::Serialize(FArchive& Ar)
{
	Super::Serialize(Ar);

	if (!MsgKey.IsEmpty())
	{
		MsgKey_Do = *MsgKey;

		MsgKey.Empty();
	}
}

void UUAESkillAction_BroadcastGMPEvent::Broadcast(FName const& InMsgKey)
{
#if defined(GMP_API)
	if (!InMsgKey.IsNone())
	{
		auto SkillUID = 0;

		AActor* const OwnerActor = GetOwnerPawn();

		if (const AUTSkill* const Skill = GetOwnerSkill())
		{
			SkillUID = Skill->GetSkillGlobalUID();
		}
		else
		{
			if (const USTBaseBuff* const OwnerBuff = GetOwnerBuff())
			{
				if (USTBaseBuffManager::TryGetInstance())
				{
					SkillUID = USTBaseBuffManager::TryGetInstance()->GetBuffID(*OwnerBuff->BuffName);
				}
			}
		}

		FGMPHelper::SendObjectMessage(OwnerActor, InMsgKey, SkillUID);
	}
#endif
}
