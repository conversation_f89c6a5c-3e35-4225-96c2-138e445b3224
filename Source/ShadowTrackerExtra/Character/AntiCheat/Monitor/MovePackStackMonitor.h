#pragma once

#include "CoreMinimal.h"
#include "Delegates/Delegate.h"
#include "Misc/Optional.h"
#include "Engine/EngineTypes.h"
#include "MovePackStackMonitor.generated.h"

class ACharacter;
class UMoveAntiCheatComponent;
class ASTExtraPlayerCharacter;
class ASTExtraPlayerController;
class USTCharacterMovementComponent;
class UPlayerAntiCheatManager;
class UCharaStatComp;

/** 监控状态 */
enum class EMovePackStackMonitorState : uint8
{
    EMPSMS_None,
    EMPSMS_Start
};

/** 移动模式类别 根据移动信任距离大小 */
enum class EPackStackMode : uint8
{
    EPSM_WalkAndFall_Standard,
    EPSM_WalkAndFall_OverStandard,
    EPSM_Other_Standard,
    EPSM_Other_OverLevel1,
    EPSM_Other_OverLevel2,
    EPSM_Other_OverLevel3
};

/** 单帧周期 */
struct FMovePackStackSingleInfo
{
    int32 RecvNum = 0;
    EPackStackMode PackStackMode = EPackStackMode::EPSM_WalkAndFall_Standard;

    EMovementMode MovementMode = MOVE_None;
    float PositionErrorDist = 0.0f;

    FVector StartLocation = FVector::ZeroVector;
    FVector EndLocation = FVector::ZeroVector;

public:
    void Init(EPackStackMode InPackStackMode, EMovementMode InMovementMode, float InPositionErrorDist, const FVector& InStartLocation, const FVector& InEndLocation);
    void Clear();
    FString ToString() const;
};

/** 上报周期 */
struct FMovePackStackCalculateInfo
{
    int32 RecvNum = 0;

    EMovementMode MovementMode = MOVE_None;
    float PositionErrorDist = 0.0f;

    int32 TotalSinglePackNum = 0;
    int32 MaxSinglePackNum = 0;
    int32 MinSinglePackNum = 0;

    float TotalSingleMoveDist = 0.0f;
    float MaxSingleMoveDist = 0.0f;
    float MinSingleMoveDist = 0.0f;

public:
    void Init(const FMovePackStackSingleInfo& InCurTickInfo);
    void AddTickInfo(const FMovePackStackSingleInfo& InCurTickInfo);

    void Clear();
    FString ToString() const;
};

/**
 * 监控弱网堆包
 * 短时间内移动包堆包数量监控 2024-6-28:新版本，监控单帧堆包大于5个的情况
 */
USTRUCT()
struct FMovePackStackMonitor
{
    GENERATED_BODY()

public:
    /** 逻辑初始化 */
    void DoInit(UMoveAntiCheatComponent* InMoveAc);

    /** 逻辑更新 */
    void DoUpdate(const float InDeltaTime);

    /** 清理状态 */
    void ClearState();

    /** 外部调用清理状态 */
    void Clear();

    /** 检查是否处于需要检查的状态 */
    bool IsStateCanCheck();

public:
    /** 当前状态 */
    EMovePackStackMonitorState CurState = EMovePackStackMonitorState::EMPSMS_None;

    /** 是否完成了初始化 */
    bool bIsInited = false;

    /** 移动反外挂指针 */
    TWeakObjectPtr<UMoveAntiCheatComponent> MoveAntiCheat;

    /** AntiCheatManager类 */
    TWeakObjectPtr<UPlayerAntiCheatManager> AntiCheatManager;

    /** 指向监控的玩家 */
    TWeakObjectPtr<ASTExtraPlayerCharacter> StCharacter;

    /** PlayerController类 */
    TWeakObjectPtr<ASTExtraPlayerController> StController;

    /** 移动组件 */
    TWeakObjectPtr<USTCharacterMovementComponent> StMovementComponent;

    /** 状态收集组件 */
    TWeakObjectPtr<UCharaStatComp> CharaStat;

    /** 缓存的PlayerKey */
    uint32 PlayerKey = 0;

public:
    /** 当前帧收包数据 */
    FMovePackStackSingleInfo CurTickInfo;

public:
    /** 收到移动包 */
    void OnReceivePack(const FVector& PrevMoveLocation, const FVector& PostMoveLocation, EMovementMode MovementMode, float PositionErrorDist, float StandardPositionErrorDist);

    /** 判断属于哪种堆包类型 */
    EPackStackMode GetPackStackMode(EMovementMode MovementMode, float PositionErrorDist, float StandardPositionErrorDist);

    /** 上报检查 */
    void ReportCheck(const FMovePackStackSingleInfo& InCurTickInfo);
};