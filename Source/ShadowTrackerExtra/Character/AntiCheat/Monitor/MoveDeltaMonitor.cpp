#include "ShadowTrackerExtra.h"
#include "MoveDeltaMonitor.h"
#include "Character/MoveAntiCheatComponent.h"
#include "Player/STExtraPlayerCharacter.h"
#include "Weapons/AntiCheat/AntiCheatUtils.h"
#include "Player/STExtraPlayerController.h"
#include "Character/STCharacterMovementComponent.h"
#include "Security/PlayerAntiCheatManager.h"
#include "Components/PrimitiveComponent.h"
#include "Security/SuspiciousInfoHelper.h"
#include "Security/CharaStatComp.h"
#include "Game/STExtraGameStateBase.h"
#include "Misc/DateTime.h"

/**
 * ���Monitor���ڼ���ƶ�����
 * Ŀǰ�ļ�ذ�����Dt��أ��ͻ�������ٶȼ��, �ͻ���ʱ�������
 */

/** MoveDelta����ܿ��� */
static int32 OpenMoveDeltaMonitor = 1;
static FAutoConsoleVariableRef CVarOpenMoveDeltaMonitor(
    TEXT("mac.OpenMoveDeltaMonitor"),
    OpenMoveDeltaMonitor,
    TEXT("Delta monitor"),
    ECVF_Default
);

/** MoveDelta�쳣��ֵ */
static float MoveDeltaThreshold = 0.005;
static FAutoConsoleVariableRef CVarMoveDeltaThreshold(
    TEXT("mac.MoveDeltaThreshold"),
    MoveDeltaThreshold,
    TEXT("Delta monitor"),
    ECVF_Default
);

/** MoveDelta�쳣��������ֵ */
static int32 MoveDeltaNumThreshold = 40;
static FAutoConsoleVariableRef CVarMoveDeltaNumThreshold(
    TEXT("mac.MoveDeltaNumThreshold"),
    MoveDeltaNumThreshold,
    TEXT("Delta monitor"),
    ECVF_Default
);

/** MoveDelta�쳣�ͷ����� */
static int32 EnableMoveDeltaPunish = 0;
static FAutoConsoleVariableRef CVarEnableMoveDeltaPunish(
    TEXT("mac.EnableMoveDeltaPunish"),
    EnableMoveDeltaPunish,
    TEXT("Delta monitor"),
    ECVF_Default
);

/** MoveDelta�쳣�ͷ���ֵ */
static int32 MoveDeltaPunishThreshold = 100;
static FAutoConsoleVariableRef CVarMoveDeltaPunishThreshold(
    TEXT("mac.MoveDeltaPunishThreshold"),
    MoveDeltaPunishThreshold,
    TEXT("Delta monitor"),
    ECVF_Default
);
//-------------------------------------------------------------------------

/** ClientMaxSpeed����ܿ��� */
static int32 OpenClientMaxSpeedMonitor = 1;
static FAutoConsoleVariableRef CVarOpenClientMaxSpeedMonitor(
    TEXT("mac.OpenClientMaxSpeedMonitor"),
    OpenClientMaxSpeedMonitor,
    TEXT("ClientMaxSpeed monitor"),
    ECVF_Default
);

/** ClientMaxSpeed���ֵ */
static int32 MinClientMaxSpeed = 700;
static FAutoConsoleVariableRef CVarMinClientMaxSpeed(
    TEXT("mac.MinClientMaxSpeed"),
    MinClientMaxSpeed,
    TEXT("MinClientMaxSpeed"),
    ECVF_Default
);
//--------------------------------------------------------------------------

/** �ͻ���ʱ������ټ���ܿ��� */
static int32 OpenClientTimeStampMonitor = 1;
static FAutoConsoleVariableRef CVarOpenClientTimeStampMonitor(
    TEXT("mac.OpenClientTimeStampMonitor"),
    OpenClientTimeStampMonitor,
    TEXT("ClientBigMove monitor"),
    ECVF_Default
);

/** �ͻ���ʱ������ٳ����ı��� */
static float ClientTimeStampOverRate = 2;
static FAutoConsoleVariableRef CVarClientTimeStampOverRate(
    TEXT("mac.ClientTimeStampOverRate"),
    ClientTimeStampOverRate,
    TEXT("ClientBigMove monitor"),
    ECVF_Default
);

/** �ͻ���ʱ������ٳͷ����� */
static int32 EnableClientTimeStampPunish = 0;
static FAutoConsoleVariableRef CVarEnableClientTimeStampPunish(
    TEXT("mac.EnableClientTimeStampPunish"),
    EnableClientTimeStampPunish,
    TEXT("Delta monitor"),
    ECVF_Default
);

/** �ͻ���ʱ������ٳͷ���ֵ */
static int32 ClientTimeStampPunishThreshold = 3;
static FAutoConsoleVariableRef CVarClientTimeStampPunishThreshold(
    TEXT("mac.ClientTimeStampPunishThreshold"),
    ClientTimeStampPunishThreshold,
    TEXT("Delta monitor"),
    ECVF_Default
);

/** �ͻ���ʱ������ٳͷ�ʱ�� */
static float ClientTimeStampPunishTimer = 10.0f;
static FAutoConsoleVariableRef CVarClientTimeStampPunishTimer(
    TEXT("mac.ClientTimeStampPunishTimer"),
    ClientTimeStampPunishTimer,
    TEXT("Delta monitor"),
    ECVF_Default
);

/** �ͻ���ʱ�����������ʱ�� */
static int32 ClientTimeStampOverContiuneTimesThreshold = 3;
static FAutoConsoleVariableRef CVarClientTimeStampOverContiuneTimesThreshold(
    TEXT("mac.ClientTimeStampOverContiuneTimesThreshold"),
    ClientTimeStampOverContiuneTimesThreshold,
    TEXT("Delta monitor"),
    ECVF_Default
);
/** �ͻ���ʱ�����������ʱ�ľ�����ֵ */
static float ClientTimeStampOverContiuneTimesDistThreshold = 4500.0f;
static FAutoConsoleVariableRef CVarClientTimeStampOverContiuneTimesDistThreshold(
    TEXT("mac.ClientTimeStampOverContiuneTimesDistThreshold"),
    ClientTimeStampOverContiuneTimesDistThreshold,
    TEXT("Delta monitor"),
    ECVF_Default
);

#if !(UE_BUILD_SHIPPING || UE_BUILD_TEST)
/** �ͻ���ʱ��������޸�DeltaTime */
static int32 ClientTimeStampOverContiuneTimesDistCheat = 0;
static FAutoConsoleVariableRef CVarClientTimeStampOverContiuneTimesDistCheat(
    TEXT("mac.ClientTimeStampOverContiuneTimesDistCheat"),
    ClientTimeStampOverContiuneTimesDistCheat,
    TEXT("Delta monitor"),
    ECVF_Default
);
#endif

static int32 TestUID = 0;
static FAutoConsoleVariableRef CVarTestUID(
    TEXT("mac.TestUID"),
    TestUID,
    TEXT("Delta monitor"),
    ECVF_Default
);

void FMoveDeltaMonitor::DoInit(UMoveAntiCheatComponent* InMoveAc)
{
    if (bIsInited)
    {
        return;
    }

    /** ��鷴���ָ�� */
    if (!IsValid(InMoveAc))
    {
        UE_LOG(LogAntiCheat, Warning, TEXT("FMoveDeltaMonitor::Init: Invalid MoveAntiCheat %p"), InMoveAc);
        return;
    }

    /** ����ɫָ�� */
    ASTExtraPlayerCharacter* tp_StChara = Cast<ASTExtraPlayerCharacter>(InMoveAc->GetOwner());
    if (!IsValid(tp_StChara))
    {
        UE_LOG(LogAntiCheat, Warning, TEXT("FMoveDeltaMonitor::Init: Invalid Character"));
        return;
    }

    /** �ƶ�����Ҳ�����AI */
    if (tp_StChara->IsAI())
    {
        return;
    }

    /** ���PlayerKey */
    if (0 == PlayerKey)
    {
        PlayerKey = tp_StChara->PlayerKey;
    }

    MoveAntiCheat = InMoveAc;
    StCharacter = tp_StChara;
    //StController = tp_StController;
    //StMovementComponent = tp_STMovementComponent;
    //AntiCheatManager = tp_AntiCheatManager;

    bIsInited = true;

    ClearState();

    UE_LOG(LogAntiCheat, Log, TEXT("FMoveDeltaMonitor::DoInit: Init Finish Chara=(%p)%s %f"),
        InMoveAc,
        *GetFullNameSafe(InMoveAc),
        MoveDeltaThreshold
    );
}

void FMoveDeltaMonitor::DoUpdate(const float InDeltaTime)
{
#if UE_SERVER || UE_EDITOR || WITH_UGC_EDITOR
    /** FMoveDeltaMonitor�ܿ��� */
    if (!OpenMoveDeltaMonitor)
    {
        return;
    }

    /** δ��ʼ�� */
    if (!bIsInited)
    {
        return;
    }

    /** ���ָ�� */
    if (!MoveAntiCheat.IsValid())
    {
        UE_LOG(LogAntiCheat, Warning, TEXT("FMoveDeltaMonitor::DoUpdate Bad MoveAntiCheat [%u]"), PlayerKey);
        return;
    }

    if (!StCharacter.IsValid())
    {
        UE_LOG(LogAntiCheat, Warning, TEXT("FMoveDeltaMonitor::DoUpdate Bad StCharacter [%u]"), PlayerKey);
        return;
    }

    if (!StMovementComponent.IsValid())
    {
        /** ������ȡ�ƶ���� */
        USTCharacterMovementComponent* tp_STMovementComponent = Cast<USTCharacterMovementComponent>(StCharacter->GetMovementComponent());
        if (!IsValid(tp_STMovementComponent))
        {
            UE_LOG(LogAntiCheat, Warning, TEXT("FMoveDeltaMonitor::DoUpdate Bad StMovementComponent [%u]"), PlayerKey);
            return;
        }

        StMovementComponent = tp_STMovementComponent;
    }

    if (!StController.IsValid())
    {
        /** ������ȡController */
        ASTExtraPlayerController* tp_StController = Cast<ASTExtraPlayerController>(StCharacter->GetPlayerControllerSafety());
        if (!IsValid(tp_StController))
        {
            UE_LOG(LogAntiCheat, Warning, TEXT("FMoveDeltaMonitor::DoUpdate Bad StController [%u]"), PlayerKey);
            return;
        }

        StController = tp_StController;
    }

    if (!AntiCheatManager.IsValid())
    {
        /** ������ȡAntiCheatManager */
        UPlayerAntiCheatManager* tp_AntiCheatManager = UAntiCheatUtils::GetPlayerAntiCheatManager(StCharacter.Get());
        if (!IsValid(tp_AntiCheatManager))
        {
            UE_LOG(LogAntiCheat, Warning, TEXT("FMoveDeltaMonitor::DoUpdate Bad AntiCheatManager [%u]"), PlayerKey);
            return;
        }

        AntiCheatManager = tp_AntiCheatManager;
    }

    if (!CharaStat.IsValid())
    {
        UCharaStatComp* tp_CharaStat = StController->CharaStat;
        if (!IsValid(tp_CharaStat))
        {
            UE_LOG(LogAntiCheat, Warning, TEXT("FMoveDeltaMonitor::DoUpdate Bad CharaStat [%u]"), PlayerKey);
            return;
        }

        CharaStat = tp_CharaStat;
    }

    /** ����Ƿ���ͣ */
    if (MoveAntiCheat->Get_IsPausedByFlag())
    {
        return;
    }

    switch (CurState)
    {
    case EMoveDeltaMonitorState::EMDMS_None:
    {
        TotalTime += InDeltaTime;

        /** DeltaTime��� 1s���һ�� */
        if (TotalTime >= CheckInterval)
        {
            /** DeltaTime�쳣 */
            if (BadMovePackNum >= MoveDeltaNumThreshold)
            {
                UWorld* tp_World = MoveAntiCheat->GetWorld();
                const double tf_CurWorldTime = (double)FDateTime::Now().ToUnixTimestampMS() / 1000.0f;
                AntiCheatManager->VsBadMoveDelta.RecWithTime(tf_CurWorldTime);

                static int32 LogCount = 50;
                if (LogCount > 0)
                {
                    FString DeltaTimeList = "Dt:";
                    uint8 tu_CntLimit = 0;
                    for (auto iter : BadMovePackList)
                    {
                        DeltaTimeList = FString::Printf(TEXT("%s %f"), *DeltaTimeList, iter);
                        tu_CntLimit++;

                        if (tu_CntLimit > 10)
                        {
                            break;
                        }
                    }

                    //UE_LOG(LogAntiCheat, Warning, TEXT("FMoveDeltaMonitor::DoUpdate BadMoveDelta [%u %llu] %f %d,%d %s LogCount=%d"), StCharacter->PlayerKey, StController->UID, MoveDeltaThreshold, MovePackNum, BadMovePackNum, *DeltaTimeList, LogCount);
                    LogCount--;
                }
            }
            else
            {
                if (BadMovePackNum > 0)
                {
                    //UE_LOG(LogAntiCheat, Warning, TEXT("FMoveDeltaMonitor::DoUpdate BadMoveDelta Still Has [%u %llu] %d"), StCharacter->PlayerKey, StController->UID, BadMovePackNum);
                }
            }

            TotalTime = 0;
            ClearState();
        }

        break;
    }
    case EMoveDeltaMonitorState::EMDMS_Bad:
    {
        break;
    }
    default:
        break;
    }

    /**
     * �ƶ���ʱ�������У��
     */

    if (!OpenClientTimeStampMonitor)
    {
        return;
    }

    /** �ͷ�ʱ����ʱ�� */
    if (bIsPunish)
    {
        PunishTimer -= InDeltaTime;
        if (PunishTimer <= 0.0f)
        {
            bIsPunish = false;
            _ResetTimeStampRecordInfos();
        }

        return;
    }

    if (!IsTimeStampRecordStateCanCheck())
    {
        _ResetTimeStampRecordInfos();
        return;
    }

    /** ��������ʧ��,������ �������ᴥ�� */
    if (!TimeStampRecordInfos.IsValidIndex(CurRecordIndex))
    {
        return;
    }

    /** ��һ������ */
    if (!bIsFull && CurRecordIndex == IntervalLength - 1)
    {
        bIsFull = true;
    }

    TimeStampRecordTotalTime += InDeltaTime;
    /** ʱ������ټ�� 1s���һ�� */
    if (TimeStampRecordTotalTime >= CheckInterval)
    {
        /** ������ */
        if (bIsFull)
        {
            FTimeStampRecord& CurRecordInfo = TimeStampRecordInfos[CurRecordIndex];

            if (CurRecordInfo.ReceivedPackNum > 0 && LastDsTime != 0.0f)
            {
                float DsTimeDiff = CurRecordInfo.DsTime - LastDsTime;
                float ClientDeltaTimeSum = 0.0f;
                for (auto iter : TimeStampRecordInfos)
                {
                    if (!iter.bIsWrong)
                    {
                        ClientDeltaTimeSum += iter.ClientDeltaTime;
                    }
                    else
                    {
                        DsTimeDiff -= 1.0f;
                    }
                }

                //UE_LOG(LogAntiCheat, Warning, TEXT("FMoveDeltaMonitor::DoUpdate TimeStampRecord [%u %u] %f %f %s"), StCharacter->PlayerKey, StController->UID, ClientDeltaTimeSum, DsTimeDiff, *DetailLog);

                if (DsTimeDiff <= 0.0f || DsTimeDiff > 120.0f)
                {
                    if (!bDsTimeErrorReported)
                    {
                        bDsTimeErrorReported = true;

#if !(UE_BUILD_SHIPPING || UE_BUILD_TEST)
                        uint64 UID = StController.IsValid() ? StController->UID : 0;
                        FString ReportStackMsg = FString::Printf(TEXT("PlayerKey=%u;PlayerUID=%llu"), StCharacter->PlayerKey, UID);
                        FString ReportStackDetail = FString::Printf(TEXT("%f %f %f %f"), ClientDeltaTimeSum, DsTimeDiff, CurRecordInfo.DsTime, LastDsTime);

                        FApp::ExceptionReport(TEXT("TimeStampDsTimeErrorMonitor"), ReportStackMsg, ReportStackDetail);
#endif
                    }

                    _ResetTimeStampRecordInfos();
                    return;
                }
                else if (ClientDeltaTimeSum >= DsTimeDiff * ClientTimeStampOverRate)
                {
                    /** �����µ������Ϊ�������� */
                    CurRecordInfo.bIsWrong = true;

                    /** ͳ���¹�ȥ�����ڵ�λ�� */
                    float LastContiuneTimesDist = 0.0f;
                    for (int32 i = 0;i < 3;i++)
                    {
                        int32 TempIndex = (CurRecordIndex + IntervalLength - i) % IntervalLength;
                        LastContiuneTimesDist += TimeStampRecordInfos.IsValidIndex(TempIndex) ? TimeStampRecordInfos[TempIndex].MoveDist : 0.0f;
                    }

                    /** 3��ʱ�ϱ���ԭ���� */
                    if (ClientDeltaTimeSum >= DsTimeDiff * 3.0f)
                    {
                        AntiCheatManager->VsClientTimeStampOver.Rec_FailedCnt();
                    }

                    MaxContinueTimes++;
                    ContinueTimes++;
                    //ContinueTimesMoveDist += CurRecordInfo.MoveDist;
                    if (AntiCheatManager->VsClientTimeStampOverContinueTimes.Get_FailedCnt() < (uint32)MaxContinueTimes)
                    {
                        AntiCheatManager->VsClientTimeStampOverContinueTimes.Rec_FailedCnt();
                    }

                    FString DetailLog = "TimeStampRecordInfos:";
                    for (int32 i = 0; i < TimeStampRecordInfos.Num(); ++i)
                    {
                        DetailLog = FString::Printf(TEXT("%s [%d][%s]"), *DetailLog, i, *TimeStampRecordInfos[i].ToString());
                    }

                    UE_LOG(LogAntiCheat, Warning, TEXT("FMoveDeltaMonitor::DoUpdate TimeStampRecord Error [%u %llu] %f %f %f %f %s %d %d %f"), StCharacter->PlayerKey, StController->UID, ClientDeltaTimeSum, DsTimeDiff, CurRecordInfo.DsTime, LastDsTime, *DetailLog, MaxContinueTimes, ContinueTimes, LastContiuneTimesDist);

                    /** ��������쳣ʱ�䳬��3s,���ƶ����볬����ֵ */
                    uint64 PlayerUID = StController->UID;
                    int32 PunishMode = USuspiciousInfoHelper::IsHighlySuspicious(PlayerUID) ? 1 : 2;

                    if (ContinueTimes >= ClientTimeStampOverContiuneTimesThreshold && LastContiuneTimesDist >= ClientTimeStampOverContiuneTimesDistThreshold)
                    {
                        bool bShouldSecurityPunish = USuspiciousInfoHelper::Report(PlayerUID, FGameplayTag::RequestGameplayTag(TEXT("AntiCheating.ClientTimeStampOverDist")));

                        UE_LOG(LogAntiCheat, Warning, TEXT("FMoveDeltaMonitor::DoUpdate TimeStampRecord ContinueTimesAndDistError [%u %llu %llu %s] %d %f %d"), StCharacter->PlayerKey, StController->UID, PlayerUID, *StCharacter->PlayerUID, ContinueTimes, LastContiuneTimesDist, bShouldSecurityPunish ? 1 : 0);

                        const double tf_CurWorldTime = (double)FDateTime::Now().ToUnixTimestampMS() / 1000.0f;
                        AntiCheatManager->VsClientTimeStampOverAndDistError.RecWithTime(tf_CurWorldTime);
                        AntiCheatManager->VsClientTimeStampOverAndDistError.SetPunishMode(0);

                        ContinueTimes = 0;
                        //ContinueTimesMoveDist = 0.0f;
                    }

                    if (!bTimeStampErrorReported)
                    {
                        bTimeStampErrorReported = true;

                        uint64 UID = StController.IsValid() ? StController->UID : 0;
                        FString ReportStackMsg = FString::Printf(TEXT("PlayerKey=%u;PlayerUID=%llu"), StCharacter->PlayerKey, UID);
                        FString ReportStackDetail = FString::Printf(TEXT("%f>%f*%f %f %f"), ClientDeltaTimeSum, DsTimeDiff, ClientTimeStampOverRate, CurRecordInfo.DsTime, LastDsTime);

                        //FApp::ExceptionReport(TEXT("TimeStampRecordMonitor"), ReportStackMsg, ReportStackDetail);
                    }

                    bool bShouldSecurityPunish = USuspiciousInfoHelper::IsReachPunishThreshold(PlayerUID, FGameplayTag::RequestGameplayTag(TEXT("AntiCheating.ClientTimeStampOverDist")));
                    if (bShouldSecurityPunish)
                    {
                        bIsPunish = true;
                        PunishTimer = ClientTimeStampPunishTimer;
                        
                        UE_LOG(LogAntiCheat, Warning, TEXT("FMoveDeltaMonitor::DoUpdate TimeStampRecord Punish [%u %llu] %u %d %f"), StCharacter->PlayerKey, StController->UID, AntiCheatManager->VsClientTimeStampOverAndDistError.Get_FailedCnt(), ClientTimeStampPunishThreshold, PunishTimer);
                        AntiCheatManager->VsClientTimeStampOverAndDistError.SetPunishMode(PunishMode);
                    }
                }
                else
                {
                    /** û����ʱ����������� */
                    MaxContinueTimes = 0;
                    ContinueTimes = 0;
                    //ContinueTimesMoveDist = 0.0f;
                }
            }
        }

        /** �ƽ�Index */
        CurRecordIndex++;
        CurRecordIndex = CurRecordIndex % IntervalLength;
        
        if (bIsFull)
        {
            if (TimeStampRecordInfos.IsValidIndex(CurRecordIndex))
            {
                LastDsTime = TimeStampRecordInfos[CurRecordIndex].DsTime;
                TimeStampRecordInfos[CurRecordIndex].ResetState();
            }
            else
            {
                LastDsTime = 0.0f;
                CurRecordIndex = 0;
            }
        }

        TimeStampRecordTotalTime = 0;
    }
#endif
}

void FMoveDeltaMonitor::RecordServerDeltaTime(float InDeltaTime)
{
    if (!OpenMoveDeltaMonitor)
    {
        return;
    }
    
    /** δ��ʼ�� */
    if (!bIsInited)
    {
        return;
    }

    if (!MoveAntiCheat.IsValid())
    {
        return;
    }

    /** ����Ƿ���ͣ */
    if (MoveAntiCheat->Get_IsPausedByFlag())
    {
        return;
    }

    if (CurState == EMoveDeltaMonitorState::EMDMS_None)
    {
        MovePackNum++;
        if (InDeltaTime <= MoveDeltaThreshold)
        {
            BadMovePackNum++;
            BadMovePackList.Add(InDeltaTime);
        }
    }

    /** ����ϱ� */
    if (InDeltaTime <= MoveDeltaThreshold)
    {
        if (!AntiCheatManager.IsValid())
        {
            return;
        }

        if (!StCharacter.IsValid())
        {
            return;
        }

        const double tf_CurWorldTime = (double)FDateTime::Now().ToUnixTimestampMS() / 1000.0f;
        AntiCheatManager->VsBadMoveDeltaTotal.RecWithTime(tf_CurWorldTime);
    }
}

bool FMoveDeltaMonitor::ServerDeltaTimeShouldPunish(float InDeltaTime)
{
#if UE_SERVER || UE_EDITOR || WITH_UGC_EDITOR
    if (!EnableMoveDeltaPunish)
    {
        return false;
    }

    if (InDeltaTime > 0.0f && InDeltaTime <= MoveDeltaThreshold)
    {
        if (!AntiCheatManager.IsValid())
        {
            return false;
        }

        if (!StController.IsValid())
        {
            return false;
        }

        if (!StCharacter.IsValid())
        {
            return false;
        }

        /** ����Ƿ�Ϊ�߿����û� */
        bool bIsSuspicious = USuspiciousInfoHelper::IsHighlySuspicious(StController->UID);

        /** �ϱ�����ȫ */
        bool bShouldSecurityPunish = USuspiciousInfoHelper::Report(StController->UID, FGameplayTag::RequestGameplayTag(TEXT("AntiCheating.MoveDeltaMonitor")));

        /** �ͷ� */
        if (AntiCheatManager->VsBadMoveDeltaTotal.Get_FailedCnt() >= (uint32)MoveDeltaPunishThreshold || bShouldSecurityPunish)
        {
            UWorld* tp_World = StCharacter->GetWorld();
            const float tf_CurWorldTime = IsValid(tp_World) ? tp_World->GetTimeSeconds() : 0.0f;

            if (!FMath::IsNearlyEqual(DtLastLogTime, tf_CurWorldTime, 1.0f))
            {
                UE_LOG(LogAntiCheat, Warning, TEXT("FMoveDeltaMonitor::ServerDeltaTimeShouldPunish: Punish [%u,%llu] %f<%f %d,%d %u,%d"),
                    StCharacter->PlayerKey,
                    StController->UID,
                    InDeltaTime,
                    MoveDeltaThreshold,
                    bIsSuspicious ? 1 : 0,
                    bShouldSecurityPunish ? 1 : 0,
                    AntiCheatManager->VsBadMoveDeltaTotal.Get_FailedCnt(),
                    MoveDeltaPunishThreshold
                );

                DtLastLogTime = tf_CurWorldTime;
            }

            return true;
        }
    }

#endif
    return false;
}

void FMoveDeltaMonitor::RecordClientMaxSpeed(float InClientMaxSpeed)
{
    if (!OpenClientMaxSpeedMonitor)
    {
        return;
    }

    if (!bIsInited)
    {
        return;
    }

    if (!MoveAntiCheat.IsValid())
    {
        return;
    }

    if (!StCharacter.IsValid())
    {
        return;
    }

    if (!CharaStat.IsValid())
    {
        return;
    }

    if (!IsStateCanCheck())
    {
        return;
    }

    /** ��λ */
    /** 700-1000 1000-1500 1500-************** 2500-3000 >3000 */
    if (InClientMaxSpeed > (float)MinClientMaxSpeed)
    {
        TArray<uint32>* ClientMaxSpeedsRef = &CharaStat->ClientMaxSpeeds;

        int32 Index = 0;
		if (InClientMaxSpeed >= 1000.0f)
		{
            Index = (InClientMaxSpeed - 1000.0f) / 500.0f + 1;
		}

        int32 RealIndex = FMath::Clamp(Index, (int32)0, (int32)5);
        if (ClientMaxSpeedsRef->IsValidIndex(RealIndex))
        {
            (*ClientMaxSpeedsRef)[RealIndex]++;
        }

        static int32 LogCount = 50;
        if (LogCount > 0)
        {
            float tf_CurTime = IsValid(MoveAntiCheat->GetWorld()) ? MoveAntiCheat->GetWorld()->GetTimeSeconds() : 0.0f;
            if (!FMath::IsNearlyEqual(LastLogTime, tf_CurTime, 3.0f))
            {
                UE_LOG(LogAntiCheat, Warning, TEXT("FMoveDeltaMonitor::RecordClientMaxSpeed [%u] %f %d"), StCharacter->PlayerKey, InClientMaxSpeed, LogCount);
                LastLogTime = tf_CurTime;
                LogCount--;
            }
        }

        if (AntiCheatManager.IsValid() && InClientMaxSpeed < 5000.0f)
        {
            AntiCheatManager->VsClientMaxSpeed.Rec_FailedCnt();
        }
    }
}

void FMoveDeltaMonitor::RecordServerDeltaTimeAndDist(float InDeltaTime, float InMoveDist)
{
    if (!OpenClientTimeStampMonitor)
    {
        return;
    }

    if (!bIsInited)
    {
        return;
    }

    if (!MoveAntiCheat.IsValid())
    {
        return;
    }

    if (!StCharacter.IsValid())
    {
        return;
    }

    if (!StMovementComponent.IsValid())
    {
        return;
    }

    /** ��������ʧ�ܣ�����  ����һ�δ������������ᴥ�� */
    if (!TimeStampRecordInfos.IsValidIndex(CurRecordIndex))
    {
        _ResetTimeStampRecordInfos();
        UE_LOG(LogAntiCheat, Warning, TEXT("FMoveDeltaMonitor::RecordServerDeltaTimeAndTimeStamp: ResetTimeStampRecord CurRecordIndex=%d,IntervalLength=%d"),
            CurRecordIndex,
            IntervalLength
        );
        return;
    }

    FTimeStampRecord& CurTimeStampRecord = TimeStampRecordInfos[CurRecordIndex];

#if !(UE_BUILD_SHIPPING || UE_BUILD_TEST)
    if (ClientTimeStampOverContiuneTimesDistCheat)
    {
        CurTimeStampRecord.ClientDeltaTime += (0.125f - InDeltaTime);
    }
#endif

    CurTimeStampRecord.ClientDeltaTime += InDeltaTime;
    CurTimeStampRecord.DsTime = StMovementComponent->GetCurrentReceivePackTime();
    CurTimeStampRecord.ReceivedPackNum++;
    CurTimeStampRecord.MoveDist += InMoveDist;
}

void FMoveDeltaMonitor::_ResetTimeStampRecordInfos()
{
    if (TimeStampRecordInfos.Num() == 0)
    {
        TimeStampRecordInfos.SetNum(IntervalLength);
    }
    for (FTimeStampRecord& IterSingleInfo : TimeStampRecordInfos)
    {
        IterSingleInfo.ResetState();
    }

    CurRecordIndex = 0;
    LastDsTime = 0.0f;
    bIsFull = false;
    TimeStampRecordTotalTime = 0.0f;
    bIsPunish = false;
    PunishTimer = 0.0f;
    MaxContinueTimes = 0;
}

void FMoveDeltaMonitor::ClearState()
{
    CurState = EMoveDeltaMonitorState::EMDMS_None;

    MovePackNum = 0;
    BadMovePackNum = 0;
    BadMovePackList.Empty();
}

void FMoveDeltaMonitor::Clear()
{
	ClearState();
}

bool FMoveDeltaMonitor::IsStateCanCheck()
{
    if (!StCharacter.IsValid())
    {
        return false;
    }

    if (!MoveAntiCheat.IsValid())
    {
        return false;
    }

    /** ��ȡGameState */
    ASTExtraGameStateBase* MyGameState = Cast<ASTExtraGameStateBase>(UGameplayStatics::GetGameState(StCharacter.Get()));
    if (IsValid(MyGameState))
    {
        /** ���Ʋ��Բ��ڳ�����״̬�ͳ�ʼ��״ִ̬�� */
        FName MoveState = MyGameState->GetGameModeState();
        if (MoveState == FGameModeState::ActiveState || MoveState == FGameModeState::ReadyState)
        {
            return false;
        }
    }

    /** ����Ƿ���ͣ */
    if (MoveAntiCheat->Get_IsPausedByFlag())
    {
        return false;
    }

	return true;
}

bool FMoveDeltaMonitor::IsTimeStampRecordStateCanCheck()
{
    if (!StCharacter.IsValid())
    {
        return false;
    }

    if (!StMovementComponent.IsValid())
    {
        return false;
    }

    if (!MoveAntiCheat.IsValid())
    {
        return false;
    }

    /** ��ȡGameState */
    ASTExtraGameStateBase* MyGameState = Cast<ASTExtraGameStateBase>(UGameplayStatics::GetGameState(StCharacter.Get()));
    if (IsValid(MyGameState))
    {
        /** ���Ʋ��Բ��ڳ�����״̬�ͳ�ʼ��״ִ̬�� */
        FName MoveState = MyGameState->GetGameModeState();
        if (MoveState == FGameModeState::ActiveState || MoveState == FGameModeState::ReadyState)
        {
            return false;
        }
    }

    /** ����Ƿ���ͣ */
    if (MoveAntiCheat->Get_IsPausedByFlag())
    {
        return false;
    }

    USceneComponent* Root = StCharacter->GetRootComponent();
    if (Root && Root->GetAttachParent() != nullptr)
    {
        /** ���������������� */
        return false;
    }
    if (StCharacter->GetCurrentVehicle() != nullptr)
    {
        /** ���ؾ��� */
        return false;
    }
    if (StCharacter->ParachuteState != EParachuteState::PS_None)
    {
        /** ����ɡ */
        return false;
    }
    if (MovementBaseUtility::IsDynamicBase(StCharacter->GetMovementBase()))
    {
        /** �ڶ�̬Base�� */
        return false;
    }

    return true;
}

bool FMoveDeltaMonitor::IsTimeStampErrorPunish()
{
    return bIsPunish && PunishTimer > 0.0f;
}

FString FMoveDeltaMonitor::FTimeStampRecord::ToString()
{
    return FString::Printf(TEXT("%f %f %u %d %f"),
        DsTime,
        ClientDeltaTime,
        ReceivedPackNum,
        bIsWrong,
        MoveDist
    );
}
