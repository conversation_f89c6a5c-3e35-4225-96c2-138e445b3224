// Fill out your copyright notice in the Description page of Project Settings.

#pragma once

#include "CoreMinimal.h"
#include "AvatarStrategyProxy_Player.h"
#include "AvatarStrategyProxy_Player_Entity.generated.h"

/** HOT_UPDATE_PROTECT 参考 AvatarCore.h */
/****** 子类覆盖时，HOT_UPDATE_PROTECT 中的几个关键方法必须用热更保护宏做保护 HOT_UPDATE_PROTECT(FuncName) ******/

class UAvatarComponent;
class UBattleItemHandleBase;
class UAvatarEntity;

UCLASS()
class UAvatarStrategyProxy_Player_Entity : public UAvatarStrategyProxy_Player
{
	GENERATED_BODY()
public:
	/**************************************** HOT_UPDATE_PROTECT ******************************************/
	UFUNCTION(BlueprintCallable)
	virtual void InitStrategyProxy() override;

	UFUNCTION(BlueprintCallable)
	virtual void ReleaseStrategyProxy() override;
	/**************************************** HOT_UPDATE_PROTECT ******************************************/

	/**
	 * +++++++++Entity+++++++++
	 * Should Contains Avatar Component and Avatar Entity in Params
	 */

	UFUNCTION(BlueprintCallable)
	bool PreprocessingEntityLoadedHandle(UAvatarComponent* InAC, UAvatarEntity* InAE, UBattleItemHandleBase* InBackpackHandle);

	UFUNCTION(BlueprintCallable)
	bool PreprocessingEntityMeshData(UAvatarComponent* InAC, UAvatarEntity* InAE, UBattleItemHandleBase* InBackpackHandle);
	
	/**
	 * ---------Entity---------
	 */
	
	/**************************************** HOT_UPDATE_PROTECT ******************************************/
	
	UFUNCTION(BlueprintNativeEvent, BlueprintCallable)
	bool BP_PreprocessingEntityLoadedHandle(UAvatarComponent* InAC, UAvatarEntity* InAE, UBattleItemHandleBase* InBackpackHandle);

	UFUNCTION(BlueprintNativeEvent, BlueprintCallable)
	bool BP_PreprocessingEntityMeshData(UAvatarComponent* InAC, UAvatarEntity* InAE, UBattleItemHandleBase* InBackpackHandle);

	/**************************************** HOT_UPDATE_PROTECT ******************************************/

protected:
	/**************************************** HOT_UPDATE_PROTECT ******************************************/
	
	UPROPERTY(EditAnywhere, BlueprintReadWrite)
	bool bOverride_PreprocessingEntityLoadedHandle = true;

	UPROPERTY(EditAnywhere, BlueprintReadWrite)
	bool bOverride_PreprocessingEntityMeshData= true;
	
	/**************************************** HOT_UPDATE_PROTECT ******************************************/
};
