// 音游组件，为了解释音符和处理逻辑

#pragma once

#include "CoreMinimal.h"
#include "Components/ActorComponent.h"
#include "UI/Widget/CustomSocketObject.h"
#include "UAEPlayerController.h"
#include "BPClassManager.h"
#include "UAECharacter.h"
#include "Player/STExtraPlayerController.h"
#include "MusicGameComponent.generated.h"






class AUAEPlayerController;
class UMusicClientCompBase;
class UMusicClientDrum;

//解释成的音符数据
USTRUCT()
struct FMusicNote
{
	GENERATED_BODY()

	TArray<int32> NoteTrack;
	int32 NoteT = 0;

	int32 NodeIndex = 0;
	bool bRoll = false;

	float expectedHitTime = 0.0f;
	float showEffectTime = 0.0f;
	float animTime = 0.0f;

	float GetNoteTime(int32 nType)
	{
		return nType == 0 ? showEffectTime : animTime;
	}

	friend bool operator<(const FMusicNote& Item1, const FMusicNote& Item2);
};

USTRUCT(BlueprintType)
struct FMusicConfig
{
	GENERATED_BODY()

public:
	UPROPERTY(EditAnywhere, BlueprintReadOnly, meta = (DisplayName = "歌曲id"))
		TArray<int32> SongIDList;

	UPROPERTY(EditAnywhere, BlueprintReadOnly, meta = (DisplayName = "绑定UI"))
		FChildPendingActiveObject uisock;
};

USTRUCT()
struct FMusicDelayEffect
{
	GENERATED_BODY()

	TSoftObjectPtr<UParticleSystem> PS;
	FName socketName;
	float PlayTick = 0;
};

UENUM()
enum EMusicGameType
{
	NoneMusic = 0,
	DrumMusic = 1,
	ElectricMusic = 2,
};

// 歌谱解析
class SongSheetAnalyst
{
public:
	void AnalyzeSong(const FMusicConfig* config, int32 songPlayIndex,
		float HitBiasTime, float ShowBiasTime, float AnimBiasTime, int32 BaseKind, float CurrServerTime, float WorldStartTime);

	void LoadBank(const FMusicConfig* config);

	void GetSongInfo(int32 songID, float& startTime, float& showTime, TArray<float>& errorTimeList,
		TArray<float>& evaluationList, TArray<float>& rankList, TArray<float>& awardList, float& hitCD, FString& xmlFile, FString& BankName, FString& EventName);

	void CalculateAllIndex(float CurrServerTime, float WorldStartTime);
	int32 CaluCurrIndex(int32 indexType, float CurrServerTime, float WorldStartTime);

public:
	FString MusicTableName = TEXT("/Game/CSV/MusicGame.MusicGame");
	int32 SongID = -1;
	float StartTime = 0;
	float EndTime = 0;
	float ShowTime = 0;
	TArray<float> ErrorTimeList;
	TArray<float> EvaluationList;
	TArray<float> RankList;
	TArray<float> AwardList;
	float HitCD = 0;
	FString XMLFile;

	TArray<FMusicNote> MusicNoteList;
	int32 ShowEffectNoteIndex = -1;
	int32 AnimNoteIndex = -1;
	int32 canHitIndex = -1;
	float noteTime = 0;

	FString SongBankName;
	FString SongStartEventName;
};

//DS不可复制组件
UCLASS(BlueprintType, Blueprintable)
class  UMusicServerCompBase : public UActorComponent
{
	GENERATED_BODY()

public:
	UMusicServerCompBase();

	virtual void BeginPlay() override;

	template<class ServerComp, class ClientComp>
	static void CreateMusicGameBase(AActor* owner, AUAEPlayerController* PC, EMusicGameType musicType, ServerComp *&SC, ClientComp *&CC, TSubclassOf<UMusicClientCompBase> ClientCompBase)
	{
		if (!owner)
		{
			UE_LOG(LogGameInfo, Log, TEXT("CreateMusicGameBase Not Valid Owner"));
			return;
		}
		if (!ClientCompBase.Get())
		{
			UE_LOG(LogGameInfo, Log, TEXT("CreateMusicGameBase Not Valid ClientCompBase"));
			return;
		}
		// 这里获取蓝图子类的方式要改，可能要走一波异步
		//UClass* ClientCompClass = UBPClassManager::Get()->GetBPClassOverride(ClientComp::StaticClass());
		// DS组件
		SC = owner->FindComponentByClass<ServerComp>();
		if (!SC)
		{
			SC = NewObject<ServerComp>(owner, ServerComp::StaticClass());
			if (SC)
			{
				SC->RegisterComponent();
				SC->MusicType = musicType;
				SC->ClientComponentClass = ClientCompBase;
			}
			else
			{
				UE_LOG(LogGameInfo, Log, TEXT("CreateMusicGame server null"));
				return;
			}
		}

		ASTExtraPlayerController* STEPC = Cast<ASTExtraPlayerController>(PC);
		if (!STEPC) return;
		ACharacter* Character = STEPC->GetPlayerCharacterSafety();
		if (!Character) return;

		// 通信和配置组件
		CC = Character->FindComponentByClass<ClientComp>();
		if (!CC)
		{
			CC = NewObject<ClientComp>(Character, ClientCompBase);
			if (CC)
			{
				CC->SetIsReplicated(true);
				CC->RegisterComponent();
				CC->SetMusicType(musicType);
			}
			else
			{
				UE_LOG(LogGameInfo, Log, TEXT("CreateMusicGame client null"));
				return;
			}
		}

		CC->SetMusicServer(SC);
		SC->AddPlayer(CC);
		// 通知所有CC当前的状态
		SC->RepMusicTime();
	}

	template<class ServerComp, class ClientComp>
	static void ExitMusicGameBase(AActor* owner, AUAEPlayerController* PC, ServerComp *&SC, ClientComp *&CC)
	{
		if (!owner || !PC)
			return;

		ASTExtraPlayerController* STEPC = Cast<ASTExtraPlayerController>(PC);
		if (!STEPC) return;
		ACharacter* Character = STEPC->GetPlayerCharacterSafety();
		if (Character)
		{
			SC = owner->FindComponentByClass<ServerComp>();
			CC = Character->FindComponentByClass<ClientComp>();
			if (SC && CC)
			{
				SC->RemovePlayer(CC);
				CC->StopPlayMusic();
				//MCC->SetOnVehicle(false);
			}
		}
	}

	UPROPERTY()
		TArray<class UMusicClientCompBase*> SeatList;

	void StartMusicTime();
	void RepMusicTime();
	void StartByPlayerBase(float musicStartTime, float musicEndTime);
	void StopByPlayer(UMusicClientCompBase* MCC);

	bool IsStartToPlay();
	bool IsPlayingMusic();
	bool IsAwardTime();
	bool IsEndTime();

	virtual void AddPlayer(UMusicClientCompBase* MCC);
	virtual void RemovePlayer(UMusicClientCompBase* MCC);

	float GetServerTime();

	EMusicGameType MusicType;
	UPROPERTY()
		UClass* ClientComponentClass = nullptr;
	UPROPERTY(BlueprintReadWrite)
		float StartServerTime = -1000;
	float MusicStartTime = 0;
	float MusicEndTime = 0;
	float AwardTime = 0;
	int32 SongStartIndex = -1;

	SongSheetAnalyst SongAnalyst;
};


//DS不可复制组件
UCLASS(BlueprintType, Blueprintable)
class  UMusicServerDrum : public UMusicServerCompBase
{
	GENERATED_BODY()

public:
	UMusicServerDrum();

	virtual void BeginPlay() override;

	static void CreateMusicGame(APawn* owner, AUAEPlayerController* PC, EMusicGameType musicType, TSubclassOf<UMusicClientCompBase> CreateMusicGameBase, bool isMusicPlayer = false);

	static void ExitMusicGame(APawn* owner, AUAEPlayerController* PC);

	void UpdateMusicPlayer();

	void AddPlayerWithMusicPlayer(UMusicClientDrum* MCC, bool bMusicPlayer);
	virtual void RemovePlayer(UMusicClientCompBase* MCC) override;

	float GetServerTime();

public:

	UPROPERTY()
		UMusicClientDrum* MusicPlayer;

};

USTRUCT(BlueprintType)
struct FDelayAddGas
{
	GENERATED_BODY()

public:
	FDelayAddGas()
	{
		Index = -1;
		ServerTime = 0;
	}
	FDelayAddGas(int32 index, float servertime)
	{
		Index = index;
		ServerTime = servertime;
	}
	int32 Index = -1;
	float ServerTime = 0;
};

// 在什么时间播第几首歌曲
USTRUCT(BlueprintType)
struct FMusicGamePlayInfo
{
	GENERATED_BODY()
public:
	FMusicGamePlayInfo()
	{
		WorldTimeStarted = -1000;
		SongPlayIndex = 0;
	}
	UPROPERTY()
		float WorldTimeStarted = -1000;
	UPROPERTY()
		int32 SongPlayIndex;
};


//客户端可复制组件
UCLASS(BlueprintType, Blueprintable)
class  UMusicClientCompBase : public UActorComponent
{
	GENERATED_BODY()

public:
	UMusicClientCompBase();

	static void RecoverSkill(AUAEPlayerController* PC);

	UFUNCTION(BlueprintCallable)
	static void ReconnectRecoverSkill(ASTExtraPlayerController* PC);

	UFUNCTION(BlueprintCallable)
		static bool IsInMusicGame(ASTExtraPlayerController* PC);

	virtual void BeginPlay() override;

	virtual void StopPlayMusic();

	virtual void HandleConsumeItemSkillBreak_Private() {};

	virtual void ReconnectRecover() {};

	virtual bool IsInMusicGamePlay() { return false; }

	virtual AActor* GetAkEventActor() { return GetOwner(); }

	UFUNCTION(Reliable, Client)
		void ClientStopPlayMusic();

	UFUNCTION(BlueprintCallable, Reliable, Server, WithValidation)
		void ServerStartMusic();

	UFUNCTION(BlueprintCallable)
		virtual bool CanStartMusic() { return true; }

	UFUNCTION()
		void OnPlayerHealthStatusChangedDelegate(ECharacterHealthStatus PrevStatus, ECharacterHealthStatus NewStatus);

	UPROPERTY(ReplicatedUsing = OnRep_MusicType, BlueprintReadOnly)
		TEnumAsByte<EMusicGameType> MusicType = EMusicGameType::NoneMusic;

	UFUNCTION()
		virtual void OnRep_MusicType() {}

	virtual void TickComponent(float DeltaTime, enum ELevelTick TickType, FActorComponentTickFunction *ThisTickFunction) override;

	virtual bool CanPlayMusic() { return true; }

	UPROPERTY(ReplicatedUsing = OnRep_MusicGamePlayInfo, BlueprintReadOnly)
		FMusicGamePlayInfo MusicGamePlayInfo;

	SongSheetAnalyst SongAnalyst;

	bool isMusicStarted = false;
	bool isMusicPlaying = false;
	int32 SongAKID = 0;
	UFUNCTION()
		virtual void OnRep_MusicGamePlayInfo();

	UPROPERTY(EditAnywhere, BlueprintReadOnly, meta = (DisplayName = "音乐配置"))
		TMap<TEnumAsByte<EMusicGameType>, FMusicConfig> musicConfig;

	UPROPERTY(EditAnywhere, BlueprintReadOnly, meta = (DisplayName = "表格配置"))
		UUAEDataTable* MusicGameTable;

	UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Music")
		int32 BaseKind = 18;

	UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Music", meta = (DisplayName = "命中提前量"))
		float HitBiasTime = 0.2;

	UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Music", meta = (DisplayName = "提示提前量"))
		float ShowBiasTime = 0.2;

	UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Music", meta = (DisplayName = "上帝动画提前量"))
		float AnimBiasTime = 0.2;

	UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Music", meta = (DisplayName = "鼓面特效播放延迟"))
		float BeatEffectDelayTime = 0.02;

	UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Music", meta = (DisplayName = "颁奖时间"))
		float AwardTime = 0;

	UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "GameNet", meta = (DisplayName = "是否同步到模拟端"))
		bool SimulateComponent = false;


	bool GodPlay = false;
	UFUNCTION(BlueprintCallable)
		bool SwitchGodPlay();

	UPROPERTY()
		FString SongBankName = TEXT("Scene_Activity_DragoonBoat");
	UPROPERTY()
		FString SongStartEventName = TEXT("Play_Music_DragoonBoat");

	UFUNCTION(BlueprintCallable)
		void BeatNote(int32 trackBit, bool byPlayer);
	TMap<int32, float> LastTrackBitTime;

	virtual void OnBeatNote(int32 HitSuccIndex, int32 trackBit, bool FinishRoll, int32 noteIndex) {}

	void SetMusicServer(class UMusicServerCompBase* musicServer);
	void SetMusicType(EMusicGameType musicType);
	void AnalyzeNote();
	void LoadBank();
	void PlayMusic();
	virtual void OnPlayBGMusic() {}
	virtual void OnAnalyzeNote() {}
	void SetWorldTimeStarted(float worldTimeStarted, int32 songPlayIndex, const SongSheetAnalyst& SongAnalyst);
	virtual void OnWorldTimeStartedOnDS() {}
	class UUTSkillManagerComponent* GetSkillManager();
	virtual void OnEnvaluation() {}
	void HitSuccByRank(int32 HitSuccIndex);
	virtual int32 EvaluateRank();
	bool IsCountDowning();

	UPROPERTY()
		UMusicServerCompBase* MusicServer;

	void MoveIndex(int32 IndexType, int32& NoteIndex, float MusicTick);
	virtual void OnGuideEffect(int32 track, int32 noteIndex) {}

	template<typename... ParamTypes>
	void HandleUIMessageBattleWithParam(const FString& UIMessage, ParamTypes... Params)
	{
		AUAEPlayerController* PC = GetOwnerController();
		if (!IsUIRelative())
		{
			UE_LOG(LogGameInfo, Log, TEXT("MusicGame HandleUIMessageBattleWithParam PC null or spectator [%s]"), *UIMessage);
		}
		else if (PC)
		{
			if (sockobj && sockobj->GetActivedSocket())
				sockobj->GetActivedSocket()->HandleUIMessageBattleWithParam(UIMessage, Params...);
			else
				UE_LOG(LogGameInfo, Log, TEXT("MusicGame HandleUIMessageBattleWithParam UI null [%s]"), *UIMessage);
		}
	}

	AUAEPlayerController* GetOwnerController()
	{
		ASTExtraPlayerCharacter* Character = Cast<ASTExtraPlayerCharacter>(GetOwner());
		if (Character)
		{
			AUAEPlayerController* STEPC = Cast<AUAEPlayerController>(Character->GetPlayerControllerSafety());
			return STEPC;
		}
		return nullptr;
	}

	template<typename... ParamTypes>
	void HandleIngameUIMessageBattleWithParam(const FString& UIMessage, const FString& Module, ParamTypes... Params)
	{
		AUAEPlayerController* UAEPC = GetOwnerController();
		if (!IsUIRelative())
		{
			UE_LOG(LogGameInfo, Log, TEXT("MusicGame HandleIngameUIMessageBattleWithParam Character null or spectator [%s]"), *UIMessage);
		}
		else if (UAEPC)
			UAEPC->CastUIMsgWithParam(UIMessage, Module, Params...);
	}

	void HandleUIMessageBattle(const FString& UIMessage)
	{
		AUAEPlayerController* PC = GetOwnerController();
		if (!IsUIRelative())
		{
			UE_LOG(LogGameInfo, Log, TEXT("MusicGame HandleUIMessageBattle PC null or spectator [%s]"), *UIMessage);
		}
		else if (PC)
		{
			if (sockobj && sockobj->GetActivedSocket())
				sockobj->GetActivedSocket()->HandleUIMessageBattle(UIMessage);
			else
				UE_LOG(LogGameInfo, Log, TEXT("MusicGame HandleUIMessageBattle UI null [%s]"), *UIMessage);
		}
	}

	void HandleIngameUIMessageBattle(const FString& UIMessage, const FString& Module)
	{
		AUAEPlayerController* UAEPC = GetOwnerController();
		if (!IsUIRelative())
		{
			UE_LOG(LogGameInfo, Log, TEXT("MusicGame HandleIngameUIMessageBattle PC null or spectator[%s]"), *UIMessage);
		}
		else if (UAEPC)
			UAEPC->CastUIMsg(UIMessage, Module);
	}

	bool IsUIRelative()
	{
		USTExtraGameInstance* GameInstance = Cast<USTExtraGameInstance>(GetWorld()->GetGameInstance());
		if (GameInstance && GameInstance->IsPlayingAnyPlayback())
		{
			return false;
		}
		AUAEPlayerController* UAEPC = GetOwnerController();
		if (UAEPC && UAEPC->IsSpectator())
		{
			return false;
		}
		return true;
	}

	virtual bool IsTickRelativeDerived() { return true; }

	void CreateUI();
	virtual void ShowMusicUI(bool bShow) {}

	// 所有音符
	TArray<FMusicNote> MusicNoteList;

	float GetServerTime();
public:

	float TotalEvaluateValue = 0;

	bool LastMiss = true;
	int32 ComboNum = 0;

	float lastTotalTick = 0;

public:
	UPROPERTY(BlueprintReadOnly)
		UCustomSocketObject* sockobj;
	bool IsShowUI = true;
	UFUNCTION()
		virtual void OnLoadFinished() {}
};

//////////////////////////////////////////////////////////////////////////

//客户端可复制组件
UCLASS(BlueprintType, Blueprintable)
class  UMusicClientDrum : public UMusicClientCompBase
{
	GENERATED_BODY()

public:
	UMusicClientDrum();

	virtual void BeginPlay() override;

	virtual void StopPlayMusic() override;

	virtual void HandleConsumeItemSkillBreak_Private() override;

	virtual void ReconnectRecover() override;

	virtual bool IsTickRelativeDerived() override;

	virtual AActor* GetAkEventActor() override;

	UFUNCTION(BlueprintCallable)
		virtual bool CanStartMusic() override;

	UFUNCTION(BlueprintCallable, Reliable, Server, WithValidation)
		void ServerChangeSeat(bool toMusicPlayer);

	void ServerChangeVehicleSeat();

	UFUNCTION(BlueprintCallable, Reliable, Server, WithValidation)
		void ServerHitSucc(int32 HitSuccIndex);

	UFUNCTION(BlueprintCallable, Reliable, Server, WithValidation)
		void ServerStartDBoost();

	UFUNCTION(BlueprintCallable, Reliable, Client)
		void ClientTriggerSkillEvent();

	bool WaitUntilSkillTrigger = false;
	void TickUntilSkillTrigger();

	virtual void TickComponent(float DeltaTime, enum ELevelTick TickType, FActorComponentTickFunction *ThisTickFunction) override;

	UFUNCTION(BlueprintCallable)
		void SwitchSeat(bool isDrum);

	virtual void OnEnvaluation() override;

	virtual bool CanPlayMusic() { return isOnVehicle; }

public:
	UFUNCTION()
		virtual void OnRep_MusicType() override;
	FTimerHandle TimerHandle;

	// 是否是音游玩家
	UPROPERTY(ReplicatedUsing = OnRep_isMusicPlayer, BlueprintReadOnly)
		bool isMusicPlayer = false;
	
	UFUNCTION()
		void OnRep_isMusicPlayer();
	void SetisMusicPlayer(bool _isMusicPlayer);

	UPROPERTY(ReplicatedUsing = OnRep_VehicleHasPlayer, BlueprintReadOnly)
		bool VehicleHasPlayer = false;
	UFUNCTION()
		void OnRep_VehicleHasPlayer();

	// 是否在载具上
	UPROPERTY(ReplicatedUsing = OnRep_isOnVehicle, BlueprintReadOnly)
		bool isOnVehicle = false;
	UFUNCTION()
		void OnRep_isOnVehicle();

	// 音游开始时间
	UFUNCTION()
		virtual void OnRep_MusicGamePlayInfo() override;

	UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Effect")
		TSoftObjectPtr<UParticleSystem> PS_HitTips;

	UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Effect")
		TArray<TSoftObjectPtr<UParticleSystem>> PS_Beats;

	UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Effect")
		TArray<FName> DrumSocket;

	UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Effect")
		TArray<FName> DrumBeatSocket;

	UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Skill")
		int32 simulateSkillID = 112;

	UPROPERTY(EditAnywhere, Category = PlayMontage)
		TArray<TSoftObjectPtr<UAnimMontage>> AnimMontage;

	UPROPERTY(EditDefaultsOnly, Category = "Music")
		TArray<FString> HitEventName = { TEXT("Play_DragoonBoat_Lo_Drum_Hit") , TEXT("Play_DragoonBoat_Hi_Drum_Hit") , TEXT("Play_DragoonBoat_Drum_Roll")};

	UPROPERTY(EditDefaultsOnly, Category = "Gas")
		float DelaySendGasTime = 0.8;


	UFUNCTION(BlueprintCallable)
		void PlayDrumEffect(const TSoftObjectPtr<UParticleSystem>& PS, FName socketName);

	void DelayPlayDrumEffect(const TSoftObjectPtr<UParticleSystem>& PS, FName socketName, float DelaySecond);
	TQueue<FMusicDelayEffect> DelayEffectQueue;

	virtual void OnBeatNote(int32 HitSuccIndex, int32 trackBit, bool FinishRoll, int32 noteIndex) override;

	void PlayMontageLocal(TArray<TSoftObjectPtr<UAnimMontage>>& AnimMontagePlay, int32 Index);

	UFUNCTION(BlueprintCallable)
		void RefreshBoostGasValue();

	void SetOnVehicle(bool onVehicle);
	void SetBoostGasValue(float CurBoostGasNum);
	virtual void ShowMusicUI(bool bShow) override;
	void SetDrumEffectGuidePosition();
	void ClientChangeSeat();
	bool IsDriver();

	UFUNCTION(BlueprintCallable)
		void SetDrumEffectGuidePositionBP();

	UFUNCTION(BlueprintCallable)
		class ASTExtraDragonBoatVehicle* GetVehicle();
	

	void SetUIMusicComp();

	virtual void OnGuideEffect(int32 track, int32 noteIndex) override;

	UFUNCTION()
		virtual void OnLoadFinished() override;

	void TickDelayGas();
	void TickDelayEffect();

	UMusicServerDrum* GetMusicServer();

public:
	TArray<int32> noteSockIndex;

	TQueue<FDelayAddGas> DelayAddGasQueue;

public:
	UFUNCTION(Reliable, Server, WithValidation)
		void ServerOnFinishMusicGame(uint32 _perfectTime, uint32 _bestTime, uint32 _goodTime, uint32 _missTime);

	//完美的次数
	uint32 perfectTime;
	//很好
	uint32 bestTime;
	//不错
	uint32 goodTime;
	uint32 missTime;

};

// 比赛相关
UCLASS(BlueprintType, Blueprintable)
class  UMusicMatchComp : public UActorComponent
{
	GENERATED_BODY()

public:
	UMusicMatchComp();

	static void CreateMusicMatchASync(AUAEPlayerController* PC, FStreamableDelegate ClassLoadSuccessDelegate);
	static UMusicMatchComp* CreateOrGetMusicMatch(AUAEPlayerController* PC);
	static void ExitMusicMatch(AUAEPlayerController* PC);

public:
	void CreateUI();
	void ShowUI(bool bShow);
	ASTExtraDragonBoatVehicle* GetVehicle();
	// 比赛相关
	void SetMatchState(int32 matchState);
	int32 MatchState = 0;
	void SetMatchInfoLeftTime(int32 matchInfoLeftTime);
	int32 MatchInfoLeftTime = 0;
	UFUNCTION()
		void UpdateMatch();

	UPROPERTY(BlueprintReadOnly)
		UCustomSocketObject* sockobj;

	UPROPERTY(EditAnywhere, BlueprintReadOnly, meta = (DisplayName = "绑定UI"))
		FChildPendingActiveObject uisock;

};