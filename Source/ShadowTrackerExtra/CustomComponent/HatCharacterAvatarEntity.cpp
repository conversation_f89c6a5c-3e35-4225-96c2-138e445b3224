

#include "ShadowTrackerExtra.h"
#include "HatCharacterAvatarEntity.h"
#include "CharacterAvatarComponent.h"

void UHatCharacterAvatarEntity::CollectDesiredResource(TMap<EResourceType, FResourceList>& CollectRes, bool DisableCheck /* = false */)
{
#if UE5_OR_LATER
	check(bIsBPGenerated || DisableCheck || GetClass()->GetFName() == StaticClass()->GetFName());
#else
	check(bIsBPGenerated || DisableCheck || GetClass()->GetFName() == GetPrivateStaticClass()->GetFName());
#endif
	CollectMeshResource(CollectRes);
	CollectMaterialResource(CollectRes);
	CollectAnimResource(CollectRes);
	CollectParticleResource(CollectRes);
	CollectAudioResource(CollectRes);
}

void UHatCharacterAvatarEntity::ApplyAnimation()
{
	Super::ApplyAnimation();

	UCharacterAvatarComponent* CharAvatarComp = Cast<UCharacterAvatarComponent>(AvatarComponent);
	if (!CharAvatarComp)
	{
		return;
	}
	CharAvatarComp->CheckOverrideRigidBodyAsset();
}

void UHatCharacterAvatarEntity::ResetEntityBeforePushToPool(bool DisableCheck)
{
#if UE5_OR_LATER
	check(bIsBPGenerated || DisableCheck || GetClass()->GetFName() == StaticClass()->GetFName());
#else
	check(bIsBPGenerated || DisableCheck || GetClass()->GetFName() == GetPrivateStaticClass()->GetFName());
#endif
	Super::ResetEntityBeforePushToPool(true);
}
