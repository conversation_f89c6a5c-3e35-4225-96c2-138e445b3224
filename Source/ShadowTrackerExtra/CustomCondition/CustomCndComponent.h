// Fill out your copyright notice in the Description page of Project Settings.

#pragma once

#include "CoreMinimal.h"
#include "Components/ActorComponent.h"
#include "CustomCndWrapper.h"
#include "UTSkillInterface.h"
#include "CustomCndComponent.generated.h"

DECLARE_LOG_CATEGORY_EXTERN(LogCustomCndComponent, Log, All)


USTRUCT(BlueprintType)
struct FCustomCndWrappperIns
{
	GENERATED_USTRUCT_BODY()

public:
	UPROPERTY(EditAnywhere, Instanced, BlueprintReadWrite)
		UCustomCndWrapper*  Wrapper = NULL;

	UPROPERTY(EditAnywhere, BlueprintReadWrite)
		int32  CndWrapperId = 0;
};

UCLASS(Blueprintable, BlueprintType, ClassGroup=(Custom), meta=(BlueprintSpawnableComponent) )
class  UCustomCndComponent : public UActorComponent
{
	GENERATED_BODY()

public:	
	// Sets default values for this component's properties
	UCustomCndComponent();

	UPROPERTY(EditAnywhere, BlueprintReadWrite)
		TArray<FCustomCndWrappperIns>  CndWrappers;

protected:
	// Called when the game starts
	virtual void BeginPlay() override;
	UFUNCTION()
		void OnWeaponGetOwnerActor(AActor* OwnerActor);
	UFUNCTION()
		void OnOwnerPlayerSkillFinishedDelegate(UTSkillStopReason StopReason, int32 SkillUID, bool HasThrownGrenade);

	UPROPERTY()
		class ASTExtraPlayerCharacter* WeaponOwnerPlayer=nullptr;

public:	
	// Called every frame
	virtual void TickComponent(float DeltaTime, ELevelTick TickType, FActorComponentTickFunction* ThisTickFunction) override;
	virtual void EndPlay(const EEndPlayReason::Type EndPlayReason) override;
	UFUNCTION(BlueprintNativeEvent, BlueprintCallable)
		void RegisterCndWrappers();

	UFUNCTION(BlueprintCallable)
		void AddCndWrapper(const FString &Path, int32 Id);

	UFUNCTION(BlueprintCallable)
		void RemoveCndWrapper(int32 Id);

	UFUNCTION(BlueprintNativeEvent, BlueprintCallable)
		void OnReceiveEvent(ECustomCndEventType Type);

	UFUNCTION(BlueprintNativeEvent)
		void HandleStartFireCpp();
	UFUNCTION(BlueprintNativeEvent)
		void HandleStopFireCpp();
	UFUNCTION(BlueprintCallable)
		void OnDyunamicAddOrRemoveToWeapon(class ASTExtraShootWeapon* ShootWeapon, bool bAdd);

	UPROPERTY(EditAnywhere, BlueprintReadWrite)
		bool bEnableCndServerOnly = false;

	UPROPERTY(BlueprintReadWrite)
		bool bIsStartFireEnabled = true;
};
