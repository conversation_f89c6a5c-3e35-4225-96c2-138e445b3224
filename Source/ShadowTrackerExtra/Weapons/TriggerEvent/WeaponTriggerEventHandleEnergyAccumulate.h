// Fill out your copyright notice in the Description page of Project Settings.

#pragma once

#include "CoreMinimal.h"
#include "WeaponTriggerEventHandleBase.h"
#include "WeaponTriggerEventHandleEnergyAccumulate.generated.h"

/**
* 武器事件处理器射击相关
*/

UCLASS(ClassGroup = (WeaponSkill), meta = (BlueprintSpawnableComponent))
class  UWeaponTriggerEventHandleEnergyAccumulate : public UWeaponTriggerEventHandleBase
{
	GENERATED_BODY()
public:

protected:
	UFUNCTION(BlueprintCallable)
		virtual void ProcessTriggerEventInternal(EWeaponTriggerEvent Event, const FString& EventData) override;
};
