// Fill out your copyright notice in the Description page of Project Settings.

#include "ShadowTrackerExtra.h"
#include "WeaponTriggerEventHandleSkill.h"
#include "AddNewSkillToOwnerInterface.h"
#include "UTSkill.h"
#include "Player/STExtraPlayerCharacter.h"
#include "UTSkillInterface.h"
#include "Character/PersistBaseComponent.h"
#include "Character/PersistSkill/PersistEffectSkill.h"

//触发技能新接口
int32 GCVar_EnableTriggerWeaponSkillNewMethod = 1;
FAutoConsoleVariableRef CVarEnableTriggerWeaponSkillNewMethod(
	TEXT("weapon.EnableTriggerWeaponSkillNewMethod"),
	GCVar_EnableTriggerWeaponSkillNewMethod,
	TEXT("1 [default]\n"),
	ECVF_Default);


#define USE_SPECIFIC_SKILL_FILTER 0


UTSkillEventType UWeaponTriggerEventHandleSkill::GetSkillEventByWeaponEvent(EWeaponTriggerEvent Event) const
{
	UTSkillEventType SkillEvent = UTSkillEventType::SET_DEFAULT_MAX;
	switch (Event)
	{
	case EWeaponTriggerEvent::EWeaponTriggerEvent_PressFuncBtn:
	case EWeaponTriggerEvent::EWeaponTriggerEvent_TriggerSpesificSkill:
	{
		SkillEvent = UTSkillEventType::SET_KEY_DOWN;
		break;
	}
	case EWeaponTriggerEvent::EWeaponTriggerEvent_ReleaseFuncBtn:
	case EWeaponTriggerEvent::EWeaponTriggerEvent_StopSpesificSkill:
	{
		SkillEvent = UTSkillEventType::SET_KEY_UP;
		break;
	}
	default:
		break;
	}
	return SkillEvent;
}

void UWeaponTriggerEventHandleSkill::OnUnEquip(class IWeaponOwnerProxyFactory* ProxyFactory, bool bIsSimulate)
{
	Super::OnUnEquip(ProxyFactory, bIsSimulate);

	if (GetOwnerRole() == ROLE_Authority)
	{
		ASTExtraWeapon* WeapOwner = TryGetOwnerWeapon();

		ASTExtraBaseCharacter* PawnOwner = WeapOwner ? Cast<ASTExtraBaseCharacter>(WeapOwner->GetOwnerPawn()) : nullptr;

		UUTSkillManagerComponent* SkillManager = PawnOwner ? PawnOwner->GetSkillManager() : nullptr;

		if (SkillManager)
		{
			for (FItemSkillsConfig Config : GetSelfWeaponSkillConfigList(WeapOwner))
			{
				int32 const SkillUID = SkillManager->GetSkillUIDBySoftClass(Config.SkillTemplateClass);

				if (SkillUID != INVALID_SKILL_INDEX)
				{
					SkillManager->StopSkillWithID(SkillManager->GetSkillIndexByUID(SkillUID), UTSkillStopReason::SkillStopReason_Manul);
				}
			}
		}
	}
}

//
//bool UWeaponTriggerEventHandleSkill::IsActive() const
//{
//	const ASTExtraWeapon* const Weapon = TryGetOwnerWeapon();
//	const ASTExtraPlayerController* const STPC = Weapon ? Cast<ASTExtraPlayerController>(Weapon->GetOwnerPlayerController()) : nullptr;
//	return Super::IsActive() && STPC && !STPC->bWeaponIsActivatedForThrowing;
//}

void UWeaponTriggerEventHandleSkill::ProcessTriggerEventInternal(EWeaponTriggerEvent Event, const FString& EventData)
{
	if (!IsActive())
	{
		return;
	}
	UE_LOG(LogWeapon, Log, TEXT("UWeaponTriggerEventHandleSkill::ProcessTriggerEventInternal Weapon[%s]Event[%s]EventData[%s]"), *GetOwnerWeaponInfo(), *GETENUMSTRING("EWeaponTriggerEvent", Event), *EventData);

	//判断是否能是否技能
	if (!TryGetOwnerWeapon())
	{
		UE_LOG(LogWeapon, Warning, TEXT("UWeaponTriggerEventHandleSkill::ProcessTriggerEventInternal TryGetOwnerWeapon() NULL!"));
		return;
	}

	ASTExtraPlayerCharacter* Player = Cast<ASTExtraPlayerCharacter>(TryGetOwnerWeapon()->GetOwnerPawn());
	if (!Player)
	{
		UE_LOG(LogWeapon, Warning, TEXT("UWeaponTriggerEventHandleSkill::ProcessTriggerEventInternal Player NULL!"));
		return;
	}
	IWeaponOwnerInterface* WOI = Cast<IWeaponOwnerInterface>(Player);
	if (!WOI)
	{
		UE_LOG(LogWeapon, Warning, TEXT("UWeaponTriggerEventHandleSkill::ProcessTriggerEventInternal WOI NULL!"));
		return;
	}
	if (!WOI->CheckWeaponCanTriggerSkill(TryGetOwnerWeapon(), Event))
	{
		UE_LOG(LogWeapon, Warning, TEXT("UWeaponTriggerEventHandleSkill::ProcessTriggerEventInternal CheckWeaponCanTriggerSkill false!"));
		return;
	}
	UUTSkillManagerComponent* SkillComp = Player->GetSkillManagerComponent();
	if (!SkillComp)
	{
		UE_LOG(LogWeapon, Warning, TEXT("UWeaponTriggerEventHandleSkill::ProcessTriggerEventInternal SkillComp NULL!"));
		return;
	}
	switch (Event)
	{
	case EWeaponTriggerEvent::EWeaponTriggerEvent_None:
		break;
	case EWeaponTriggerEvent::EWeaponTriggerEvent_PressFuncBtn:
	case EWeaponTriggerEvent::EWeaponTriggerEvent_ReleaseFuncBtn:
	{
		UTSkillEventType SkillEvent = GetSkillEventByWeaponEvent(Event);
		if (GCVar_EnableTriggerWeaponSkillNewMethod > 0)
		{
			if (IsTriggerSkillConditionOK())
			{
				TArray<FItemSkillsConfig> SkillList = GetSelfWeaponSkillConfigList(TryGetOwnerWeapon());
				for (const FItemSkillsConfig& Skill : SkillList)
				{

#if USE_SPECIFIC_SKILL_FILTER
					// 不能触发特殊技能
					if (IsSelfWeaponSpecificSkillConfig(TryGetOwnerWeapon(), Skill))
					{
						continue;
					}
#endif

					int32 const SkillUID = SkillComp->GetSkillUIDBySoftClass(Skill.SkillTemplateClass);
					if (SkillUID != INVALID_SKILL_INDEX)
					{
						if (SkillComp->GetSkillByUID(SkillUID))
						{
							Player->TriggerSkillEvent(Skill.SkillTemplateClass, SkillEvent);
						}
					}

				}
			}
		}
		else
		{
			TArray<int32> SkillIndexList = GetSelfWeaponSkillIDList(TryGetOwnerWeapon());
			if (SkillIndexList.Num() > 0)
			{
				int32 ChosenIdx = SkillIndexList[0];
				if (SkillComp->ShouldTriggerEvent(ChosenIdx, SkillEvent))
				{
					UE_LOG(LogWeapon, Log, TEXT("UWeaponTriggerEventHandleSkill::ProcessTriggerEventInternal TriggerEvent ChosenIdx[%d]SkillEvent[%s]"), ChosenIdx, *GETENUMSTRING("UTSkillEventType", SkillEvent));
					SkillComp->TriggerEvent(ChosenIdx, SkillEvent);
				}
				else
				{
					UE_LOG(LogWeapon, Warning, TEXT("UWeaponTriggerEventHandleSkill::ProcessTriggerEventInternal ShouldTriggerEvent Failed!ChosenIdx[%d]SkillEvent[%s]"), ChosenIdx, *GETENUMSTRING("UTSkillEventType", SkillEvent));
				}
			}
		}
	}
		break;
	case EWeaponTriggerEvent::EWeaponTriggerEvent_TriggerSpesificSkill:
	case EWeaponTriggerEvent::EWeaponTriggerEvent_StopSpesificSkill:
	{
		UTSkillEventType SkillEvent = GetSkillEventByWeaponEvent(Event);
		FItemSkillsConfig SpesificSkill = GetSelfWeaponSpecificSkillConfig(TryGetOwnerWeapon(), EventData);
		int32 const SkillUID = SkillComp->GetSkillUIDBySoftClass(SpesificSkill.SkillTemplateClass);
		if (SkillUID != INVALID_SKILL_INDEX && SkillComp->GetSkillByUID(SkillUID))
		{
			Player->TriggerSkillEvent(SpesificSkill.SkillTemplateClass, SkillEvent);
		}
	}
		break;
	default:
		break;
	}

	if (ASTExtraWeapon* WeaponOwner = TryGetOwnerWeapon())
	{
		for (FWeaponSkillConfig& SkillConfig : WeaponOwner->WeaponSkillConfigs)
		{
			if (SkillConfig.ItemSkillInstance && SkillConfig.bIsBindFireBtn)
			{
				switch (Event)
				{
				case EWeaponTriggerEvent::EWeaponTriggerEvent_PressFuncBtn:
#if defined(GMP_API)
					if (auto PESkill = Cast<UPersistEffectSkill>(SkillConfig.ItemSkillInstance))
					{
						if (!PESkill->IsActivating())
						{
							PESkill->ActivateSkill();
						}
						FGMPHelper::SendObjectMessage(PESkill, MSGKEY_TYPE::MAKE_MSGKEY_TYPE("UGC.PersistEffect.SkillInput"), (int32)EPersistEffectClientEvent::InputPress);
					}
#endif
					break;
				case EWeaponTriggerEvent::EWeaponTriggerEvent_ReleaseFuncBtn:
#if defined(GMP_API)
					if (auto PESkill = Cast<UPersistEffectSkill>(SkillConfig.ItemSkillInstance))
					{
						FGMPHelper::SendObjectMessage(PESkill, MSGKEY_TYPE::MAKE_MSGKEY_TYPE("UGC.PersistEffect.SkillInput"), (int32)EPersistEffectClientEvent::InputRelease);
					}
#endif
					break;
				default: ;
				}
			}
		}
	}
}

TArray<FItemSkillsConfig> UWeaponTriggerEventHandleSkill::GetSelfWeaponSkillConfigList(class ASTExtraWeapon* Weapon)
{
	if (!Weapon || SkillConfigList.Num() > 0)
	{
		return SkillConfigList;
	}
	TArray<FItemSkillsConfig> Ret;
	do
	{
		FItemDefineID ID = Weapon->GetItemDefineID();
		UItemHandleBase* ItemHandle = nullptr;
		if (ROLE_Authority == Weapon->Role)
		{
			ItemHandle = Weapon->GetBackpackItemHandle();
		}
		else
		{
			ItemHandle = Weapon->GetClientWeaponHandle();
		}
		if (!ItemHandle || !ItemHandle->GetClass())
		{
			UE_LOG(LogWeapon, Warning, TEXT("UWeaponTriggerEventHandleSkill::GetSelfWeaponSkillConfigList ItemHandle or Handle Class NULL!"));
			break;
		}
		if (ItemHandle->GetClass() && ItemHandle->GetClass()->ImplementsInterface(UAddNewSkillToOwnerInterface::StaticClass()))
		{
			if (ItemHandle->IsTemplate())
			{
				TScriptInterface<IItemContainerInterface> Backpack(Weapon->GetBackpackComponent());

				UBattleItemHandleBase::FItemOwnerOverrider Overrider(ItemHandle, Backpack, ID);

				Ret = IAddNewSkillToOwnerInterface::Execute_GetSkillTemplates(ItemHandle);
			}
			else
			{
				Ret = IAddNewSkillToOwnerInterface::Execute_GetSkillTemplates(ItemHandle);
			}
		}
	} while (false);
	return Ret;
}

FItemSkillsConfig UWeaponTriggerEventHandleSkill::GetSelfWeaponSpecificSkillConfig(class ASTExtraWeapon* Weapon, const FString& EventData)
{
	FItemSkillsConfig RetSkill;
	if (!Weapon)
	{
		return RetSkill;
	}
	do
	{
		FItemDefineID ID = Weapon->GetItemDefineID();
		UItemHandleBase* ItemHandle = nullptr;
		if (ROLE_Authority == Weapon->Role)
		{
			ItemHandle = Weapon->GetBackpackItemHandle();
		}
		else
		{
			ItemHandle = Weapon->GetClientWeaponHandle();
		}
		if (!ItemHandle || !ItemHandle->GetClass())
		{
			UE_LOG(LogWeapon, Warning, TEXT("UWeaponTriggerEventHandleSkill::GetSelfWeaponSpecificSkillConfig ItemHandle or Handle Class NULL!"));
			break;
		}
		if (ItemHandle->GetClass() && ItemHandle->GetClass()->ImplementsInterface(UAddNewSkillToOwnerInterface::StaticClass()))
		{
			if (ItemHandle->IsTemplate())
			{
				TScriptInterface<IItemContainerInterface> Backpack(Weapon->GetBackpackComponent());

				UBattleItemHandleBase::FItemOwnerOverrider Overrider(ItemHandle, Backpack, ID);

				RetSkill = IAddNewSkillToOwnerInterface::Execute_GetSpecificSkillTemplateByData(ItemHandle, EventData);
			}
			else
			{
				RetSkill = IAddNewSkillToOwnerInterface::Execute_GetSpecificSkillTemplateByData(ItemHandle, EventData);
			}
		}
	} while (false);
	return RetSkill;
}

bool UWeaponTriggerEventHandleSkill::IsSelfWeaponSpecificSkillConfig(ASTExtraWeapon* Weapon, const FItemSkillsConfig& InSkillConfig)
{
	if (!Weapon)
	{
		return false;
	}

	FItemDefineID ID = Weapon->GetItemDefineID();
	UItemHandleBase* ItemHandle = nullptr;
	if (ROLE_Authority == Weapon->Role)
	{
		ItemHandle = Weapon->GetBackpackItemHandle();
	}
	else
	{
		ItemHandle = Weapon->GetClientWeaponHandle();
	}
	if (!ItemHandle || !ItemHandle->GetClass())
	{
		UE_LOG(LogWeapon, Warning, TEXT("UWeaponTriggerEventHandleSkill::IsSelfWeaponSpecificSkillConfig ItemHandle or Handle Class NULL!"));
		return false;
	}

	if (ItemHandle->GetClass() && ItemHandle->GetClass()->ImplementsInterface(UAddNewSkillToOwnerInterface::StaticClass()))
	{
		if (ItemHandle->IsTemplate())
		{
			TScriptInterface<IItemContainerInterface> Backpack(Weapon->GetBackpackComponent());
			UBattleItemHandleBase::FItemOwnerOverrider Overrider(ItemHandle, Backpack, ID);
			return IAddNewSkillToOwnerInterface::Execute_IsSpecificSkill(ItemHandle, InSkillConfig);
		}
		else
		{
			return IAddNewSkillToOwnerInterface::Execute_IsSpecificSkill(ItemHandle, InSkillConfig);
		}
	}

	return false;
}

bool UWeaponTriggerEventHandleSkill::IsTriggerSkillConditionOK_Implementation()
{
	return true;
}

void UWeaponTriggerEventHandleSkill::OnRegister()
{
	Super::OnRegister();

	if (ASTExtraWeapon* const Weap = Cast<ASTExtraWeapon>(GetOwner()))
	{
		Weap->SkillHandleComp = this;
	}
}

void UWeaponTriggerEventHandleSkill::OnUnregister()
{
	Super::OnUnregister();

	if (ASTExtraWeapon* const Weap = Cast<ASTExtraWeapon>(GetOwner()))
	{
#if WITH_EDITOR
		ensureMsgf(Weap->SkillHandleComp, TEXT("UWeaponTriggerEventHandleSkill component must be registered to weapon->SkillHandleComp  %s"), *GetPathNameSafe(Weap));
#else
		checkf(Weap->SkillHandleComp, TEXT("UWeaponTriggerEventHandleSkill component must be registered to weapon->SkillHandleComp  %s"), *GetPathNameSafe(Weap));
#endif

		Weap->SkillHandleComp = nullptr;
	}
}

TArray<int32> UWeaponTriggerEventHandleSkill::GetSelfWeaponSkillIDList(class ASTExtraWeapon* Weapon)
{
	if (bSkillIndicesInitialized)
	{
		return SkillIndices;
	}

	if (SkillConfigList.Num() == 0)
	{
		SkillIndices = GetWeaponSkillIDList(Weapon);

		bSkillIndicesInitialized = true;

		return SkillIndices;
	}

	bSkillIndicesInitialized = true;

	for (FItemSkillsConfig& SkillConfig : SkillConfigList)
	{
		int32 SkillIndex = GetWeaponSkillIndexBySoftClassPtr(Weapon, SkillConfig.SkillTemplateClass);

		if (SkillIndex >= 0)
		{
			SkillIndices.Add(SkillIndex);
		}
	}

	return SkillIndices;
}

TArray<int32> UWeaponTriggerEventHandleSkill::GetWeaponSkillIDList(ASTExtraWeapon* Weapon)
{
	TArray<int32> IDList;
	if (!Weapon)
	{
		UE_LOG(LogWeapon, Warning, TEXT("UWeaponTriggerEventHandleSkill::GetWeaponSkillIDList Weapon NULL!"));
		return IDList;
	}
	UE_LOG(LogWeapon, Log, TEXT("UWeaponTriggerEventHandleSkill::GetWeaponSkillIDList Weapon[%s]"), *Weapon->GetWeaponDetailInfo());
	do
	{
		FItemDefineID ID = Weapon->GetItemDefineID();
		UItemHandleBase* ItemHandle = nullptr;
		if (ROLE_Authority == Weapon->Role)
		{
			ItemHandle = Weapon->GetBackpackItemHandle();
		}
		else
		{
			ItemHandle = Weapon->GetClientWeaponHandle();
		}
		if (!ItemHandle || !ItemHandle->GetClass())
		{
			UE_LOG(LogWeapon, Warning, TEXT("UWeaponTriggerEventHandleSkill::GetWeaponSkillIDList ItemHandle NULL!"));
			break;
		}
		if (ItemHandle->GetClass()->ImplementsInterface(UAddNewSkillToOwnerInterface::StaticClass()))
		{
			TArray<FItemSkillsConfig> ItemSkillsConfig = IAddNewSkillToOwnerInterface::Execute_GetSkillTemplates(ItemHandle);
			for (FItemSkillsConfig& SkillConfig : ItemSkillsConfig)
			{
				int32 SkillIndex = GetWeaponSkillIndexBySoftClassPtr(Weapon, SkillConfig.SkillTemplateClass);
				if (SkillIndex >= 0)
				{
					IDList.Add(SkillIndex);
				}
			}
		}
	} while (false);
	return IDList;
}

int32 UWeaponTriggerEventHandleSkill::GetWeaponSkillIndexBySoftClassPtr(ASTExtraWeapon* Weapon, TSoftClassPtr<AUTSkill>& ClassPtr)
{
	int32 SkillIndex = -1;
	do 
	{
		if (!Weapon)
		{
			UE_LOG(LogWeapon, Warning, TEXT("UWeaponTriggerEventHandleSkill::GetWeaponSkillIndexBySoftClassPtr Weapon NULL!"));
			break;
		}
		ASTExtraPlayerCharacter* Player = Cast<ASTExtraPlayerCharacter>(Weapon->GetOwnerPawn());
		if (!Player)
		{
			UE_LOG(LogWeapon, Warning, TEXT("UWeaponTriggerEventHandleSkill::GetWeaponSkillIndexBySoftClassPtr Player NULL!"));
			break;
		}
		UUTSkillManagerComponent* SkillComp = Player->GetSkillManagerComponent();
		if (!SkillComp)
		{
			UE_LOG(LogWeapon, Warning, TEXT("UWeaponTriggerEventHandleSkill::GetWeaponSkillIndexBySoftClassPtr SkillComp NULL!"));
			break;
		}
		UClass* SkillClass = ClassPtr.Get();
		if (!SkillClass)
		{
			UE_LOG(LogWeapon, Warning, TEXT("UWeaponTriggerEventHandleSkill::GetWeaponSkillIndexBySoftClassPtr SkillClass NULL!ClassPtr[%s]"), *ClassPtr.ToSoftObjectPath().ToString());
			break;
		}
		SkillIndex = SkillComp->GetSkillIndexByClass(SkillClass);
		UE_LOG(LogWeapon, Log, TEXT("UWeaponTriggerEventHandleSkill::GetWeaponSkillIndexBySoftClassPtr SkillIndex[%d]"), SkillIndex);
	} while (false);
	return SkillIndex;
}