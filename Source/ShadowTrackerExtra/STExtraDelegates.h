// Copyright 1998-2017 Epic Games, Inc. All Rights Reserved.

#pragma once
#include "Delegates/Delegate.h"
#include "Game/STExtraGameStateBase.h"
#include "Player/STExtraPlayerController.h"
#include "Player/STExtraPlayerCharacter.h"
#include "Player/STExtraPlayerState.h"

class SHADOWTRACKEREXTRA_API FSTExtraDelegates
{
public:
#if WITH_EDITOR
	DECLARE_MULTICAST_DELEGATE_TwoParams(FOneFStringDel_EDITOR, const FString&, UObject*);
	static FOneFStringDel_EDITOR LoadUIModule_EDITOR;
	static FOneFStringDel_EDITOR UnloadUIModule_EDITOR;
	DECLARE_MULTICAST_DELEGATE_ThreeParams(FTwoFStringDel_EDITOR, const FString&, const FName&, UObject*);
	static FTwoFStringDel_EDITOR ShowUI_EDITOR;
	static FTwoFStringDel_EDITOR HideUI_EDITOR;
#endif
	DECLARE_DELEGATE_OneParam(FOneFStringDel, const FString&);
	static FOneFStringDel LoadUIModule;
	static FOneFStringDel UnloadUIModule;

	DECLARE_DELEGATE_TwoParams(FTwoFStringDel, const FString&, const FName&);
	static FTwoFStringDel ShowUI;
	static FTwoFStringDel HideUI;

	DECLARE_MULTICAST_DELEGATE_OneParam(FOnCreateGameStateBase, ASTExtraGameStateBase*);
	static FOnCreateGameStateBase OnCreateGameStateBase;

	DECLARE_MULTICAST_DELEGATE_OneParam(FOnGameStateBaseBeginPlay, ASTExtraGameStateBase*);
	static FOnGameStateBaseBeginPlay OnGameStateBaseBeginPlay;

	DECLARE_MULTICAST_DELEGATE_OneParam(FOnPlayerControllerInitialized, ASTExtraPlayerController*);
	static FOnPlayerControllerInitialized OnPlayerControllerInitialized;

	DECLARE_MULTICAST_DELEGATE_OneParam(FOnPlayerPawnInitialized, ASTExtraPlayerCharacter*);
	static FOnPlayerPawnInitialized OnPlayerPawnInitialized;

	DECLARE_MULTICAST_DELEGATE_OneParam(FOnPlayerStateInitialized, ASTExtraPlayerState*);
	static FOnPlayerStateInitialized OnPlayerStateInitialized;

	// ASTExtraPlayerState、ASTExtraPlayerController、ASTExtraPlayerCharacter三者在本地都有了
	DECLARE_MULTICAST_DELEGATE_OneParam(FPostLocalMainCharInitialized, ASTExtraPlayerController*);
	static FPostLocalMainCharInitialized PostLocalMainCharInitialized;

	DECLARE_MULTICAST_DELEGATE_ThreeParams(FOnBattleStateChanged, UStateMachineComponent*, EStateType, EStateType);
	static FOnBattleStateChanged OnBattleStateChanged;
};
