// Fill out your copyright notice in the Description page of Project Settings.

#include "ShadowTrackerExtra.h"
#include "GenericPlatform/GenericPlatformHttp.h"
#include "Game/STExtraGameStateBase.h"
#include "ItemsBoxConfigDataAsset.h"


UItemsBoxConfigDataAsset::UItemsBoxConfigDataAsset()
{
	DataBase = nullptr;
	TablePath = TEXT("/Game/CSV/");
}
void UItemsBoxConfigDataAsset::InitDataTable(FString TableName)
{
	FString FullPath = FString::Printf(TEXT("%s%s.%s"),*TablePath,*TableName,*TableName);
	DataBase= LoadObject<UDataTable>(nullptr, *FullPath);
	if (!DataBase)
	{
		UE_LOG(LogPickUp,Warning,TEXT("UItemsBoxConfigDataAsset::InitDataTable Failed!FullPath[%s]"),*FullPath);
	}

	UE_LOG(LogGameInfo, Warning, TEXT("UItemsBoxConfigDataAsset::InitDataTable %s"),*GetName());

	UWorld* MyWorld = GetWorld();
	if (MyWorld != nullptr)
	{

		MyWorld->GetTimerManager().ClearTimer(TimerHandle_Init);
		MyWorld->GetTimerManager().SetTimer(TimerHandle_Init, this, &UItemsBoxConfigDataAsset::OnDelayInitTimer, 0.1f);

		OnDelayInitTimer();

	}
	else
	{
		UE_LOG(LogPickUp, Warning, TEXT("UItemsBoxConfigDataAsset::InitDataTable Failed! World is null!"));
	}
}

void UItemsBoxConfigDataAsset::ReplaceRegex(const FString& IDIPItemsBoxConfigs)
{
	FRegexPattern PatternGroupConfig(TEXT("(\\w*)=\\(GroupName=(\\w*?),ConfigItems=\\((.*?)\\)(,FPPConfigItems=\\((.*?)\\))?\\)"));//"(\\w*)=\\(GroupName=(\\w*?),ConfigItems=\\((.*?)\\)\\)"));
	FRegexMatcher MatcherGroupConfig(PatternGroupConfig, IDIPItemsBoxConfigs);

	UE_LOG(LogPickUp, Warning, TEXT("UItemsBoxConfigDataAsset::ReplaceRegex IDIPItemsBoxConfigs[%s] "), *IDIPItemsBoxConfigs);

	ASTExtraGameStateBase* STGameState = Cast<ASTExtraGameStateBase>(GetWorld() ? GetWorld()->GetGameState() : nullptr);
	bool IsFPPGameMode = STGameState && STGameState->IsFPPGameMode;

	while (MatcherGroupConfig.FindNext())
	{
		FString GroupConfigTotal = MatcherGroupConfig.GetCaptureGroup(0);
		FString GroupConfigName = MatcherGroupConfig.GetCaptureGroup(1);
		FString GroupName = MatcherGroupConfig.GetCaptureGroup(2);
		FString ConfigItems = MatcherGroupConfig.GetCaptureGroup(3);
		FString FPPConfigItemsTotal = MatcherGroupConfig.GetCaptureGroup(4);
		FString FPPConfigItems = MatcherGroupConfig.GetCaptureGroup(5);
		UE_LOG(LogPickUp, Warning, TEXT("UItemsBoxConfigDataAsset::ReplaceRegex GroupConfigTotal[%s] "), *GroupConfigTotal);
		UE_LOG(LogPickUp, Warning, TEXT("UItemsBoxConfigDataAsset::ReplaceRegex GroupConfigName[%s] GroupName[%s] ConfigItems[%s] FPPConfigItemsTotal[%s] FPPConfigItems[%s]")
			, *GroupConfigName, *GroupName, *ConfigItems, *FPPConfigItemsTotal, *FPPConfigItems);


		TArray<FItemsBoxConfigDataAssetConfigGroup>* GroupConfigs = &AirDropBoxGroupConfigs;
		if (GroupConfigName == TEXT("AirDropBoxGroupConfigs"))
		{
			GroupConfigs = &AirDropBoxGroupConfigs;
		}
		else if (GroupConfigName == TEXT("SuperAirDropBoxGroupConfigs"))
		{
			GroupConfigs = &SuperAirDropBoxGroupConfigs;
		}
		else if (GroupConfigName == TEXT("XmasAirDropBoxGroupConfigs"))
		{
			GroupConfigs = &XmasAirDropBoxGroupConfigs;
		}
		else if (GroupConfigName == TEXT("TreasureBoxGroupConfigs"))
		{
			GroupConfigs = &TreasureBoxGroupConfigs;
		}
		else if (GroupConfigName == TEXT("ChannelBuoyBoxGroupConfigs"))
		{
			GroupConfigs = &ChannelBuoyBoxGroupConfigs;
		}
		else if (GroupConfigName == TEXT("StarGameDropBoxGroupConfigs"))
		{
			GroupConfigs = &StarGameDropBoxGroupConfigs;
		}

		if (IsFPPGameMode && !FPPConfigItems.IsEmpty())
		{
			UE_LOG(LogPickUp, Warning, TEXT("UItemsBoxConfigDataAsset::ReplaceRegex IsFPPGameMode=[%d] FPPConfigItems[%s] "), (int32)IsFPPGameMode, *FPPConfigItems);
			ReplaceGroupConfigs(*GroupConfigs, GroupName, FPPConfigItems, GroupConfigName);
		}
		else
		{
			UE_LOG(LogPickUp, Warning, TEXT("UItemsBoxConfigDataAsset::ReplaceRegex IsFPPGameMode=[%d] ConfigItems[%s] "), (int32)IsFPPGameMode, *ConfigItems);
			ReplaceGroupConfigs(*GroupConfigs, GroupName, ConfigItems, GroupConfigName);
		}
	}

	ReplaceItemsBoxDataTable(IDIPItemsBoxConfigs);

}

void UItemsBoxConfigDataAsset::ReplaceItemsBoxDataTable(const FString& IDIPItemsBoxConfigs)
{
	UE_LOG(LogPickUp, Warning, TEXT("UItemsBoxConfigDataAsset::ReplaceItemsBoxDataTable IDIPItemsBoxConfigs[%s]"), *IDIPItemsBoxConfigs);

	FRegexPattern PatternItemsBoxDataTable(TEXT("ItemsBoxDataTable=\\((.*?)\\)"));
	FRegexMatcher MatcherItemsBoxDataTable(PatternItemsBoxDataTable, IDIPItemsBoxConfigs);
	bool IsMatch = MatcherItemsBoxDataTable.FindNext();

	if (IsMatch)
	{
		FString ItemsBoxDataTableTotal = MatcherItemsBoxDataTable.GetCaptureGroup(0);
		FString ItemsBoxDataTable = MatcherItemsBoxDataTable.GetCaptureGroup(1);
		UE_LOG(LogPickUp, Warning, TEXT("UItemsBoxConfigDataAsset::ReplaceItemsBoxDataTable ItemsBoxDataTableTotal[%s] ItemsBoxDataTable[%s]"), *ItemsBoxDataTableTotal, *ItemsBoxDataTable);

		FRegexPattern PatternKeyValue(TEXT("(\\w*?)=(\\w*)"));
		FRegexMatcher MatcherKeyValue(PatternKeyValue, ItemsBoxDataTable);
		while (MatcherKeyValue.FindNext())
		{
			FString KeyValueTotal = MatcherKeyValue.GetCaptureGroup(0);
			FString Key = MatcherKeyValue.GetCaptureGroup(1);
			FString Value = MatcherKeyValue.GetCaptureGroup(2);
			UE_LOG(LogPickUp, Warning, TEXT("UItemsBoxConfigDataAsset::ReplaceItemsBoxDataTable ItemsBoxDataTable KeyValueTotal[%s] Key[%s] Value[%s] "), *KeyValueTotal, *Key, *Value);
			//UE_LOG(LogPickUp, Log, TEXT("UItemsBoxConfigDataAsset::ReplaceRegex [ItemsBoxData]"));
			//UE_LOG(LogPickUp, Log, TEXT("UItemsBoxConfigDataAsset::ReplaceRegex [ItemsBoxData] ItemTag[%s] Weight[%d]"), *ItemTag, Weight);
			ItemsBoxDataOverrideMap.Add(Key, FCString::Atoi(*Value));
		}
	}
}

void UItemsBoxConfigDataAsset::ReplaceGroupConfigs(TArray<FItemsBoxConfigDataAssetConfigGroup>& GroupConfigs, FString GroupName, FString ConfigItems, FString GroupConfigName)
{
	for (int32 GroupIndex = 0; GroupIndex < GroupConfigs.Num(); GroupIndex++)
	{
		FItemsBoxConfigDataAssetConfigGroup& TargetGroup = GroupConfigs[GroupIndex];
		if (TargetGroup.GroupName == GroupName)
		{
			UE_LOG(LogPickUp, Warning, TEXT("UItemsBoxConfigDataAsset::ReplaceGroupConfigs GroupConfigName[%s] GroupName[%s]"), *GroupConfigName, *GroupName);

			FRegexPattern PatternKeyValue(TEXT("(\\w*?)=(\\w*)"));
			FRegexMatcher MatcherKeyValue(PatternKeyValue, ConfigItems);
			while (MatcherKeyValue.FindNext())
			{
				FString KeyValueTotal = MatcherKeyValue.GetCaptureGroup(0);
				FString Key = MatcherKeyValue.GetCaptureGroup(1);
				FString Value = MatcherKeyValue.GetCaptureGroup(2);
				UE_LOG(LogPickUp, Warning, TEXT("UItemsBoxConfigDataAsset::ReplaceGroupConfigs GroupConfigName[%s] GroupName[%s] KeyValueTotal[%s] Key[%s] Value[%s] "), *GroupConfigName, *GroupName, *KeyValueTotal, *Key, *Value);
				for (int32 ConfigItemsIndex = 0; ConfigItemsIndex < TargetGroup.ConfigItems.Num(); ConfigItemsIndex++)
				{
					FItemsBoxConfigDataAssetConfigItem& ConfigItem = TargetGroup.ConfigItems[ConfigItemsIndex];
					if (ConfigItem.CategoryFilter == Key)
					{
						ConfigItem.ProbabilityPercent = FCString::Atof(*Value);
						//UE_LOG(LogPickUp, Log, TEXT("UItemsBoxConfigDataAsset::ReplaceRegex [%s] GroupName[%s] Match CategoryFilter[%s], CategoryFilter[%d]"), *GroupConfigsKey, *GroupName, *CategoryFilter, ProbabilityPercent);
						UE_LOG(LogPickUp, Warning, TEXT("UItemsBoxConfigDataAsset::ReplaceGroupConfigs Match GroupConfigName[%s] GroupName[%s] KeyValueTotal[%s] Key[%s] Value[%s] "), *GroupConfigName, *GroupName, *KeyValueTotal, *Key, *Value);
					}
				}
			}
		}
	}
}

void UItemsBoxConfigDataAsset::OnDelayInitTimer()
{
	UE_LOG(LogPickUp, Warning, TEXT("UItemsBoxConfigDataAsset::OnDelayInitTimer"));

	UWorld* MyWorld = GetWorld();
	if (MyWorld != nullptr)
	{
		UGameInstance* MyGameInstance = Cast<UGameInstance>(MyWorld->GetGameInstance());
		//MyGameInstance->IDIPItemsBoxConfigs = TEXT("{ \"AirDropBoxGroupConfigs\": [{\"GroupName\":\"AirDrop\", \"ConfigItems\" : [{\"CategoryFilter\":\"Weapon_Famas\", \"ProbabilityPercent\" : 99}, {\"CategoryFilter\":\"Attachment_Ballistics\",\"ProbabilityPercent\" : 69}] }, {\"GroupName\":\"18AirDrop\",\"ConfigItems\" : [{\"CategoryFilter\":\"Weapon_AMR\",\"ProbabilityPercent\" : 98},{\"CategoryFilter\":\"Armor\",\"ProbabilityPercent\" : 66}] }] , \"SuperAirDropBoxGroupConfigs\" : [{\"GroupName\":\"SuperAirDrop\", \"ConfigItems\" : [{\"CategoryFilter\":\"Weapon1_Famas\", \"ProbabilityPercent\" : 88}, {\"CategoryFilter\":\"Attachment_Ballistics\",\"ProbabilityPercent\" : 66}] }, {\"GroupName\":\"ChristmasAirDrop\",\"ConfigItems\" : [{\"CategoryFilter\":\"Weapon\",\"ProbabilityPercent\" : 12},{\"CategoryFilter\":\"HeadChristmas\",\"ProbabilityPercent\" : 33}] }],\"ItemsBoxDataTable\":[{\"ItemTag\":\"AMR\",\"Weight\":1},{\"ItemTag\":\"AMR2\",\"Weight\":10}] }");
		//MyGameInstance->IDIPItemsBoxConfigs = TEXT("%7B%22AirDropBoxGroupConfigs%22%3A%5B%7B%22GroupName%22%3A%22AirDrop%22%2C%22ConfigItems%22%3A%5B%7B%22CategoryFilter%22%3A%22Weapon_Famas%22%2C%22ProbabilityPercent%22%3A99%7D%2C%7B%22CategoryFilter%22%3A%22Attachment_Ballistics%22%2C%22ProbabilityPercent%22%3A69%7D%2C%7B%22CategoryFilter%22%3A%22Weapon_Famas%22%2C%22ProbabilityPercent%22%3A99%7D%5D%7D%2C%7B%22GroupName%22%3A%2218AirDrop%22%2C%22ConfigItems%22%3A%5B%7B%22CategoryFilter%22%3A%22Weapon_AMR%22%2C%22ProbabilityPercent%22%3A98%7D%2C%7B%22CategoryFilter%22%3A%22Armor%22%2C%22ProbabilityPercent%22%3A66%7D%5D%7D%5D%2C%22SuperAirDropBoxGroupConfigs%22%3A%5B%7B%22GroupName%22%3A%22SuperAirDrop%22%2C%22ConfigItems%22%3A%5B%7B%22CategoryFilter%22%3A%22Weapon1_Famas%22%2C%22ProbabilityPercent%22%3A88%7D%2C%7B%22CategoryFilter%22%3A%22Attachment_Ballistics%22%2C%22ProbabilityPercent%22%3A66%7D%5D%7D%2C%7B%22GroupName%22%3A%22ChristmasAirDrop%22%2C%22ConfigItems%22%3A%5B%7B%22CategoryFilter%22%3A%22Weapon%22%2C%22ProbabilityPercent%22%3A12%7D%2C%7B%22CategoryFilter%22%3A%22HeadChristmas%22%2C%22ProbabilityPercent%22%3A33%7D%5D%7D%5D%2C%22ItemsBoxDataTable%22%3A%5B%7B%22ItemTag%22%3A%22AMR%22%2C%22Weight%22%3A1%7D%2C%7B%22ItemTag%22%3A%22AMR2%22%2C%22Weight%22%3A10%7D%5D%7D");
		//MyGameInstance->IDIPItemsBoxConfigs = TEXT("(AirDropBoxGroupConfigs=(GroupName=AirDrop,ConfigItems=(Weapon_Famas=99,Attachment_Ballistics=100)),AirDropBoxGroupConfigs=(GroupName=18AirDrop,ConfigItems=(Weapon_Famas=99,Attachment_Ballistics=100)),SuperAirDropBoxGroupConfigs=(GroupName=AirDrop,ConfigItems=(Weapon_Famas=99,Attachment_Ballistics=100)),SuperAirDropBoxGroupConfigs=(GroupName=18AirDrop,ConfigItems=(Weapon_Famas=99,Attachment_Ballistics=100)),ItemsBoxDataTable=(AMR=1,AMR2=10))");
		//(\w*)=\(GroupName=(\w*?),ConfigItems=\((.*?)\)\)
		//ItemsBoxDataTable=\((.*?)\)
		//(\w*?)=(\w*)
// 		if (MyGameInstance != nullptr)
// 		{
// // 			MyGameInstance->IDIPItemsBoxConfigs = FString("(AirDropBoxGroupConfigs=(GroupName=AirDrop,ConfigItems=(Weapon_Famas=0,Attachment_Ballistics=100),FPPConfigItems=(Weapon_Famas=10,Attachment_Ballistics=100)),AirDropBoxGroupConfigs=(GroupName=18AirDrop,ConfigItems=(Weapon_Famas=99,Attachment_Ballistics=100)),SuperAirDropBoxGroupConfigs=(GroupName=AirDrop,ConfigItems=(Weapon_Famas=99,Attachment_Ballistics=100)),SuperAirDropBoxGroupConfigs=(GroupName=18AirDrop,ConfigItems=(Weapon_Famas=99,Attachment_Ballistics=100)),ItemsBoxDataTable=(AMR=1000,Armor_Lv3=0))");
// 			MyGameInstance->IDIPItemsBoxConfigs = FString(TEXT("AirDropBoxGroupConfigs=AirDrop,ConfigItems=TPP,Weapon2_Famas=0,Attachment_Ballistics=100|AirDropBoxGroupConfigs=AirDrop,ConfigItems=FPP,Weapon1_CompoundBow=0,Attachment_Ballistics=100|AirDropBoxGroupConfigs=18AirDrop,ConfigItems=TPP,Weapon2_Famas=0,Attachment_Ballistics=100|SuperAirDropBoxGroupConfigs=AirDrop,ConfigItems=TPP,Weapon2_Famas=0,Attachment_Ballistics=100|SuperAirDropBoxGroupConfigs=18AirDrop,ConfigItems=TPP,Weapon2_Famas=0,Attachment_Ballistics=100|ItemsBoxDataTable=0,GROZA=1000,Armor_Lv3=0"));
//  		}
		if (MyGameInstance != nullptr && MyGameInstance->IDIPItemsBoxConfigs.Len() > 0)
		{

			OverrideIDIPItemBoxConfig(MyGameInstance->IDIPItemsBoxConfigs);

			MyWorld->GetTimerManager().ClearTimer(TimerHandle_Init);
		}
		else
		{
			UE_LOG(LogPickUp, Warning, TEXT("UItemsBoxConfigDataAsset::OnDelayInitTimer Len[0]"));
		}
	}
}

void UItemsBoxConfigDataAsset::OverrideIDIPItemBoxConfig(FString& IDIPItemsBoxConfigs)
{
	UE_LOG(LogPickUp, Warning, TEXT("UItemsBoxConfigDataAsset::Override IDIPItemsBoxConfigs[%s] "), *IDIPItemsBoxConfigs);

	IDIPItemsBoxConfigs.ReplaceInline(TEXT("\n"), TEXT(""));
	IDIPItemsBoxConfigs.ReplaceInline(TEXT(" "), TEXT(""));

	FString ItemsBoxDataTable = FString(TEXT("ItemsBoxDataTable"));
	FString ConfigItems = FString(TEXT("ConfigItems"));
	FString FPPMode = FString(TEXT("FPP"));
	ASTExtraGameStateBase* STGameState = Cast<ASTExtraGameStateBase>(GetWorld() ? GetWorld()->GetGameState() : nullptr);
	bool IsFPPGameMode = STGameState && STGameState->IsFPPGameMode;

	TArray<FString> GroupConfigs;
	IDIPItemsBoxConfigs.ParseIntoArray(GroupConfigs, TEXT("|"));
	for (int32 idx = 0; idx < GroupConfigs.Num(); idx++)
	{
		FString GroupConfigStr = GroupConfigs[idx];
		//AirDropBoxGroupConfigs=AirDrop,ConfigItems=TPP,Weapon_Famas=0,Attachment_Ballistics=100
		UE_LOG(LogPickUp, Warning, TEXT("UItemsBoxConfigDataAsset::Override 3.4 GroupConfigStr[%s] "), *GroupConfigStr);

		TMap<FString, FString> ParamMap;
		TArray<FString> Params;
		GroupConfigStr.ParseIntoArray(Params, TEXT(","));


		FString Key = TEXT("");
		FString Value = TEXT("");

		for (int32 i = 0; i < Params.Num(); i++)
		{
			FString ParamStr = Params[i];
			if (ParamStr.Split(TEXT("="), &Key, &Value, ESearchCase::IgnoreCase, ESearchDir::FromStart))
			{
				ParamMap.Add(Key, Value);
			}
		}

		FString ConfigType = Params.IsValidIndex(0) ? Params[0] : TEXT("");
		if (ConfigType.Split(TEXT("="), &Key, &Value, ESearchCase::IgnoreCase, ESearchDir::FromStart))
		{
			if (ItemsBoxDataTable.Equals(Key))
			{
				if (!(IsFPPGameMode ^ FPPMode.Equals(Value)))
				{
					//ItemsBoxDataTable
					for (auto& Pair : ParamMap)
					{
						UE_LOG(LogPickUp, Warning, TEXT("UItemsBoxConfigDataAsset::OverrideItemsBoxDataTable ItemsBoxDataTable Key[%s] Value[%s] "), *Pair.Key, *Pair.Value);
						ItemsBoxDataOverrideMap.Add(Pair.Key, FCString::Atoi(*Pair.Value));
					}
				}
			}
			else
			{
				//AirDropBoxGroupConfigs
				FString GroupConfigName = Key;
				FString GroupName = Value;
				FString FPPType = Params.IsValidIndex(1) ? Params[1] : TEXT("");
				if (FPPType.Split(TEXT("="), &Key, &Value, ESearchCase::IgnoreCase, ESearchDir::FromStart))
				{
					if (ConfigItems.Equals(Key))
					{
						if (!(IsFPPGameMode ^ FPPMode.Equals(Value)))
						{
							UE_LOG(LogPickUp, Warning, TEXT("UItemsBoxConfigDataAsset::OverrideGroupConfigItems IsFPPGameMode=[%d] ConfigItems[%s] Match"), (int32)IsFPPGameMode, *Value);
							TArray<FItemsBoxConfigDataAssetConfigGroup>* BoxGroupConfigs = &AirDropBoxGroupConfigs;
							if (GroupConfigName == TEXT("AirDropBoxGroupConfigs"))
							{
								BoxGroupConfigs = &AirDropBoxGroupConfigs;
							}
							else if (GroupConfigName == TEXT("SuperAirDropBoxGroupConfigs"))
							{
								BoxGroupConfigs = &SuperAirDropBoxGroupConfigs;
							}
							else if (GroupConfigName == TEXT("XmasAirDropBoxGroupConfigs"))
							{
								BoxGroupConfigs = &XmasAirDropBoxGroupConfigs;
							}
							else if (GroupConfigName == TEXT("TreasureBoxGroupConfigs"))
							{
								BoxGroupConfigs = &TreasureBoxGroupConfigs;
							}
							else if (GroupConfigName == TEXT("ChannelBuoyBoxGroupConfigs"))
							{
								BoxGroupConfigs = &ChannelBuoyBoxGroupConfigs;
							}
							else if (GroupConfigName == TEXT("StarGameDropBoxGroupConfigs"))
							{
								BoxGroupConfigs = &StarGameDropBoxGroupConfigs;
							}

							UE_LOG(LogPickUp, Warning, TEXT("UItemsBoxConfigDataAsset::OverrideGroupConfigItems GroupConfigName[%s] GroupName[%s]"), *GroupConfigName, *GroupName);
							for (auto& Pair : ParamMap)
							{
								OverrideGroupConfigItems(*BoxGroupConfigs, GroupName, GroupConfigName, Pair.Key, Pair.Value);
							}
						}
						else
						{
							UE_LOG(LogPickUp, Warning, TEXT("UItemsBoxConfigDataAsset::OverrideGroupConfigItems IsFPPGameMode=[%d] ConfigItems[%s] Skip"), (int32)IsFPPGameMode, *Value);
						}
					}
				}
			}
		}
	}
}

void UItemsBoxConfigDataAsset::OverrideGroupConfigItems(TArray<FItemsBoxConfigDataAssetConfigGroup>& GroupConfigs, FString& GroupName, FString& GroupConfigName, FString& Key, FString& Value)
{
	for (int32 GroupIndex = 0; GroupIndex < GroupConfigs.Num(); GroupIndex++)
	{
		FItemsBoxConfigDataAssetConfigGroup& TargetGroup = GroupConfigs[GroupIndex];
		if (TargetGroup.GroupName == GroupName)
		{

			UE_LOG(LogPickUp, Warning, TEXT("UItemsBoxConfigDataAsset::OverrideGroupConfigItems GroupConfigName[%s] GroupName[%s] Key[%s] Value[%s] "), *GroupConfigName, *GroupName, *Key, *Value);
			for (int32 ConfigItemsIndex = 0; ConfigItemsIndex < TargetGroup.ConfigItems.Num(); ConfigItemsIndex++)
			{
				FItemsBoxConfigDataAssetConfigItem& ConfigItem = TargetGroup.ConfigItems[ConfigItemsIndex];
				if (ConfigItem.CategoryFilter == Key)
				{
					ConfigItem.ProbabilityPercent = FCString::Atof(*Value);
					//UE_LOG(LogPickUp, Log, TEXT("UItemsBoxConfigDataAsset::ReplaceRegex [%s] GroupName[%s] Match CategoryFilter[%s], CategoryFilter[%d]"), *GroupConfigsKey, *GroupName, *CategoryFilter, ProbabilityPercent);
					UE_LOG(LogPickUp, Warning, TEXT("UItemsBoxConfigDataAsset::OverrideGroupConfigItems Match GroupConfigName[%s] GroupName[%s] Key[%s] Value[%s] "), *GroupConfigName, *GroupName, *Key, *Value);
				}
			}
		}
	}
}

TArray<uint8> UItemsBoxConfigDataAsset::ConvertStringToBytes(const FString& Data)
{
	TArray<uint8> Payload;

	FTCHARToUTF8 Converter(*Data);
	Payload.SetNum(Converter.Length());
	FMemory::Memcpy(Payload.GetData(), (uint8*)(ANSICHAR*)Converter.Get(), Payload.Num());

	return Payload;
}

/*
**获取空投箱生成数据
*/
TArray<FAirDropBoxGenerateWrapperItemData> UItemsBoxConfigDataAsset::GetAirDropBoxGenerateDataList(int32 AirDropIndex)
{
	check(AirDropIndex>=0);
	int32 GroupIndex = 0;
	if(AirDropBoxIndexToGroupIndexMap.Contains(AirDropIndex))
	{
		GroupIndex = AirDropBoxIndexToGroupIndexMap[AirDropIndex];
		UE_LOG(LogPickUp, Log, TEXT("UItemsBoxConfigDataAsset::GetAirDropBoxGenerateDataList Find Match GroupIndex[%d]"), GroupIndex);
	}
	check(GroupIndex>=0);
	UE_LOG(LogGameInfo, Warning, TEXT("UItemsBoxConfigDataAsset::GetAirDropBoxGenerateDataList AirDropIndex[%d]GroupIndex[%d] %s"), AirDropIndex, GroupIndex, *GetName());
	OnDelayInitTimer();
	TArray<FAirDropBoxGenerateWrapperItemData> FinalDataList;
	if (AirDropBoxGroupConfigs.Num() > GroupIndex)
	{
		FItemsBoxConfigDataAssetConfigGroup TargetGroup = AirDropBoxGroupConfigs[GroupIndex];
		
		for (const FItemsBoxConfigDataAssetConfigItem& Item : TargetGroup.ConfigItems)
		{
			float RandRate = FMath::RandRange(1, 100);
			UE_LOG(LogGameInfo, Warning, TEXT("UItemsBoxConfigDataAsset::GetAirDropBoxGenerateDataList Loop CategoryFilter=%s,RandRate=%f"), *Item.CategoryFilter, RandRate);
			if (RandRate <= Item.ProbabilityPercent)
			{
				FinalDataList.Append(GetTargetItemsDataFromTableByCategoryFilter(Item.CategoryFilter));
			}
		}
	}
	return FinalDataList;
}

/*
**IDIP JSON 替换参数
* {"AirDropBoxGroupConfigs":[{"GroupName":"AirDrop","ConfigItems":[{"CategoryFilter":"Weapon_M200","ProbabilityPercent":99},{"CategoryFilter":"Weapon_M201","ProbabilityPercent":69}]},{"GroupName":"AirDrop","ConfigItems":[{"CategoryFilter":"Weapon_M200","ProbabilityPercent":99},{"CategoryFilter":"Weapon_M201","ProbabilityPercent":69}]}],"SuperAirDropBoxGroupConfigs":[{"GroupName":"AirDrop","ConfigItems":[{"CategoryFilter":"Weapon_M200","ProbabilityPercent":99},{"CategoryFilter":"Weapon_M201","ProbabilityPercent":69}]},{"GroupName":"AirDrop","ConfigItems":[{"CategoryFilter":"Weapon_M200","ProbabilityPercent":99},{"CategoryFilter":"Weapon_M201","ProbabilityPercent":69}]}]}
* {"ItemsBoxDataTable":[{"ItemTag":"AMR","Weight":1},{"ItemTag":"AMR2","Weight":10}]}
*/
void UItemsBoxConfigDataAsset::ReplaceJSON(FString JSON)
{
	UE_LOG(LogPickUp, Log, TEXT("UItemsBoxConfigDataAsset::ReplaceJSON JSON[%s]"), *JSON);
	
	if (!JSON.IsEmpty())
	{
		TSharedPtr<FJsonObject> JsonObject;
		TSharedRef< TJsonReader<TCHAR> > Reader = TJsonReaderFactory<TCHAR>::Create(JSON);
		if (FJsonSerializer::Deserialize(Reader, JsonObject))
		{
			ReplaceGroupConfigs(JsonObject, TEXT("AirDropBoxGroupConfigs"), AirDropBoxGroupConfigs);
			ReplaceGroupConfigs(JsonObject, TEXT("SuperAirDropBoxGroupConfigs"), SuperAirDropBoxGroupConfigs);
			ReplaceGroupConfigs(JsonObject, TEXT("XmasAirDropBoxGroupConfigs"), XmasAirDropBoxGroupConfigs);
			ReplaceGroupConfigs(JsonObject, TEXT("TreasureBoxGroupConfigs"), TreasureBoxGroupConfigs);
			ReplaceGroupConfigs(JsonObject, TEXT("ChannelBuoyBoxGroupConfigs"), ChannelBuoyBoxGroupConfigs);
			ReplaceGroupConfigs(JsonObject, TEXT("StarGameDropBoxGroupConfigs"), StarGameDropBoxGroupConfigs);

			ReadItemBoxDataTable(JsonObject, TEXT("ItemsBoxDataTable"));
		}
	}
}

void UItemsBoxConfigDataAsset::ReadItemBoxDataTable(TSharedPtr<FJsonObject>& JsonObject, FString ItemsBoxDataTableKey)
{
// 	UE_LOG(LogPickUp, Log, TEXT("UItemsBoxConfigDataAsset::ReplaceJSON [ItemsBoxData]"));
// 	const TArray<TSharedPtr<FJsonValue>>* JSONItemsBoxDataTable;
// 	if (JsonObject->TryGetArrayField(ItemsBoxDataTableKey, JSONItemsBoxDataTable))
// 	{
// 		for (int32 idx = 0; idx < JSONItemsBoxDataTable->Num(); idx++)
// 		{
// 			TSharedPtr<FJsonValue> JSONItemsBox = (*JSONItemsBoxDataTable)[idx];
// 			TSharedPtr<FJsonObject> JSONItemsBoxObj = JSONItemsBox->AsObject();
// 
// 			int32 Weight = 0;
// 			FString ItemTag;
// 			if (JSONItemsBoxObj->TryGetStringField(TEXT("ItemTag"), ItemTag) &&
// 				JSONItemsBoxObj->TryGetNumberField(TEXT("Weight"), Weight))
// 			{
// 				UE_LOG(LogPickUp, Log, TEXT("UItemsBoxConfigDataAsset::ReplaceJSON [ItemsBoxData] ItemTag[%s] Weight[%d]"), *ItemTag, Weight);
// 				ItemsBoxDataOverrideMap.Add(ItemTag, Weight);
// 			}
// 		}
// 	}
}

void UItemsBoxConfigDataAsset::ReplaceGroupConfigs(TSharedPtr<FJsonObject>& JsonObject, FString GroupConfigsKey, TArray<FItemsBoxConfigDataAssetConfigGroup>& GroupConfigs)
{
// 	const TArray<TSharedPtr<FJsonValue>>* JSONGroupConfigs;
// 	if (JsonObject->TryGetArrayField(GroupConfigsKey, JSONGroupConfigs))
// 	{
// 		for (int32 idx = 0; idx < JSONGroupConfigs->Num(); idx++)
// 		{
// 			TSharedPtr<FJsonValue> JSONTargetGroup = (*JSONGroupConfigs)[idx];
// 			TSharedPtr<FJsonObject> JSONTargetGroupObj = JSONTargetGroup->AsObject();
// 			FString GroupName = JSONTargetGroupObj->GetStringField("GroupName");
// 			for (int32 GroupIndex = 0; GroupIndex < GroupConfigs.Num(); GroupIndex++)
// 			{
// 				FItemsBoxConfigDataAssetConfigGroup& TargetGroup = GroupConfigs[GroupIndex];
// 				if (TargetGroup.GroupName == GroupName)
// 				{
// 					UE_LOG(LogPickUp, Log, TEXT("UItemsBoxConfigDataAsset::ReplaceJSON [%s] GroupName[%s]"), *GroupConfigsKey, *GroupName);
// 
// 					const TArray<TSharedPtr<FJsonValue>>* JSONConfigItems;
// 
// 					if (JSONTargetGroupObj->TryGetArrayField(TEXT("ConfigItems"), JSONConfigItems))
// 					{
// 						for (int32 ConfigItemsIdx = 0; ConfigItemsIdx < JSONConfigItems->Num(); ConfigItemsIdx++)
// 						{
// 							TSharedPtr<FJsonValue> JSONConfigItem = (*JSONConfigItems)[ConfigItemsIdx];
// 							TSharedPtr<FJsonObject> JSONConfigItemObj = JSONConfigItem->AsObject();
// 
// 							int32 ProbabilityPercent = 0;
// 							FString CategoryFilter;
// 							if (JSONConfigItemObj->TryGetStringField(TEXT("CategoryFilter"), CategoryFilter) &&
// 								JSONConfigItemObj->TryGetNumberField(TEXT("ProbabilityPercent"), ProbabilityPercent))
// 							{
// 								for (int32 ConfigItemsIndex = 0; ConfigItemsIndex < TargetGroup.ConfigItems.Num(); ConfigItemsIndex++)
// 								{
// 									FItemsBoxConfigDataAssetConfigItem& ConfigItem = TargetGroup.ConfigItems[ConfigItemsIndex];
// 									if (ConfigItem.CategoryFilter == CategoryFilter)
// 									{
// 										ConfigItem.ProbabilityPercent = (float)ProbabilityPercent;
// 										UE_LOG(LogPickUp, Log, TEXT("UItemsBoxConfigDataAsset::ReplaceJSON [%s] GroupName[%s] Match CategoryFilter[%s], CategoryFilter[%d]"), *GroupConfigsKey, *GroupName, *CategoryFilter, ProbabilityPercent);
// 									}
// 								}
// 							}
// 						}
// 					}
// 				}
// 			}
// 		}
// 	}
}

TArray<FAirDropBoxGenerateWrapperItemData> UItemsBoxConfigDataAsset::GetAirDropBoxGenerateDataListByGroupIndex(int32 IndexInCfg /*= 0*/)
{
	check(IndexInCfg >= 0);
	UE_LOG(LogPickUp, Log, TEXT("UItemsBoxConfigDataAsset::GetAirDropBoxGenerateDataList GroupIndex[%d]"), IndexInCfg);
	OnDelayInitTimer();
	TArray<FAirDropBoxGenerateWrapperItemData> FinalDataList;
	if (AirDropBoxGroupConfigs.IsValidIndex(IndexInCfg))
	{
		FItemsBoxConfigDataAssetConfigGroup TargetGroup = AirDropBoxGroupConfigs[IndexInCfg];
		for (const FItemsBoxConfigDataAssetConfigItem& Item : TargetGroup.ConfigItems)
		{
			float RandRate = FMath::RandRange(1, 100);
			if (RandRate <= Item.ProbabilityPercent)
			{
				FinalDataList.Append(GetTargetItemsDataFromTableByCategoryFilter(Item.CategoryFilter));
			}
		}
	}
	return FinalDataList;
}

/*
**获取超级空投箱生成数据
*/
TArray<FAirDropBoxGenerateWrapperItemData> UItemsBoxConfigDataAsset::GetSuperAirDropBoxGenerateDataList(int32 GroupIdx)
{
	UE_LOG(LogPickUp, Log, TEXT("UItemsBoxConfigDataAsset::GetSuperAirDropBoxGenerateDataList GroupIndex[%d]"), GroupIdx);
	OnDelayInitTimer();
	TArray<FAirDropBoxGenerateWrapperItemData> FinalDataList;
	if (SuperAirDropBoxGroupConfigs.Num() > GroupIdx)
	{
		FItemsBoxConfigDataAssetConfigGroup TargetGroup = SuperAirDropBoxGroupConfigs[GroupIdx];
		for (const FItemsBoxConfigDataAssetConfigItem& Item : TargetGroup.ConfigItems)
		{
			float RandRate = FMath::RandRange(1, 100);
			if (RandRate <= Item.ProbabilityPercent)
			{
				FinalDataList.Append(GetTargetItemsDataFromTableByCategoryFilter(Item.CategoryFilter));
			}
		}
	}
	return FinalDataList;
}

/*
**获取圣诞空投箱生成数据
*/
TArray<FAirDropBoxGenerateWrapperItemData> UItemsBoxConfigDataAsset::GetXmasAirDropBoxGenerateDataList(FString GroupName)
{
	UE_LOG(LogPickUp, Log, TEXT("UItemsBoxConfigDataAsset::GetXmasAirDropBoxGenerateDataList GroupName[%s]"), *GroupName);
	OnDelayInitTimer();
	TArray<FAirDropBoxGenerateWrapperItemData> FinalDataList;
	for (const FItemsBoxConfigDataAssetConfigGroup& GroupItem : XmasAirDropBoxGroupConfigs)
	{
		if (GroupItem.GroupName.Equals(GroupName))
		{
			for (const FItemsBoxConfigDataAssetConfigItem& Item : GroupItem.ConfigItems)
			{
				float RandRate = FMath::RandRange(1, 100);
				if (RandRate <= Item.ProbabilityPercent)
				{
					FinalDataList.Append(GetTargetItemsDataFromTableByCategoryFilter(Item.CategoryFilter));
				}
			}
		}
	}
	return FinalDataList;
}

/*
**获取地图宝箱生成数据
*/
TArray<FAirDropBoxGenerateWrapperItemData> UItemsBoxConfigDataAsset::GetTreasureBoxGenerateDataList(FString GroupName)
{
	UE_LOG(LogPickUp, Log, TEXT("UItemsBoxConfigDataAsset::GetTreasureBoxGenerateDataList GroupName[%s]"), *GroupName);
	OnDelayInitTimer();
	TArray<FAirDropBoxGenerateWrapperItemData> FinalDataList;
	for (const FItemsBoxConfigDataAssetConfigGroup& GroupItem : TreasureBoxGroupConfigs)
	{
		if (GroupItem.GroupName.Equals(GroupName))
		{
			for (const FItemsBoxConfigDataAssetConfigItem& Item : GroupItem.ConfigItems)
			{
				float RandRate = FMath::RandRange(1, 100);
				if (RandRate <= Item.ProbabilityPercent)
				{
					FinalDataList.Append(GetTargetItemsDataFromTableByCategoryFilter(Item.CategoryFilter));
				}
			}
		}
	}
	return FinalDataList;
}

/*
**获取航道浮标宝箱生成数据
*/
TArray<FAirDropBoxGenerateWrapperItemData> UItemsBoxConfigDataAsset::GetChannelBuoyBoxGenerateDataList(FString const& GroupName)
{
	UE_LOG(LogPickUp, Log, TEXT("UItemsBoxConfigDataAsset::GetChannelBuoyBoxGenerateDataList GroupName[%s]"), *GroupName);
	OnDelayInitTimer();
    TArray<FAirDropBoxGenerateWrapperItemData> FinalDataList;
    if (ChannelBuoyBoxGroupConfigs.Num() > 0)
    {
        for (auto&& TargetGroup : ChannelBuoyBoxGroupConfigs)
        {
            if (TargetGroup.GroupName == GroupName)
            {
                for (const FItemsBoxConfigDataAssetConfigItem& Item : TargetGroup.ConfigItems)
                {
                    float RandRate = FMath::RandRange(1, 100);
                    if (RandRate <= Item.ProbabilityPercent)
                    {
                        FinalDataList.Append(GetTargetItemsDataFromTableByCategoryFilter(Item.CategoryFilter));
                    }
                }
            }
        }
    }
    return FinalDataList;
}

/*
**获取表演赛空投宝箱生成数据
*/
TArray<FAirDropBoxGenerateWrapperItemData> UItemsBoxConfigDataAsset::GetStarGameDropBoxGenerateDataList(FString const& GroupName)
{
	UE_LOG(LogPickUp, Log, TEXT("UItemsBoxConfigDataAsset::GetStarGameDropBoxGenerateDataList GroupName[%s]"), *GroupName);
	OnDelayInitTimer();
	TArray<FAirDropBoxGenerateWrapperItemData> FinalDataList;
	if (StarGameDropBoxGroupConfigs.Num() > 0)
	{
		for (auto&& TargetGroup : StarGameDropBoxGroupConfigs)
		{
			if (TargetGroup.GroupName == GroupName)
			{
				for (const FItemsBoxConfigDataAssetConfigItem& Item : TargetGroup.ConfigItems)
				{
					float RandRate = FMath::RandRange(1, 100);
					if (RandRate <= Item.ProbabilityPercent)
					{
						FinalDataList.Append(GetTargetItemsDataFromTableByCategoryFilter(Item.CategoryFilter));
					}
				}
			}
		}
	}
	return FinalDataList;
}

/*
**从表里读取类型
*/
TArray<FAirDropBoxGenerateWrapperItemData> UItemsBoxConfigDataAsset::GetTargetItemsDataFromTableByCategoryFilter(FString CategoryFilter)
{
	//replace json
	UE_LOG(LogGameInfo, Warning, TEXT("UItemsBoxConfigDataAsset::GetTargetItemsDataFromTableByCategoryFilter CategoryFilter=%s"), *CategoryFilter);
	TArray<FAirDropBoxGenerateWrapperItemData> FinalData;
	if (DataBase)
	{
		TArray<FItemsBoxDataTableRow*> AllRows;
		TArray<const FItemsBoxDataTableRow*> TargetFilterRows;
		DataBase->GetAllRows<FItemsBoxDataTableRow>(TEXT(""), AllRows);
		int32 AllWeight = 0;
		for (const FItemsBoxDataTableRow* Item : AllRows)
		{
			if (Item && Item->Category.Equals(CategoryFilter))
			{
				int32 Weight = ItemsBoxDataOverrideMap.Contains(Item->ItemTag) ? ItemsBoxDataOverrideMap.FindRef(Item->ItemTag) : Item->Weight;
				if (Weight > 0)
				{
					TargetFilterRows.Add(Item);
					AllWeight += Weight;
					UE_LOG(LogGameInfo, Warning, TEXT("UItemsBoxConfigDataAsset::GetAirDropBoxGenerateDataList CategoryFilter[%s]ItemTag[%s]Weight[%d]"), *CategoryFilter, *Item->ItemTag, Weight);
				}
			}
		}
		int32 CurAccWeight = 0;
		int32 RandWeight = FMath::RandRange(0, AllWeight);
		for (int i = 0; i < TargetFilterRows.Num(); ++i)
		{
			const FItemsBoxDataTableRow* Row = TargetFilterRows[i];
			if (Row)
			{
				int32 Weight = ItemsBoxDataOverrideMap.Contains(Row->ItemTag) ? ItemsBoxDataOverrideMap.FindRef(Row->ItemTag) : Row->Weight;
				CurAccWeight += Weight;
				if (CurAccWeight >= RandWeight)
				{
					for (TMap<TSoftClassPtr<APickUpWrapperActor>, int32>::TConstIterator it = Row->ItemsData.CreateConstIterator(); it; ++it)
					{
						FAirDropBoxGenerateWrapperItemData AddItem;
						AddItem.WrapperClass = it->Key.LoadSynchronous();
						AddItem.Count = it->Value;
						FinalData.Add(AddItem);
						UE_LOG(LogGameInfo, Warning, TEXT("UItemsBoxConfigDataAsset::GetAirDropBoxGenerateDataList CategoryFilter[%s]AddItem[%s]"),*CategoryFilter, *AddItem.ToString());
					}
					break;
				}
			}
		}
	}
	return FinalData;
}