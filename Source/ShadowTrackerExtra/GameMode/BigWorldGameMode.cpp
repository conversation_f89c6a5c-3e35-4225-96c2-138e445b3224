// Fill out your copyright notice in the Description page of Project Settings.

#include "ShadowTrackerExtra.h"
#include "Pxg/PxgCrowdManagerComponent.h"
#include "BigWorldGameMode.h"
#include "UAEAdvertisementActor.h"
#include "DataSource/GMDataManager.h"
#include "STExtraGameplayStatics.h"
#include "Assist/GMData/GMDataSource_PlayerStart.h"
#include "Game/STExtraPlayerStart.h"
#include "Utility/STExtraGMDelegatesMgr.h"
#include "Player/STExtraPlayerController.h"

DEFINE_LOG_CATEGORY(LogBigWorldGameMode)

extern ENGINE_API int32 GCVarEnableCG25RelicationOpt;
extern BASIC_API int32 GGameModeID;

extern TAutoConsoleVariable<int32> CVarEnableRecSyncOBDataNormalDS;


ABigWorldGameMode::ABigWorldGameMode(const FObjectInitializer& ObjectInitializer) :
	Super(ObjectInitializer.DoNotCreateDefaultSubobject(TEXT("AIActingComponent"))
		.DoNotCreateDefaultSubobject(TEXT("PxgCrowdManagerComponent"))
		.DoNotCreateDefaultSubobject(TEXT("GameModeProbeComponent")))
{
	bForceClearPendingExitPlayerWhenReenter = true;
	ClearPendingExitDSPlayerTimeout = 30.f;
}

void ABigWorldGameMode::BeginPlay()
{
	Super::BeginPlay();
	CVarEnableRecSyncOBDataNormalDS->Set(0);

	if (GetWorld())
	{
		// 大世界启动时加载所有关卡
		GetWorld()->SetWorldAlwaysVisible(EWorldLevelLoadType::WorldLevelLoadType_All);
	}

#if UE_SERVER
	// bonnywu, receive the fake data for UGCPIE under bigworld mode
	if(GIsUGCPIEServer &&  BWS::IsEnabled(BWS::GBigWorldOverall))
	{
		UE_LOG(LogTemp, Log, TEXT("bonnywu, ABigWorldGameMode::BeginPlay"));
		if (GameModeDelegates::RetrieveGameModeInitGameParams.IsBound())
		{
			UE_LOG(LogTemp, Log, TEXT("bonnywu, ABigWorldGameMode::BeginPlay RetrieveGameModeInitGameParams"));
			FGameModeInitGameParams param;
			param = GameModeDelegates::RetrieveGameModeInitGameParams.Execute();
			if (STGameState)
			{
				STGameState->GameModeID = param.GameModeID;
				GGameModeID = FCString::Atoi(*param.GameModeID);
				STGameState->ModeUIManagerArray = param.GameModeUIManagerArray;
			}
			else
			{
				UE_LOG(LogTemp, Warning, TEXT("bonnywu, ABigWorldGameMode::BeginPlay RetrieveGameModeInitGameParams STGameState is nullptr! might not be able to show some big world resources"));
			}
		}
	}
#endif
}

void ABigWorldGameMode::InitGameState()
{
	Super::InitGameState();

	DynamicFeatureConfigs = BigWorldDynamicFeatureConfigs;

	CreateDynamicGameModeComponent(ESubSystemType::ESS_AIProbe);
	CreateDynamicGameModeComponent(ESubSystemType::ESS_AIActing);
}

void ABigWorldGameMode::Tick(float DeltaSeconds)
{
	Super::Tick(DeltaSeconds);
}

void ABigWorldGameMode::PostLogin(APlayerController* NewPlayer)
{
	Super::PostLogin(NewPlayer);
	ReceivePostLogin(NewPlayer);
}

APawn* ABigWorldGameMode::SpawnDefaultPawnAtTransformInBigWorld(AController* NewPlayer, FTransform Trans)
{
	if (!GetWorld())
	{
		return nullptr;
	}

	FActorSpawnParameters SpawnInfo;
	SpawnInfo.Owner = NewPlayer;
	SpawnInfo.Instigator = Instigator;
	SpawnInfo.ObjectFlags |= RF_Transient;	// We never want to save default player pawns into a map
	SpawnInfo.ActorFeatureSetConfig = GetFeatureSetConfigForDefaultPawn();
	SpawnInfo.SpawnCollisionHandlingOverride = ESpawnActorCollisionHandlingMethod::AlwaysSpawn;
	UClass* PawnClass = GetDefaultPawnClassForController(NewPlayer);
	APawn* ResultPawn = GetWorld()->SpawnActor<APawn>(PawnClass, Trans, SpawnInfo);
	if (!ResultPawn)
	{
		UE_LOG(LogBigWorldGameMode, Warning, TEXT("SpawnDefaultPawnAtTransformInBigWorld Couldn't spawn Pawn of type %s at %s"), *GetNameSafe(PawnClass), *Trans.ToHumanReadableString());
	}
	else
	{
		UE_LOG(LogBigWorldGameMode, Log, TEXT("SpawnDefaultPawnAtTransformInBigWorld ResultPawn=%s ResultPawn->GetActorLocation()=%s Trans=%s"), *(ResultPawn->GetName()), *((ResultPawn->GetActorLocation()).ToString()), *Trans.GetLocation().ToString());
		if (ResultPawn->GetTargetLocation() != Trans.GetLocation())
		{
			UE_LOG(LogBigWorldGameMode, Warning, TEXT("SpawnDefaultPawnAtTransformInBigWorld calibrate pawn location to %s"), *Trans.GetLocation().ToString());
			ResultPawn->SetActorLocation(Trans.GetLocation());
		}
	}

	return ResultPawn;
}

void ABigWorldGameMode::CheckAliveCharacters(float DeltaTime)
{
	static float CheckDeltaTime = 0;
	CheckDeltaTime += DeltaTime;
	
	if (CheckDeltaTime > BigWorldCheckAliveCharactersPeriod)
	{
		CheckDeltaTime = 0;

		EAutoSwitchDSState AutoSwitchState = UGMComponentManager::GetInstance()->InvokeFunctionWithReturn<EAutoSwitchDSState>(ESubSystemType::ESS_AutoSwitchDS, TEXT("GetState"));

		const bool bNeedClose =
			AutoSwitchState == EAutoSwitchDSState::Switching ||
			AutoSwitchState == EAutoSwitchDSState::ForceSwitching ||
			AutoSwitchState == EAutoSwitchDSState::Closing;
		
		if (bNeedClose)
		{
			TArray<AActor*> AllAliveCharacters;
			UGameplayStatics::GetAllActorsOfClass(GetWorld(), ASTExtraBaseCharacter::StaticClass(), AllAliveCharacters);
			if (AllAliveCharacters.Num() == 0)
			{
				// We don't send the "game_over" message the first time no characters were found
				// In case of some players were "half-login", and they had no characters
				// Between the first time and the second time, match was forbidden
				// Therefore we can ensure "half-login" players no longer existed the second time
				static bool bFirstTimeNoCharacters = true;
				if (bFirstTimeNoCharacters)
				{
					UE_LOG(LogGameModeBase, Log, TEXT("---CheckAliveCharacters: FirstTimeNoCharacters"));
					bFirstTimeNoCharacters = false;
				}
				else
				{
					if (SendGameOver.IsBound())
					{
						UE_LOG(LogGameModeBase, Log, TEXT("---CheckAliveCharacters: SendGameOver"));
						SendGameOver.Execute();
					}
				}
			}
			else if (AutoSwitchState == EAutoSwitchDSState::ForceSwitching || AutoSwitchState == EAutoSwitchDSState::Closing)
			{
				CheckAliveCharactersRetryCount++;
				if (CheckAliveCharactersRetryCount == CheckAliveCharactersRetryCountExceptionThreshold)
				{
					if (BWS::IsEnabled(BWS::Misc::GEnableSwitchDSExceptionReport))
					{
						FApp::ExceptionReport(TEXT("SwitchDSException"), TEXT("long_time_no_game_over"),
							FString::Printf(TEXT("state=%d retry=%u"), (int32)AutoSwitchState, CheckAliveCharactersRetryCount));
					}

					// Open debug log
					GDebugAutoSwitchDS = true;
					
					// Dump essential information of alive players
					for (AActor* Actor : AllAliveCharacters)
					{
						if (ASTExtraBaseCharacter* CH = Cast<ASTExtraBaseCharacter>(Actor))
						{
							USTExtraGameplayStatics::DumpObjectToLog(CH);
							if (ASTExtraPlayerController* PC = CH->GetPlayerControllerSafety())
							{
								USTExtraGameplayStatics::DumpObjectToLog(PC);
							}
						}
					}
				}
			}
		}
	}
}

void ABigWorldGameMode::OnPlayerRealExit(APlayerController* Exiting)
{
	QUICK_SCOPE_CYCLE_COUNTER(STAT_BigWorldGameMode_OnPlayerRealExit)

	if (Exiting)
	{
		if (ASTExtraPlayerStart** PlayerStartPtr = PlayerToStartMap.Find(TWeakObjectPtr<APlayerController>(Exiting)))
		{
			if (ASTExtraPlayerStart* PlayerStart = *PlayerStartPtr)
			{
				UnoccupiedPlayerStarts.Add(PlayerStart);
			}
		}
	}
	
	Super::OnPlayerRealExit(Exiting);

	if (USTExtraGMDelegatesMgr::GetInstance())
	{
		QUICK_SCOPE_CYCLE_COUNTER(STAT_BigWorldGameMode_OnPlayerExitBigWorld)

		USTExtraGMDelegatesMgr::GetInstance()->OnPlayerExitBigWorld.Broadcast(Exiting);
	}
}

AActor* ABigWorldGameMode::FindPlayerStart_Implementation(AController* Player, const FString& IncomingName)
{
	AUAEPlayerController* PlayerController = Cast<AUAEPlayerController>(Player);
	ASTExtraPlayerState* PlayerState = Cast<ASTExtraPlayerState>(Player->PlayerState);

	UGMDataSource_PlayerStart* PlayerStartData = UGMDataManager::Get()->GetDataSource<UGMDataSource_PlayerStart>(EGMDataType::EGMDT_PlayerStart);

	if (!PlayerStartData)
	{
		return Super::FindPlayerStart_Implementation(Player, IncomingName);
	}

	TArray<ASTExtraPlayerStart*> STEPlayerStartsList = PlayerStartData->GetPlayerStarts();

	if (STEPlayerStartsList.Num() == 0)
	{
		return Super::FindPlayerStart_Implementation(Player, IncomingName);
	}

	if (bFindPlayerStartFirstTime)
	{
		UnoccupiedPlayerStarts = STEPlayerStartsList;
		Algo::Reverse(UnoccupiedPlayerStarts);
		bFindPlayerStartFirstTime = false;
	}

	if (PlayerController == nullptr || PlayerState == nullptr || !PlayerState->binitializedData)
	{
		UE_LOG(LogBigWorldGameMode, Log, TEXT("FindPlayerStart_Implementation null or uninitailized"));
		return STEPlayerStartsList[0];
	}

	if (ASTExtraPlayerStart** PlayerStartPtr = PlayerToStartMap.Find(TWeakObjectPtr<APlayerController>(PlayerController)))
	{
		if (PlayerStartPtr)
		{
			UE_LOG(LogBigWorldGameMode, Log, TEXT("FindPlayerStart_Implementation[%s] Occupied PlayerStart[%s]"),
				*PlayerState->PlayerName, *(*PlayerStartPtr)->GetName());
			return *PlayerStartPtr;
		}
	}

	ASTExtraPlayerStart* SelectedPlayerStart = nullptr;

	if (UnoccupiedPlayerStarts.Num() > 0)
	{
		SelectedPlayerStart = UnoccupiedPlayerStarts.Pop(false); // don't shrink

		if (SelectedPlayerStart)
		{
			UE_LOG(LogBigWorldGameMode, Log, TEXT("FindPlayerStart_Implementation[%s] Found unoccupied PlayerStart[%s]"),
            	*PlayerState->PlayerName, *SelectedPlayerStart->GetName());
            
            SelectedPlayerStart->SetMarkOccupied();
            PlayerToStartMap.Add(TWeakObjectPtr<APlayerController>(PlayerController), SelectedPlayerStart);
    
            if (SelectedPlayerStart->PlayerStartTag.ToString().Len() > 0 && SelectedPlayerStart->PlayerStartTag != NAME_None)
            {
            	PlayerState->PlayerStartID = FCString::Atoi(*SelectedPlayerStart->PlayerStartTag.ToString());
            	PlayerController->PlayerStartID = PlayerState->PlayerStartID;
				if (GCVarEnableCG25RelicationOpt)
				{
					DOREPONCE(AUAEPlayerController, PlayerStartID, PlayerController);
				}
            }

			return SelectedPlayerStart;
		}
	}
	else
	{
		SelectedPlayerStart = STEPlayerStartsList[FMath::RandRange(0, STEPlayerStartsList.Num() - 1)];

		if (SelectedPlayerStart)
		{
			UE_LOG(LogBigWorldGameMode, Warning, TEXT("FindPlayerStart_Implementation[%s] Stortage, choose PlayerStart[%s] randomly"),
				*PlayerState->PlayerName, *SelectedPlayerStart->GetName());
			
			return SelectedPlayerStart;
		}
	}

	return Super::FindPlayerStart_Implementation(Player, IncomingName);
}

void ABigWorldGameMode::PostInitializedByDSUtils()
{
	Super::PostInitializedByDSUtils();

	// 自适应阈值
	if (GetWorld())
	{
		if (UNetDriver* NetDriver = GetWorld()->GetNetDriver())
		{
			CheckAliveCharactersRetryCountExceptionThreshold += (int32)NetDriver->ConnectionTimeout / (int32)BigWorldCheckAliveCharactersPeriod;
			UE_LOG(LogBigWorldGameMode, Log, TEXT("CheckAliveCharactersRetryCountExceptionThreshold=%d"), CheckAliveCharactersRetryCountExceptionThreshold);
		}
	}
}

void ABigWorldGameMode::InitAdvertisement()
{
#if WITH_EDITOR
	if (AdvConfigList.Num() < 1)
	{
#if PLATFORM_WINDOWS && !WIN_RELEASE
		FString obtestfile = FPaths::ProjectDir() + "adtest.txt";
		FILE* file = nullptr;
		if (fopen_s(&file, TCHAR_TO_UTF8(*obtestfile), "r") == 0 && file != nullptr)
		{
			int32 Id;
			float x, y, z;
			float yaw = 0.0f, scale = 1.0f;
			char meshpath[512];
			char picpath[512];
			while (fscanf_s(file, "%d %f %f %f %f %f %s %s", &Id, &x, &y, &z, &yaw, &scale,
				meshpath, 512, picpath, 512) == 8)
			{
				FAdvertisementActorConfig config = FAdvertisementActorConfig();

				config.Id = Id;
				config.ResPath = UTF8_TO_TCHAR(meshpath);
				config.HttpImgPath = UTF8_TO_TCHAR(picpath);
				config.Loc = FVector(x, y, z);
				config.Rot = FRotator(0.0f, yaw, 0.0f);
				config.Scale = FVector(scale, scale, scale);
				static int tmp_i = 0;
				if (tmp_i & 1)
				{
					config.HideWhenFighting = true;
				}
				tmp_i++;
				AdvConfigList.Add(config);
			}

			fclose(file);
		}
#endif
	}
	else
	{
		return;
	}
#endif

	TArray<AActor*> AdvActors;
	UGameplayStatics::GetAllActorsOfClass(GetWorld(), AUAEAdvertisementActor::StaticClass(), AdvActors);
	for (AActor* tmpAdvActor : AdvActors)
	{
		AUAEAdvertisementActor* AdvActor = Cast<AUAEAdvertisementActor>(tmpAdvActor);
		if (AdvActor)
		{
			AdvActorList.Add(AdvActor->Id, AdvActor);
		}
	}
	for (const FAdvertisementActorConfig& config : AdvConfigList)
	{
		FString AdvPath = config.ResPath;
		FString HttpImgPath = config.HttpImgPath;
		FVector SpawnScale = config.Scale;

		if (AdvActorList.Find(config.Id))
		{
			AUAEAdvertisementActor* AdvActor = AdvActorList[config.Id];
			UStaticMesh* mesh = LoadObject<UStaticMesh>(nullptr, *AdvPath);

			AdvActor->SetActorHiddenInGame(false);
			//AdvActor->SetReplicates(true);
			AdvActor->SetScale(SpawnScale);

			AdvActor->SetStaticMeshPath(AdvPath);
			AdvActor->SetStaticMesh(mesh);
			AdvActor->SetHttpImage(HttpImgPath);

			AdvActor->HideWhenFighting = config.HideWhenFighting;
		}else
		{
			UE_LOG(LogBigWorldGameMode, Log, TEXT("---UAEGM:InitAdvertisement Can not find ID = [%d]"), config.Id);
			AdvTobeInit.Add(config.Id, config);
		}
	}
}

void ABigWorldGameMode::ReceivedIDIPStates(const TMap<int32, FIDIPStateInfo>& IDIPStateInfos)
{
	UBWDynamicParaIDIPComponent* IdipComponent = FindComponentByClass<UBWDynamicParaIDIPComponent>();
	if(IdipComponent)
	{
		IdipComponent->ReceivedIDIPStates(IDIPStateInfos);
	}
}

void ABigWorldGameMode::NotifyPlayerReenter(uint32 PlayerKey, FName PlayerType)
{
	AUAEPlayerController* UAEPC = FindPlayerControllerWithPlayerKey(PlayerKey, PlayerType);
	if (UAEPC == nullptr)
	{
		return;
	}

	UE_LOG(LogBigWorld, Log, TEXT("---BWGM: NotifyPlayerReenter PlayerName=[%s] PlayerKey=[%u] PlayerType=[%s]"), *UAEPC->PlayerName, PlayerKey, *PlayerType.ToString());

	// wexuanhuang dirty code: we except that client connection should be empty when reentering, close it otherwise
	UNetConnection* Conn = UAEPC->GetNetConnection();
	if (Conn != nullptr)
	{
		const FString Msg = TEXT("PendingExit player reenter this ds");
		Conn->Close(EConnectionClosedReason::UserException_DefaultUserDefinedException, Msg);
	}
}

FIDIPActivityActorInfo ABigWorldGameMode::GetIDIPStateInfo(int32 ActivityActorID)
{
	UBWDynamicParaIDIPComponent* IdipComponent = FindComponentByClass<UBWDynamicParaIDIPComponent>();
	if(IdipComponent)
	{
		return IdipComponent->GetIDIPStateInfo(ActivityActorID);
	}
	return FIDIPActivityActorInfo();
}

bool ABigWorldGameMode::CheckIDIPStateInfoIsValid(int32 ActivityActorID)
{
	UE_LOG(LogBigWorld, Log, TEXT("ABigWorldGameMode: CheckIDIPStateInfoIsValid ActivityActorID %d"), ActivityActorID);
	UBWDynamicParaIDIPComponent* IdipComponent = FindComponentByClass<UBWDynamicParaIDIPComponent>();
	if(IdipComponent)
	{
		return IdipComponent->CheckIsValid(ActivityActorID);
	}
	return false;
}
