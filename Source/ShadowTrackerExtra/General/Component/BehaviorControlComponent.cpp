#include "ShadowTrackerExtra.h"
#include "BehaviorControlComponent.h"
// 
#include "AIController.h"
#include "BehaviorTree/BlackboardComponent.h"
#include "BehaviorTree/BehaviorTreeComponent.h"

UBehaviorControlComponent::UBehaviorControlComponent()
{
	PrimaryComponentTick.bCanEverTick = false;
}

void UBehaviorControlComponent::BeginPlay()
{
	Super::BeginPlay();
	auto OwnerCharacter = Cast<ACharacter>(GetOwner());
	if (OwnerCharacter)
	{
		if (auto AIC = Cast<AAIController>(OwnerCharacter->GetController()))
		{
			int stop = 0;
		}
	}
}

void UBehaviorControlComponent::BeginDestroy()
{
	Super::BeginDestroy();
}

void UBehaviorControlComponent::PostInitProperties()
{
	Super::PostInitProperties();
}

void UBehaviorControlComponent::PossessedBy(AController* NewController)
{
	if (auto AIC = Cast<AAIController>(NewController))
	{
		AIController = AIC;
		// 开始行为树
		RunBehavior(BehaviorTreeSetting.BehaviorTreePath);
		// 缓存必要组件
		BlackboardComp = AIC->GetBlackboardComponent();
		BehaviorTreeComp = Cast<UBehaviorTreeComponent>(AIC->GetBrainComponent());
		// 初始化黑板初始值
		if (BlackboardComp.IsValid())
		{
			BehaviorTreeSetting.InitializeBlackboardValue(BlackboardComp.Get());
		}
	}
}

void UBehaviorControlComponent::UnPossessed()
{
	StopBehavior(TEXT("UnPossessed"));

	AIController = nullptr;
	BlackboardComp = nullptr;
	BehaviorTreeComp = nullptr;
}

void UBehaviorControlComponent::OnRespawned(bool bFromPersistentPool)
{

}

void UBehaviorControlComponent::OnRecycled(bool bToPersistentPool)
{

}

void UBehaviorControlComponent::RunBehaviorByPath(const FString& InBehaviorPath)
{
	auto const BehaviorSoftObjectPtr = TSoftObjectPtr<UBehaviorTree>(InBehaviorPath);
	if (BehaviorSoftObjectPtr.IsValid())
	{
		RunBehavior(BehaviorSoftObjectPtr);
	}
}

void UBehaviorControlComponent::RunBehavior(const TSoftObjectPtr<UBehaviorTree>& InBehaviorPath)
{
	if (!AIController.IsValid()) return;

	UBehaviorTree* BTAsset = InBehaviorPath.Get();
	if (BTAsset == nullptr)
	{
		BTAsset = InBehaviorPath.LoadSynchronous();
	}
	if (BTAsset == nullptr)
	{
		UE_LOG(LogGenericCharacter, Warning, TEXT("UBehaviorControlComponent::RunBehavior: Unable to run NULL behavior tree"));
		return;
	}

	AIController->RunBehaviorTree(BTAsset);
	UE_LOG(LogGenericCharacter, Log, TEXT("UBehaviorControlComponent::RunBehaviorTree, BehaviorTree: %s, Controller: %s"), *GetNameSafe(BTAsset), *GetNameSafe(AIController.Get()));
}

void UBehaviorControlComponent::StopBehavior(const FString& Reason)
{
	if (BehaviorTreeComp.IsValid())
	{
		BehaviorTreeComp->StopLogic(Reason);
	}
}

void UBehaviorControlComponent::PauseBehavior(const FString& Reason)
{
	if (BehaviorTreeComp.IsValid())
	{
		BehaviorTreeComp->PauseLogic(Reason);
	}
}

void UBehaviorControlComponent::ResumeBehavior(const FString& Reason)
{
	if (BehaviorTreeComp.IsValid())
	{
		BehaviorTreeComp->ResumeLogic(Reason);
	}
}

void UBehaviorControlComponent::RestartBehavior()
{
	if (BehaviorTreeComp.IsValid())
	{
		BehaviorTreeComp->RestartLogic();
	}
}

void UBehaviorControlComponent::OnBehaviorNotify(const FString& InMessage)
{
	if (OnBehvaiorNotifyDelegate.IsBound())
	{
		OnBehvaiorNotifyDelegate.Broadcast(InMessage);
	}
}
