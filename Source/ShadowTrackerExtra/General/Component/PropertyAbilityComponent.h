#pragma once

#include "CoreMinimal.h"
#include "AttrModifyComponent.h"
#include "General/Interface/PropertyAbilityInterface.h"
#include "AttrModifyInterface.h" 
#include "General/TypeDefines.h"
#include "PropertyAbilityComponent.generated.h"

namespace GeneralCharacterBasicAttr
{
	const FName NAME_Health				= TEXT("Health");
	const FName NAME_HealthMax			= TEXT("HealthMax");
	const FName NAME_SpeedScale			= TEXT("SpeedScale");
	const FName NAME_DamageScale		= TEXT("DamageScale");
	const FName NAME_TakeDamageScale	= TEXT("TakeDamageScale");
}

/*
* 属性管理组件
* 存储基础属性值，如血量移速等
* 实际属性存储在AttrModifyComponent中（因为AttrModifyInterface接口和AttrModifyComponent有函数冲突所以不能继承AttrModifyComponent）
*/

UCLASS(config=Game, BlueprintType, Blueprintable)
class UPropertyAbilityComponent : public UActorComponent, public IPropertyAbilityInterface
{
	GENERATED_UCLASS_BODY()

public: 
	
	virtual void BeginPlay() override;

#if UE5_OR_LATER // UE5会将包含网络复制的类自动生成GetLifetimeReplicatedProps，所以需要定义一个
	virtual void GetLifetimeReplicatedProps(TArray<FLifetimeProperty>& OutLifetimeProps) const override
	{
		Super::GetLifetimeReplicatedProps(OutLifetimeProps);
	}
#else
	//virtual void GetLifetimeReplicatedProps(TArray<FLifetimeProperty>& OutLifetimeProps) const override;
#endif
	virtual void GetRepOnceReplicatedProps(TArray<FLifetimeProperty>& OutLifetimeProps) const override;
	//////////////////////////////////////
	//IAttrModifyInterface
	// virtual UAttrModifyComponent* GetAttrModifyComponent_Implementation()  override;

	// virtual float GetAttributeValue_Implementation(const FString& AttrName) override;
	// virtual void AddAttrValue_Implementation(const FString& AttrName, float AddVal, int32 Reason = -1) override;
	// virtual void SetAttrValue_Implementation(const FString& AttrName, float NewVal, int32 Reason = -1) override;
	//////////////////////////////////////

	//////////////////////////////////////
	//IPropertyAbilityInterface
	virtual float PA_GetAttributeValue(const FString& AttrName) override;

	virtual void PA_AddAttrValue(const FString& AttrName, float AddVal, int32 Reason = -1) override;

	virtual void PA_SetAttrValue(const FString& AttrName, float NewVal, int32 Reason = -1) override;
	
	virtual void RefreshAttrValueChangedDelegate(const FName& AttrName,bool IsAdd) override;

	//////////////////////////////////////

	UFUNCTION(BlueprintCallable)
	virtual float SetSpeedScale(float InSpeedScale) override;

	UFUNCTION(BlueprintCallable)
	virtual float GetSpeedScale() const override;

	UFUNCTION(BlueprintCallable)
	virtual float SetHealthSafety(float InHealth) override;
	
	UFUNCTION(BlueprintCallable)
	virtual float GetHealthSafety() const override;

	UFUNCTION(BlueprintCallable)
	virtual float AddHealthSafety(float AddHealth) override;

	UFUNCTION(BlueprintCallable)
	virtual bool IsAlive() override;

	virtual void InitAttr(int32 AttrId) override;
protected:
	UFUNCTION()
	virtual void InternalAddAttrValueChangedDelegate(const FName& AttrName, const float CurValue);

	UFUNCTION()
	virtual void OnRep_HealthMax(float PrevHealthMax);

	UFUNCTION()
	virtual void OnRep_Health(float PrevHealth);
protected:
	
	UPROPERTY()
	UAttrModifyComponent* OwnerAttrComp = nullptr;

	//基础属性
	UPROPERTY(EditAnywhere, BlueprintReadWrite, ReplicatedUsing = OnRep_Health, Category = AttrConfig, meta = (GameAttrGroup = Character, GameAttrOptionalDisplayName = "血量", AttrMin = 0.f, AttrMax = HealthMax))
	float Health = 100.f;

	UPROPERTY(EditAnywhere, BlueprintReadWrite, ReplicatedUsing = OnRep_HealthMax, Category = AttrConfig, meta = (GameAttrGroup = Character, GameAttrOptionalDisplayName = "最大血量", AttrMin = 0.f))
	float HealthMax = 100.f;

	UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = AttrConfig, meta = (GameAttrGroup = Character, GameAttrOptionalDisplayName = "移速倍率", AttrMin = 0.f))
	float SpeedScale = 1.0f;

	/*
	** 技能急速，值越大技能冷却越快结束
	*/
	/*UPROPERTY(BlueprintReadWrite, ReplicatedUsing = OnRep_SkillCDRecoverRate, meta = (DisplayName = "技能急速", GameAttrGroup = Character, GameAttrOptionalDisplayName = "技能急速", GameAttrUgcExpose = "true", AttrMin = 0.f, AttrMax = 3.f))
	FGameAttributeProperty SkillCDRecoverRate;*/

	//伤害属性
	UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = AttrConfig, meta = (GameAttrGroup = Character, GameAttrOptionalDisplayName = "伤害系数", AttrMin = 0.f))
	float DamageScale = 1.0f;

	UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = AttrConfig, meta = (GameAttrGroup = Character, GameAttrOptionalDisplayName = "受击系数", AttrMin = 0.f))
	float TakeDamageScale = 1.0f;

	UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = Config,  meta = (DisplayName = "属性ID"))
	int32 AttrId = 0;

	UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = Config, meta = (DisplayName = "属性表名"))
	FString AIAttrInfoTableName = TEXT("");

	UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = Config, meta = (DisplayName = "属性监听列表"))
	TArray<FString> ListenAttrValueChanged;
};