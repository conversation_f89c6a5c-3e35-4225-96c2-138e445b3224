// Fill out your copyright notice in the Description page of Project Settings.

#pragma once

#include "CoreMinimal.h"
#include "Vehicle/VehicleComponent.h"
#include "Vehicle/Myriapod/STExtraHorseVehicle.h"
#include "Vehicle/Myriapod/STMyriapodStateComponent.h"
#include "STHorseGrassStateComponent.generated.h"

/**
 * 
 */
UCLASS(ClassGroup = (Custom), meta = (BlueprintSpawnableComponent))
class  USTHorseGrassStateComponent : public USTMyriapodStateComponent
{
	GENERATED_BODY()


protected:
	// Called when the game starts
	virtual void BeginPlay() override;

	UPROPERTY()
		ASTExtraHorseVehicle* VehicleOwner;

	UPROPERTY(EditDefaultsOnly, BlueprintReadOnly, Category = "GrassTimer")
		float ServerHorseGrassInterval = 2.0f;

	float ServerHorseGrassTimer = 0.f;

public:
	virtual void OnVehicleTick(float DeltaTime) override;

protected:

	void  RecoverFuelAndHp(float DeltaTime);

	UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Horse Grass")
	float HPRecover;

	UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Horse Grass")
	float FuelRecover;
	
};
