// Copyright 1998-2017 Epic Games, Inc. All Rights Reserved.

#pragma once

#include "CoreMinimal.h"
#include "Misc/Guid.h"
#include "Templates/SubclassOf.h"
#include "MovieSceneTrack.h"
#include "ISequencerSection.h"
#include "ISequencer.h"
#include "ISequencerTrackEditor.h"
#include "TrackEditors/AttachTrackEditor.h"



// 
// class AActor;
// class FMenuBuilder;
// class USceneComponent;

/**
 * Tools for attaching an object to another object
 */
class F3DAdjustAttachTrackEditor
	: public F3DAttachTrackEditor
{
public:

	/**
	 * Constructor
	 *
	 * @param InSequencer The sequencer instance to be used by this tool
	 */
 	F3DAdjustAttachTrackEditor( TSharedRef<ISequencer> InSequencer );

	/** Virtual destructor. */
	virtual ~F3DAdjustAttachTrackEditor();

	/**
	 * Creates an instance of this class.  Called by a sequencer 
	 *
	 * @param OwningSequencer The sequencer instance to be used by this tool
	 * @return The new instance of this class
	 */
	static TSharedRef<ISequencerTrackEditor> CreateTrackEditor( TSharedRef<ISequencer> OwningSequencer );

public:

	// ISequencerTrackEditor interface
	virtual bool SupportsSequence(UMovieSceneSequence* InSequence) const
	{
		if (!InSequence ||  !InSequence->GetClass())
		{
			return false;
		}
		if (InSequence->GetClass()->GetName() == "PersistSkillSequence")
		{
			return false;
		}
		return true;
	}
	
	virtual void BuildObjectBindingsTrackMenu(FMenuBuilder& MenuBuilder, const TArray<FGuid>& ObjectBindings, const UClass* ObjectClass) override;

	virtual bool IsSupportMultiTracks() const override
	{
		return true;
	}

	virtual TSharedRef<ISequencerSection> MakeSectionInterface( UMovieSceneSection& SectionObject, UMovieSceneTrack& Track, FGuid ObjectBinding ) override;
	virtual bool SupportsType( TSubclassOf<UMovieSceneTrack> Type ) const override;

	virtual TSubclassOf<UMovieSceneTrack> GetAttachTrack() const override;

	// FTrackEditorActorPicker
 	//virtual bool IsActorPickable( const AActor* const ParentActor, FGuid ObjectBinding, UMovieSceneSection* InSection ) override;
 	virtual void ActorSocketPicked(const FName SocketName, USceneComponent* Component, AActor* ParentActor, TArray<FGuid> ObjectGuids, UMovieSceneSection* Section) override;

private:

	/** Delegate for AnimatablePropertyChanged in AddKey */
	FKeyPropertyResult AddKeyInternal(float KeyTime, const TArray<TWeakObjectPtr<UObject>> Objects, const FName SocketName, USceneComponent* Component, AActor* ParentActor);
};
