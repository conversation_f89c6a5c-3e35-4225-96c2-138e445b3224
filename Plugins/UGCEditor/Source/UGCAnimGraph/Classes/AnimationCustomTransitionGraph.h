// Copyright 1998-2017 Epic Games, Inc. All Rights Reserved.

#pragma once

#if 0

#include "CoreMinimal.h"
#include "UObject/ObjectMacros.h"
#include "AnimationGraph.h"
#include "AnimationCustomTransitionGraph.generated.h"

UCLASS(MinimalAPI)
class UUGCAnimationCustomTransitionGraph : public UUGCAnimationGraph
{
	GENERATED_UCLASS_BODY()

	// Result node within the state's animation graph
	UPROPERTY()
	class UUGCAnimGraphNode_CustomTransitionResult* MyResultNode;

	//@TODO: Document
	UGCANIMGRAPH_API class UUGCAnimGraphNode_CustomTransitionResult* GetResultNode();
};

#endif