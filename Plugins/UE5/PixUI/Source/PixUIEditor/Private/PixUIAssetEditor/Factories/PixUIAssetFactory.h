// Fill out your copyright notice in the Description page of Project Settings.

#pragma once

#include "CoreMinimal.h"

//only for editor
#if WITH_EDITOR

#include "Factories/Factory.h"
#include "UObject/Object.h"
#include "PixUIAssetFactory.generated.h"

/**
 * 
 */
UCLASS()
class UPixUIAssetFactory : public UFactory
{
	GENERATED_UCLASS_BODY()
public:
	//~ UFactory Interface

	//	virtual UObject* FactoryCreateBinary(UClass* Class, UObject* InParent, FName Name, EObjectFlags Flags, UObject* Context, const TCHAR* Type, const uint8*& Buffer, const uint8* BufferEnd, FFeedbackContext* Warn) override;
	virtual UObject* FactoryCreateFile(UClass* InClass, UObject* InParent, FName InName, EObjectFlags Flags, const FString& Filename, const TCHAR* Parms, FFeedbackContext* Warn, bool& bOutOperationCanceled) override;
	virtual UObject* FactoryCreateNew(UClass* InClass, UObject* InParent, FName InName, EObjectFlags Flags, UObject* Context, FFeedbackContext* Warn) override;
	static void TransformToPfbs(FString FilePath, FString& PFBS_Path);
};
#endif //WITH_EDITOR only for editor