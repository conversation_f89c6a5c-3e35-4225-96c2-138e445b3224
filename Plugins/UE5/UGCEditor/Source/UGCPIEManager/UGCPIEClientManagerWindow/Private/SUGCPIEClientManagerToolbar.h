#pragma once

#include "CoreMinimal.h"
#include "Widgets/DeclarativeSyntaxSupport.h"
#include "Widgets/SCompoundWidget.h"

class FUGCPIEClientManagerModel;

class SUGCPIEClientManagerToolbar
	: public SCompoundWidget
{
	TSharedPtr<FUGCPIEClientManagerModel> UGCPIEClientManagerModel = {};

	TSharedPtr<int32> CreateProcessClientId = {};
	TSharedPtr<int32> CreateProcessTeamId = {};
	TSharedPtr<bool> CreateProcessbPIEAsNormalFlow = {};

public:
	SUGCPIEClientManagerToolbar();

	SLATE_BEGIN_ARGS(SUGCPIEClientManagerToolbar) { }
	SLATE_END_ARGS()

	void Construct(FArguments const& InArgs, TSharedRef<FUGCPIEClientManagerModel> InUGCPIEClientManagerModel);

private:
	void HandlCreateProcessActionExecute();

	bool HandleCreateProcessActionCanExecute() const;

	void HandleRecreateProcessActionExecute();

	bool HandleRecreateProcessActionCanExecute() const;

	void HandleTerminateProcessActionExecute();

	bool HandleTerminateProcessctionCanExecute() const;
};