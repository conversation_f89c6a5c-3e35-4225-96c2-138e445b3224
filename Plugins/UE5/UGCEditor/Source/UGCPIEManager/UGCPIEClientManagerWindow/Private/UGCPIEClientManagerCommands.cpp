#include "UGCPIEClientManagerCommands.h"
#include "Framework/Commands/UICommandInfo.h"
#include "UGCStyleSet.h"
#include "Framework/Commands/InputChord.h"
#include "Internationalization/Text.h"

#define LOCTEXT_NAMESPACE "UGCPIEClientManagerCommands"

FUGCPIEClientManagerCommands::FUGCPIEClientManagerCommands()
	: TCommands<FUGCPIEClientManagerCommands>(
		TEXT("UGCPIEClientManager"),
		LOCTEXT("UGCPIEClientManagerCommands", "UGC PIE Client Manager Commands"),
		NAME_None, 
		FUGCStyle::GetStyleSetName()
	)
{

}

void FUGCPIEClientManagerCommands::RegisterCommands()
{
	UI_COMMAND(OpenUGCPIEClientManagerWindow, "Open UGC PIE Client Manager", "Open UGC PIE Client Manager", EUserInterfaceActionType::Button, FInputChord{});

	UI_COMMAND(CreateProcess, "Create Process", "Create process", EUserInterfaceActionType::Button, FInputChord{});
	UI_COMMAND(RecreateProcess, "Recreate Process", "Recreate process", EUserInterfaceActionType::Button, FInputChord{});
	UI_COMMAND(TerminateProcess, "Terminate Process", "Terminate process", EUserInterfaceActionType::Button, FInputChord{});
}

#undef LOCTEXT_NAMESPACE