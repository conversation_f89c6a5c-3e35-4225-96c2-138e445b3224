{"FileVersion": 3, "Version": 1, "VersionName": "1.0", "FriendlyName": "UGCConcert", "Description": "UGC multi-user collaboration and long-session PIE based on Concert system", "Category": "Other", "CreatedBy": "Galaxy Team", "CreatedByURL": "", "DocsURL": "", "MarketplaceURL": "", "SupportURL": "", "CanContainContent": true, "IsBetaVersion": false, "IsExperimentalVersion": false, "Installed": false, "EnabledByDefault": false, "SupportedEngineVersion": "UE5", "Modules": [{"Name": "UGCConcert", "Type": "Runtime", "LoadingPhase": "<PERSON><PERSON><PERSON>"}, {"Name": "UGCConcertGame", "Type": "Runtime", "LoadingPhase": "<PERSON><PERSON><PERSON>"}, {"Name": "UGCConcertEditor", "Type": "UGCEditor", "LoadingPhase": "<PERSON><PERSON><PERSON>"}, {"Name": "UGCConcertServer", "Type": "Editor", "LoadingPhase": "<PERSON><PERSON><PERSON>"}, {"Name": "UGCConcertRuntime", "Type": "Runtime", "LoadingPhase": "<PERSON><PERSON><PERSON>"}], "Plugins": [{"Name": "ConcertMain", "Enabled": true}, {"Name": "ConcertSyncCore", "Enabled": true}, {"Name": "ConcertSyncClient", "Enabled": true}, {"Name": "ConcertSyncServer", "Enabled": true}, {"Name": "UdpMessaging", "Enabled": true}, {"Name": "QuicMessaging", "Enabled": true}]}