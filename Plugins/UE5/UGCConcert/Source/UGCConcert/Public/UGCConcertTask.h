#pragma once

#include "CoreMinimal.h"
#include "UGCConcertCommon.h"

class FUGCConcertTask;
typedef TSharedPtr<FUGCConcertTask> FUGCConcertTaskHandle;
typedef TFunction<bool(bool)> FUGCConcertTaskCallable;

class UGCCONCERT_API FUGCConcertTask : public TSharedFromThis<FUGCConcertTask>
{
public:
	FUGCConcertTask(const FString& InName = TEXT(""), float InDuration = -1);
	static FUGCConcertTaskHandle Execute(const FString& InName, FUGCConcertTaskCallable&& Functor, float InDuration = -1, bool bSlowTask = false, bool bCanCancel = false, const FString& InMessage = TEXT(""));
	static void Stop(FUGCConcertTaskHandle Handle);
	static bool IsComplete(FUGCConcertTaskHandle Handle);
	
private:
	static TArray<FUGCConcertTaskHandle> OngoingTasks;
	static FCriticalSection Lock;
	
	FString DebugName;
	bool bSkipFirstFrame = true;
	FThreadSafeBool bFuncComplete = false;
	FThreadSafeBool bCancelOrTimeout = false;
	TUniquePtr<FSlowTask> SlowTask;
	FTSTicker::FDelegateHandle Ticker;
	FUGCConcertTimer Timer;
};