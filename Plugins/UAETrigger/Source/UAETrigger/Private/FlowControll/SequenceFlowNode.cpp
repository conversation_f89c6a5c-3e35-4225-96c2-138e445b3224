// Fill out your copyright notice in the Description page of Project Settings.

#include "FlowControll/SequenceFlowNode.h"
#include "UAETriggerObject.h"
#include "UAELevelDirector.h"
#include "FlowControll/TriggersFlowTree.h"

USequenceFlowNode::USequenceFlowNode(const FObjectInitializer& ObjectInitializer)
	: Super(ObjectInitializer),
	CurrentTriggerIndex(0)
{
	NodeType = EFlowNodeType::Sequence;
}

bool USequenceFlowNode::Execute()
{
	bool Result = false;
	if (TriggerObjects.IsValidIndex(CurrentTriggerIndex) &&
		TriggerObjects[CurrentTriggerIndex])
	{
		Result = TriggerObjects[CurrentTriggerIndex]->Execute();

		AUAELevelDirector* LevelDirector = CarriedFlowTree ? CarriedFlowTree->GetOwner() : nullptr;
		if (LevelDirector)
		{
			FUFlowNodeRepData NodeRepData;
			NodeRepData.RepData = FString::Printf(TEXT("%d"), CurrentTriggerIndex);
			NodeRepData.NodeID = this->NodeID;
			LevelDirector->SetFlowNodeRepData(NodeRepData);
		}
	}

	return Result;
}

void USequenceFlowNode::TickNode(float DeltaTime)
{
	if (TriggerObjects.IsValidIndex(CurrentTriggerIndex) &&
		TriggerObjects[CurrentTriggerIndex] &&
		TriggerObjects[CurrentTriggerIndex]->IsActive() &&
		TriggerObjects[CurrentTriggerIndex]->IsEnableTick())
	{
		TriggerObjects[CurrentTriggerIndex]->Update(DeltaTime);
	}
}

void USequenceFlowNode::ReceivedTriggerComplete(UUAETriggerObject* CompleteTrigger)
{
	++CurrentTriggerIndex;
	if (TriggerObjects.IsValidIndex(CurrentTriggerIndex) && TriggerObjects[CurrentTriggerIndex])
	{
		AUAELevelDirector* LevelDirector = CarriedFlowTree ? CarriedFlowTree->GetOwner() : nullptr;
		if (LevelDirector)
		{
			FUFlowNodeRepData NodeRepData;
			NodeRepData.RepData = FString::Printf(TEXT("%d"), CurrentTriggerIndex);
			NodeRepData.NodeID = this->NodeID;
			LevelDirector->SetFlowNodeRepData(NodeRepData);
		}

		TriggerObjects[CurrentTriggerIndex]->Execute();
	}
	// Goto Next FlowNode.
	else
	{
		NotifyNodeFinished();
	}
}

void USequenceFlowNode::RecoverByRepData(const FUFlowNodeRepData& NodeRepData)
{
	int32 RepTriggerIndex = FCString::Atoi(*NodeRepData.RepData);
	if (TriggerObjects.IsValidIndex(RepTriggerIndex))
	{
		CurrentTriggerIndex = RepTriggerIndex;
		TriggerObjects[CurrentTriggerIndex]->Execute();
	}
}

