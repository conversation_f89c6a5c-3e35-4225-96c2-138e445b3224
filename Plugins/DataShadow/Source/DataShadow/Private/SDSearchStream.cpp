#include "SDSearchStream.h"
#include "SDSearchUtils.h"
#include "SDSearchController.h"
////////////////////////////////////
FSDStreamSearch::FSDStreamSearch(const FString& InSearchValue)
	: SearchValue(InSearchValue)
	, bThreadCompleted(false)
	, StopTaskCounter(0)
	, MinimiumVersionRequirement(ESDVersion::FISD_VER_LATEST)
	, BlueprintCountBelowVersion(0)
	, ImaginaryDataFilter(ESDSearchQueryFilter::AllFilter)
{
	//FindInBPMgr = FFindInBlueprintSearchManager::GetThreadSafePtr();
	// Add on a Guid to the thread name to ensure the thread is uniquely named.
	Thread = FRunnableThread::Create(this, *FString::Printf(TEXT("FStreamSearch%s"), *FGuid::NewGuid().ToString()), 0, TPri_BelowNormal);
}

FSDStreamSearch::FSDStreamSearch(const FString& InSearchValue, ESDSearchQueryFilter InImaginaryDataFilter, ESDVersion InMinimiumVersionRequirement)
	: SearchValue(InSearchValue)
	, bThreadCompleted(false)
	, StopTaskCounter(0)
	, MinimiumVersionRequirement(InMinimiumVersionRequirement)
	, BlueprintCountBelowVersion(0)
	, ImaginaryDataFilter(InImaginaryDataFilter)
{
	//FindInBPMgr = FFindInBlueprintSearchManager::GetThreadSafePtr();
	// Add on a Guid to the thread name to ensure the thread is uniquely named.
	Thread = FRunnableThread::Create(this, *FString::Printf(TEXT("FStreamSearch%s"), *FGuid::NewGuid().ToString()), 0, TPri_BelowNormal);
}


FSDStreamSearch::FSDStreamSearch()
{
	Thread = FRunnableThread::Create(this, *FString::Printf(TEXT("FStreamSearch%s"), *FGuid::NewGuid().ToString()), 0, TPri_BelowNormal);
}

bool FSDStreamSearch::Init()
{
	return true;
}

uint32 FSDStreamSearch::Run()
{
	const double StartTime = FPlatformTime::Seconds();
	FSDSearchController::Instance()->BeginSearchQuery(this);

	TFunction<void(const TSharedPtr<FFindInShadowDatasResult>&)> OnResultReady = [this](const TSharedPtr<FFindInShadowDatasResult>& Result) {
		FScopeLock ScopeLock(&SearchCriticalSection);
		ItemsFound.Add(Result);
	};

	// Searching comes to an end if it is requested using the StopTaskCounter or continuing the search query yields no results
	for (TSharedPtr<FShadowDataBase> SDData : FSDSearchController::Instance()->DatasToSearch)
	{
		if (SDData.IsValid())
		{
			SDData->MakeImaginaryData();
			{
				TSharedPtr< FFiSDSearchInstance > SearchInstance(new FFiSDSearchInstance);
				TSharedPtr<FFindInShadowDatasResult> SearchResult;
				if (ImaginaryDataFilter != ESDSearchQueryFilter::AllFilter)
				{
					SearchInstance->MakeSearchQuery(*SearchValue, SDData->ImaginaryData);
					SearchInstance->CreateFilteredResultsListFromTree(ImaginaryDataFilter, FilteredImaginaryResults);
					SearchResult = SearchInstance->GetSearchResults(SDData->ImaginaryData);
				}
				else
				{
					SearchResult = SearchInstance->StartSearchQuery(*SearchValue, SDData->ImaginaryData);
				}

				// If there are children, add the item to the search results
				if (SearchResult.IsValid() /*&& SearchResult->Children.Num() != 0*/)
				{
					OnResultReady(SearchResult);
				}
			}
		}

		if (StopTaskCounter.GetValue())
		{
			// Ensure that the FiB Manager knows that we are done searching
			FSDSearchController::Instance()->EnsureSearchQueryEnds(this);
		}
	}

	bThreadCompleted = true;

	return 0;
}

void FSDStreamSearch::Stop()
{
	StopTaskCounter.Increment();
}

void FSDStreamSearch::Exit()
{

}

void FSDStreamSearch::EnsureCompletion()
{
	{
		FScopeLock CritSectionLock(&SearchCriticalSection);
		ItemsFound.Empty();
	}

	Stop();
	Thread->WaitForCompletion();
	delete Thread;
	Thread = NULL;
}

bool FSDStreamSearch::IsComplete() const
{
	return bThreadCompleted;
}

void FSDStreamSearch::GetFilteredItems(TArray< TSharedPtr<FFindInShadowDatasResult> >& OutItemsFound)
{
	FScopeLock ScopeLock(&SearchCriticalSection);
	OutItemsFound.Append(ItemsFound);
	ItemsFound.Empty();
}

float FSDStreamSearch::GetPercentComplete() const
{
	return FSDSearchController::Instance()->GetPercentComplete(this);
}

void FSDStreamSearch::GetFilteredImaginaryResults(TArray<TSharedPtr<FImaginaryFiSDData, ESPMode::ThreadSafe>>& OutFilteredImaginaryResults)
{
	OutFilteredImaginaryResults = MoveTemp(FilteredImaginaryResults);
}
