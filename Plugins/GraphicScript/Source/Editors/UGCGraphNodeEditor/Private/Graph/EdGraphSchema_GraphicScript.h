#pragma once

#include "CoreMinimal.h"
#include "EdGraph/EdGraphSchema.h"
#include "EdGraphUtilities.h"
#include "EdGraphSchema_GraphicScript.generated.h"

USTRUCT()
struct FGraphicScriptSchemaAction_NewNode : public FEdGraphSchemaAction_NewNode
{
	GENERATED_BODY()
public:
	FGraphicScriptSchemaAction_NewNode() {}

	FGraphicScriptSchemaAction_NewNode(FText InNodeCategory, FText InMenuDesc, FText InToolTip, const int32 InGrouping)
		: FEdGraphSchemaAction_NewNode(MoveTemp(InNodeCategory), MoveTemp(InMenuDesc), MoveTemp(InToolTip), InGrouping)
	{
		NodeTemplate = nullptr;
	}

	// FEdGraphSchemaAction interface
	//virtual UEdGraphNode* PerformAction(class UEdGraph* ParentGraph, UEdGraphPin* FromPin, const FVector2D Location, bool bSelectNewNode = true) override;
	// End of FEdGraphSchemaAction interface
};

struct FGraphicScriptGraphPanelPinFactory : public FGraphPanelPinFactory
{
public:
	virtual TSharedPtr<class SGraphPin> CreatePin(class UEdGraphPin* Pin) const override;
};

//struct  FGraphicScriptGraphNodeFactory : public FGraphPanelNodeFactory
//{
//	virtual TSharedPtr<class SGraphNode> CreateNode(class UEdGraphNode* InNode) const override;
//};
//
//struct FGraphicScriptGraphPinConnectionFactory : public FGraphPanelPinConnectionFactory
//{
//public:
//	virtual class FConnectionDrawingPolicy* CreateConnectionPolicy(const class UEdGraphSchema* Schema, int32 InBackLayerID, int32 InFrontLayerID, float ZoomFactor, const class FSlateRect& InClippingRect, class FSlateWindowElementList& InDrawElements, class UEdGraph* InGraphObj) const override;
//};

UCLASS()
class UEdGraphSchema_GraphicScript : public UEdGraphSchema
{
	GENERATED_BODY()

public:
	virtual FLinearColor GetPinTypeColor(const FEdGraphPinType& PinType) const override;
	virtual const FPinConnectionResponse CanCreateConnection(const UEdGraphPin* A, const UEdGraphPin* B) const override;
	virtual bool TryCreateConnection(UEdGraphPin* PinA, UEdGraphPin* PinB) const;
	virtual void GetGraphContextActions(FGraphContextMenuBuilder& ContextMenuBuilder) const override;
	virtual void GetContextMenuActions(const UEdGraph* CurrentGraph, const UEdGraphNode* InGraphNode, const UEdGraphPin* InGraphPin, class FMenuBuilder* MenuBuilder, bool bIsDebugging) const;
	virtual int32 GetNodeSelectionCount(const UEdGraph* Graph) const override;
public:
	// PinCategory 
	static FString PC_Statement;
	static FString PC_Input;
	static FString PC_Output;

	static FString PC_BlockField;
	static FString PC_BlockField_DropDown;
	static FString PC_BlockField_Number;
	static FString PC_BlockField_Input;
	static FString PC_BlockField_Label;
	static FString PC_BlockField_Value;

	bool CreateAutomaticValueBlockNodeAndConnections(UEdGraphPin* PinA, UEdGraphPin* PinB) const;
	FVector2D CalculateAveragePositionBetweenNodes(const UEdGraphPin* InputPin, const UEdGraphPin* OutputPin) const;

	class UBlockConnection* GetPinConnection(const UEdGraphPin*) const;
	class UBlockField* GetPinField(const UEdGraphPin* InPin) const;
};