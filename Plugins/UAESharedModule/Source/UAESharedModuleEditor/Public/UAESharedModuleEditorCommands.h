// Copyright 1998-2017 Epic Games, Inc. All Rights Reserved.

#pragma once

#include "CoreMinimal.h"
#include "Framework/Commands/Commands.h"
#include "UAESharedModuleEditorStyle.h"

class FUAESharedModuleEditorCommands : public TCommands<FUAESharedModuleEditorCommands>
{
public:

	FUAESharedModuleEditorCommands()
		: TCommands<FUAESharedModuleEditorCommands>(TEXT("UAESharedModuleEditor"), NSLOCTEXT("Contexts", "UAESharedModuleEditor", "UAESharedModuleEditor Plugin"), NAME_None, FUAESharedModuleEditorStyle::GetStyleSetName())
	{
	}

	// TCommands<> interface
	virtual void RegisterCommands() override;

public:
	TSharedPtr< FUICommandInfo > PluginAction;
	TSharedPtr<FUICommandInfo> RefreshFlowGraph;
	TSharedPtr< FUICommandInfo > OpenHistoryAction;
};
