// Copyright 1998-2017 Epic Games, Inc. All Rights Reserved.

#pragma once

#include "Modules/ModuleManager.h"
#include "UAEDataModelInterface.h"
#include "UAEDataModel.generated.h"

DECLARE_DYNAMIC_DELEGATE(FUAEDataModelDelegate);

class UUAEDataModelManager;

class FUAEDataModelModule : public IModuleInterface
{
public:

	/** IModuleInterface implementation */
	virtual void StartupModule() override;
	virtual void ShutdownModule() override;
};

//注意这个Model的划分，是以每个controller单例一份的，所以应该以功能模块划分
UCLASS(ClassGroup = (Custom),BlueprintType)
class UAEDATAMODEL_API UUAEDataModel : public UObject,public IUAEDataModelInterface
{
public:
	// Sets default values for this component's properties
	UUAEDataModel() {}

	GENERATED_BODY()

// 	注册自身变量发生变化时的回调
	virtual void RegistPropertyEvent(const FName& PropetyName, FUAEDataModelDelegate Delagete);

	virtual void Init(UUAEDataModelManager* Owner);

	virtual void CPF_CustomRepEvent_Implementation(UPropertyPtr ChangePro) override;

	virtual FUAEDataModelPropertyDataChangeDelegate& GetPropertyDataChangeDelegate()
	{
		return PropertyDataChangeDelegate;
	}

	//理論上應該是個子類方法，但是ob什麽的切數據源的時候，都要用，先放基類
	virtual void BindToObject(UObject* InTagObject);

	virtual bool IsDataValid()
	{
		if (bHaveBindObject)
		{
			return TagObject != nullptr;
		}

		return true;
	};

	FUAEDataModelPropertyDataChangeDelegate PropertyDataChangeDelegate;

//protected:
	UPROPERTY(Replicated)
	UObject* TagObject = nullptr;

	UFUNCTION()
	void OnBindObjectPropertyDataChange(UPropertyPtr Propety);

	void CopyValueIfSamePropety(FProperty* Propety);

	void NotifyPropertyEvent(FProperty* Propety);

protected:
	TWeakObjectPtr<UUAEDataModelManager> CachedManager;

private:
	//考虑一些只在ds用的数据
	bool bOnlyServer = false;

	bool bHaveBindObject = false;

	bool bIsBindSameClass = false;

};


//綁定一些object對象,考慮到觀戰和replay，bindto方法提升到基類
UCLASS(ClassGroup = (Custom))
class UAEDATAMODEL_API UUAEDataModel_BindObject : public UUAEDataModel
{
	GENERATED_BODY()
public:
	// Sets default values for this component's properties
	UUAEDataModel_BindObject() {};

	virtual void Init(UUAEDataModelManager* Owner)
	{
		Super::Init(Owner);
		InitBindObject();
	}

	virtual void InitBindObject() {};
public:
// 蓝图传变参引用取对应名字的数据，这类取法针对绑定已有数据源的对象，如playerstate
// 	UFUNCTION(BlueprintCallable, CustomThunk, Category = "LuaExtend", meta = (CustomStructureParam = "Param"))
// 	void GetPropertyValue(const FName& PropertyName, int32& Param) {}
// 
// #define Local_Get_Property(Addr, Prop, bCheck) \
// 	Stack.MostRecentProperty = nullptr; \
// 	Stack.StepCompiledIn<FProperty>(NULL); \
// 	uint8* Addr = Stack.MostRecentPropertyAddress; \
// 	FProperty* Prop = CastField<FProperty>(Stack.MostRecentProperty); \
// 	if(bCheck && (!Addr || !Prop)) \
// 	{ \
// 		return; \
// 	} 
// 
// 	DECLARE_FUNCTION(execCallLuaFunction_OneParam)
// 	{
// 		TArray<TPair<FProperty*, uint8*>> ParamList;
// 		ParamList.Empty(1);
// 
// 		P_GET_PROPERTY(FStrProperty, FuncName);
// 
// 		Local_Get_Property(Param_Value, Param_Prop, true);
// 		ParamList.Emplace(Param_Prop, Param_Value);
// 
// 		P_FINISH;
// 
// 		Generic_CallLuaFunction(FuncName, ParamList);
// 	}
};


UCLASS(ClassGroup = (Custom))
class UAEDATAMODEL_API UUAEDataModel_PlayerState : public UUAEDataModel_BindObject
{
	GENERATED_BODY()
public:
	// Sets default values for this component's properties
	UUAEDataModel_PlayerState() {};

	virtual void InitBindObject() override;

};