// Fill out your copyright notice in the Description page of Project Settings.

#pragma once

#include "IAssetExporterModule.h"
#include "Materials/MaterialInterface.h"
#include "Runtime/Launch/Resources/Version.h"

class AActor;

class ASSETEXPORTER_API FLevelAssetExporterUtils
{
public:
	static bool MergeActorsWrapper(const TArray<AActor*>& SelectedActors);

	static bool MergeActorsInLevelToStaticMeshAsset(const TArray<AActor*>& SelectedActors, const FMeshMergingSettings& InSettings, const FString& InBasePackageName, TArray<UObject*>& OutAssetsToSync, bool bSaveActor = true, FString SaveActorSuffix = TEXT("_ReferenceActor"));

	static bool MergeActorsToStaticMesh(const TArray<AActor*>& SelectedActors, const FMeshMergingSettings& InSettings, const FString& InBasePackageName, TArray<UObject*>& OutAssetsToSync, FVector& OutMergedActorLocation, const float ScreenSize);
};
