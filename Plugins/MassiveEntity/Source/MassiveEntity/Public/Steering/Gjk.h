#pragma once

#include "CoreMinimal.h"
#include "TypeDefine.h"
#include "Utils/GizmosUtils.h"
#include "SteerAgent.h"

namespace MassiveEntity
{
	// Gjk代理对象
	class FGjkProxy
	{
		/// Initialize the proxy using the given shape. The shape
		/// must remain in scope while the proxy is in use.
		void Set(const class FObstacle* shape);

		/// Initialize the proxy using a vertex cloud and radius. The vertices
		/// must remain in scope while the proxy is in use.
		FORCEINLINE void Set(const FVector* vertices, int32 count, float radius) 
		{
			m_vertices = vertices;
			m_count = count;
			m_radius = radius;
		}

		/// Get the supporting vertex index in the given direction.
		FORCEINLINE int32 GetSupport(const FVector& d) const
		{
			int32 bestIndex = 0;
			float bestValue = FVector::DotProduct(m_vertices[0], d);
			for (int32 i = 1; i < m_count; ++i)
			{
				float value = FVector::DotProduct(m_vertices[i], d);
				if (value > bestValue)
				{
					bestIndex = i;
					bestValue = value;
				}
			}

			return bestIndex;
		}

		/// Get the supporting vertex in the given direction.
		FORCEINLINE const FVector& GetSupportVertex(const FVector& d) const
		{
			int32 bestIndex = 0;
			float bestValue = FVector::DotProduct(m_vertices[0], d);
			for (int32 i = 1; i < m_count; ++i)
			{
				float value = FVector::DotProduct(m_vertices[i], d);
				if (value > bestValue)
				{
					bestIndex = i;
					bestValue = value;
				}
			}

			return m_vertices[bestIndex];
		}

		/// Get the vertex count.
		FORCEINLINE int32 GetVertexCount() const
		{
			return m_count;
		}

		/// Get a vertex by index. Used by b2Distance.
		const FVector& GetVertex(int32 index) const
		{
			check(0 <= index && index < m_count);
			return m_vertices[index];
		}

		FVector m_buffer[2];
		const FVector* m_vertices;
		int32 m_count;
		float m_radius;
	};

	/// Input parameters for b2ShapeCast
	struct FShapeCastInput
	{
		FGjkProxy proxyA;
		FGjkProxy proxyB;
		FTransform transformA;
		FTransform transformB;
		FVector translationB;
	};

	/// Output results for b2ShapeCast
	struct FShapeCastOutput
	{
		FVector point;
		FVector normal;
		float lambda;
		int32 iterations;
	};

	/// Perform a linear shape cast of shape B moving and shape A fixed. Determines the hit point, normal, and translation fraction.
	/// @returns true if hit, false if there is no hit or an initial overlap
	MASSIVEENTITY_API bool ShapeCast(FShapeCastOutput* output, const FShapeCastInput* input);
}