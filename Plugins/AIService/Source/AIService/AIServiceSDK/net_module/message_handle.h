// Copyright (c) 2019, Tencent Inc. All rights reserved.
// Author: hatchertang

#ifndef AISERVICE_SRC_NETMODULE_MESSAGE_HANDLE_H
#define AISERVICE_SRC_NETMODULE_MESSAGE_HANDLE_H

#include <functional>

#include "ai_service_impl.h"        // NOLINT
#include "session.h"

namespace avatar {

class MessageHandle {
public:
    MessageHandle();
    ~MessageHandle();

    // 在session的接收数据函数中调用，处理session中反序列化后的message
    RetCode HandleMessage(const Response* msg);

private:
    // 各个消息的分发处理函数
    RetCode HandleInitMsg(const Response* msg);
    RetCode HandleEpStartMsg(const Response* msg);
    RetCode HandleAgentStartMsg(const Response* msg);
    RetCode HandleUpdateMsg(const Response* msg);
    RetCode HandleAgentEndMsg(const Response* msg);
    RetCode HandleEpEndMsg(const Response* msg);
    RetCode HandleEventMsg(const Response* msg);
    RetCode HandleRejectMsg(const Response* msg);
    RetCode HandleRestartMsg(const Response* msg);

    // 检查应答包的返回值，返回kSuccess表示可以继续正常分发此包
    // 返回kRequestNewAgentServer表示当前预测服务不提供服务，重新请求新的预测服务
    RetCode CheckRetCode(int ret_code);
};

} // namespace avatar

#endif // AISERVICE_SRC_NETMODULE_MESSAGE_HANDLE_H
