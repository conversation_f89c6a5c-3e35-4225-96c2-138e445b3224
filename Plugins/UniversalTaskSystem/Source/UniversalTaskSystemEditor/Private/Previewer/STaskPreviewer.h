// Copyright Epic Games, Inc. All Rights Reserved.

#pragma once

#include "CoreMinimal.h"
#include "SlateFwd.h"
#include "Styling/SlateColor.h"
#include "Widgets/DeclarativeSyntaxSupport.h"
#include "Input/Reply.h"
#include "Widgets/SWidget.h"
#include "Widgets/SCompoundWidget.h"
#include "Widgets/SBoxPanel.h"
#include "Factories/Factory.h"

#include "STaskPreviewViewport.h"
#include "TaskPreviewViewprotClient.h"

class FUICommandList;

//////////////////////////////////////////////////////////////////////////
// STaskPreviewer

class STaskPreviewer : public SCompoundWidget
{
public:
	SLATE_BEGIN_ARGS( STaskPreviewer )
	{}
	
	SLATE_END_ARGS()

	virtual ~STaskPreviewer();
	virtual FReply OnKeyDown(const FGeometry& MyGeometry, const FKeyEvent& InKeyEvent);

	void Construct( const FArguments& InArgs, TSharedPtr<class FUniversalTaskEditor> InUniversalTaskEditorPtr);
	void RunPreview();
	void RefreshViewport();
	TSharedPtr<FTaskPreviewViewportClient> GetViewportClient() const;
	TSharedPtr<STaskPreviewViewport> GetViewportWidget() const { return ViewportWidget; }

private:
	TSharedPtr<FEditorViewportClient> ViewportClient;
	TSharedPtr<STaskPreviewViewport> ViewportWidget;
	TSharedPtr<FTaskPreviewScene> PreviewScene;
	TWeakPtr<class FUniversalTaskEditor> UniversalTaskEditorPtr;
};