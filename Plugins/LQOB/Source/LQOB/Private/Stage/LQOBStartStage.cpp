#include "LQOBStartStage.h"
#include "Request/LQOBStartRequest.h"
#include "Request/LQOBReconnectRequest.h"
#include "LQOBManager.h" // [tarikwu][026-031]includes auto completion

#if ENABLE_LQOB
FLQOBStartStage::FLQOBStartStage()
{
	LQOBGamePlayProxy = nullptr;
}

bool FLQOBStartStage::Tick(FLQOBStageController* LQOBStageController, TSharedPtr<FLQOBAbstractRequest, ESPMode::ThreadSafe>& OutLQOBRequest)
{
	bool bOutRequestValid = false;
	do
	{
		if (!LQOBStageController || !LQOBGamePlayProxy)
		{
			break;
		}

		if (!LQOBGamePlayProxy->IsMatchPlaying())
		{
			if (LQOBStageController)
			{
				LQOBStageController->ChangeToStage(ELQOBStage::LQOBST_END);
			}
			break;
		}
		uint64 RoomId = LQOBGamePlayProxy->GetRoomId();;
		if (RoomId == 0)
		{
			break;
		}

		if (LQOBGamePlayProxy->IsReconnectEnter())
		{
			//Reconnect Enter
			TSharedPtr<FLQOBReconnectRequest, ESPMode::ThreadSafe> LQOBRequest = MakeShared<FLQOBReconnectRequest, ESPMode::ThreadSafe>();
			if (!LQOBRequest.IsValid())
			{
				break;
			}
			LQOBRequest->RoomId = RoomId;
			OutLQOBRequest = LQOBRequest;
		}
		else
		{
			//Normal Enter
			TSharedPtr<FLQOBStartRequest, ESPMode::ThreadSafe> LQOBRequest = MakeShared<FLQOBStartRequest, ESPMode::ThreadSafe>();
			if (!LQOBRequest.IsValid())
			{
				break;
			}
			LQOBRequest->RoomId = RoomId;
			OutLQOBRequest = LQOBRequest;
		}
		//clear queue at Start/Reconnect of the match
		FLQOBManager* LQOBManager = GetLQOBManager();
		if (LQOBManager)
		{
			LQOBManager->ClearLQOBRequestsQueue();
		}
		bOutRequestValid = true;

		LQOBStageController->ChangeToStage(ELQOBStage::LQOBST_CONFIG);
	} while (0);
	if (bOutRequestValid)
	{
		UE_LOG(LogLQOB, Display, TEXT("FLQOBStartStage::Tick Valid"));
	}
	return bOutRequestValid;
}
#endif