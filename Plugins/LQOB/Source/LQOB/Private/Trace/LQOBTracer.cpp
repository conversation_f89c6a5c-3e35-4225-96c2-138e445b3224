#include "LQOBTracer.h"
#include "HAL/PlatformFilemanager.h"
#include "Misc/Paths.h"
#include "Misc/FileHelper.h"
#include "GenericPlatform/GenericPlatformFile.h"

#if ENABLE_LQOB&&ENABLE_LQOB_TRACE
#define LQOBT_CHECK_ENABLE_OR_RETURN if(!IsEnableTrace()) return;

#define LQOBT_CHECK_ENABLE_OR_RETURN_FALSE if(!IsEnableTrace()) return false;

FLQOBTracer::FLQOBTracer()
{
	bEnableTrace = false;
	SaveDirectoryPath = TEXT("");
	SaveFileAbsPath = TEXT("");
}

void FLQOBTracer::SetEnableTrace(bool InEnableTrace)
{
	bEnableTrace = InEnableTrace;
}

void FLQOBTracer::Startup()
{
	LQOBT_CHECK_ENABLE_OR_RETURN;
}

void FLQOBTracer::Shutdown()
{
	LQOBT_CHECK_ENABLE_OR_RETURN;
}

void FLQOBTracer::OnMatchStart(uint64 InRoomID)
{
	LQOBT_CHECK_ENABLE_OR_RETURN;

	FString TimeString = FDateTime::UtcNow().ToString();
	FString FileBaseName = FString::Printf(TEXT("%lld-%s.log"), InRoomID, *TimeString);
	SetSaveFileBaseName(FileBaseName);
}

void FLQOBTracer::OnMatchEnd()
{
	LQOBT_CHECK_ENABLE_OR_RETURN;
}

bool FLQOBTracer::SetSaveDirectoryPath(const FString& InDirectoryPath)
{
	LQOBT_CHECK_ENABLE_OR_RETURN_FALSE;
	if (InDirectoryPath.IsEmpty())
	{
		return false;
	}
	if (!FPaths::DirectoryExists(InDirectoryPath))
	{
		IPlatformFile& PlatformFile = FPlatformFileManager::Get().GetPlatformFile();
		PlatformFile.CreateDirectoryTree(*InDirectoryPath);
		if (!FPaths::DirectoryExists(InDirectoryPath))
		{
			return false;
		}
	}
	SaveDirectoryPath = FPaths::ConvertRelativePathToFull(InDirectoryPath);
	UE_LOG(LogLQOB, Warning, TEXT("SetSaveDirectoryPath SaveDirectoryPath = %s"), *SaveDirectoryPath);
	return true;
}

bool FLQOBTracer::AppendString(const FString& InString)
{
	LQOBT_CHECK_ENABLE_OR_RETURN_FALSE;
	if (InString.IsEmpty() || SaveFileAbsPath.IsEmpty())
	{
		return false;
	}
	FString SaveStringLine = FString::Printf(TEXT("%s\n"), *InString);
	FFileHelper::SaveStringToFile(SaveStringLine, *SaveFileAbsPath, FFileHelper::EEncodingOptions::ForceUTF8WithoutBOM, &IFileManager::Get(), EFileWrite::FILEWRITE_Append);
	return true;
}

bool FLQOBTracer::TraceElement(FLQOBBaseTraceElement* LQOBBaseTraceElement)
{
	LQOBT_CHECK_ENABLE_OR_RETURN_FALSE;
	if (!LQOBBaseTraceElement)
	{
		return false;
	}
	FString OutputStringLine = TEXT("");
	LQOBBaseTraceElement->Serialize(OutputStringLine);
	if (OutputStringLine.IsEmpty())
	{
		UE_LOG(LogLQOB, Error, TEXT("FLQOBTracer TraceElement OutputStringLine is Empty!"));
		return false;
	}
	return AppendString(OutputStringLine);;
}

bool FLQOBTracer::SetSaveFileBaseName(const FString& InFileBaseName)
{
	LQOBT_CHECK_ENABLE_OR_RETURN_FALSE;

	if (SaveDirectoryPath.IsEmpty() || InFileBaseName.IsEmpty())
	{
		return false;
	}
	if (!FPaths::DirectoryExists(SaveDirectoryPath))
	{
		return false;
	}

	SaveFileAbsPath = FPaths::ConvertRelativePathToFull(SaveDirectoryPath / InFileBaseName);
	UE_LOG(LogLQOB, Warning, TEXT("SetSaveFileBaseName SaveFileAbsPath = %s"), *SaveFileAbsPath);
	return true;
}

bool FLQOBTracer::IsEnableTrace()
{
	return bEnableTrace;
}

namespace LQOB
{
	static FLQOBTracer* GLQOBTracer = nullptr;

	FLQOBTracer* GetLQOBTracer()
	{
		if (!GLQOBTracer)
		{
			GLQOBTracer = new FLQOBTracer();
		}
		return GLQOBTracer;
	}

	void DestroyLQOBTracer()
	{
		if (GLQOBTracer)
		{
			delete GLQOBTracer;
			GLQOBTracer = nullptr;
		}
	}
}
#endif