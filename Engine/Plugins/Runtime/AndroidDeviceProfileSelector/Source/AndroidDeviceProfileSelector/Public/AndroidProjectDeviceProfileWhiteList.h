// Fill out your copyright notice in the Description page of Project Settings.
// @SStudio dreamwzhang - BEGIN
#pragma once

#include "CoreMinimal.h"
#include "UObject/NoExportTypes.h"
#include "AndroidProjectDeviceProfileWhiteList.generated.h"

struct FProfileMatch;

/**
 * 
 */
UCLASS(config = DeviceProfiles)
class UAndroidProjectDeviceProfileWhiteList : public UObject
{
	GENERATED_UCLASS_BODY()
	
public:
	UPROPERTY(EditAnywhere, config, Category = "Grade")
	TArray<FProfileMatch> GradeMatchProfileWhiteList;
};

// @SStudio dreamwzhang - END
