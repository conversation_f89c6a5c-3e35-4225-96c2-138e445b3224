#pragma once

#include "CoreMinimal.h"
#include "Widgets/SCompoundWidget.h"


class STOmniverseServiceFrontend : public SCompoundWidget
{
public:
	// default constructor.
	STOmniverseServiceFrontend();
	virtual ~STOmniverseServiceFrontend();

	SLATE_BEGIN_ARGS(STOmniverseServiceFrontend) {}
	SLATE_END_ARGS()

	// constructs this widget.
	void Construct(const FArguments& InArgs);

private:
	TSharedPtr<SWidget> MakeFarmSettingPanel();
	TSharedPtr<SWidget> MakeNucleusSettingPanel();
	void OnShowOmniverseSettings();
	void OnShowUSDSettings();
};
