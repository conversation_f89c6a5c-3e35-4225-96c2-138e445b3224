#include "TOmniverseClient.h"
#include "TOmniverseFarmClient.h"
#include "TOmniverseSettings.h"
#include "TOmniverseLog.h"
#include "TUSDMiscModule.h"
#include "Misc/CoreDelegates.h"
#include <OmniClient.h>
#include <string>

#define CLIENT_MAX_URL_SIZE 2048


static void
ClientLogCallback(
	const char* ThreadName,
	const char* Component,
	OmniClientLogLevel ClientLogLevel,
	const char* Message) OMNICLIENT_CALLBACK_NOEXCEPT
{
	switch (ClientLogLevel)
	{
		case eOmniClientLogLevel_Debug:
		case eOmniClientLogLevel_Verbose:
			TOV_VERBOSE(TEXT("%s[%s]: %s"), ANSI_TO_TCHAR(Component), ANSI_TO_TCHAR(ThreadName), ANSI_TO_TCHAR(Message));
			break;
		case eOmniClientLogLevel_Info:
			TOV_LOG(Display, TEXT("%s[%s]: %s"), ANSI_TO_TCHAR(Component), ANSI_TO_TCHAR(ThreadName), ANSI_TO_TCHAR(Message));
			break;
		case eOmniClientLogLevel_Warning:
			TOV_LOG(Warning, TEXT("%s[%s]: %s"), ANSI_TO_TCHAR(Component), ANSI_TO_TCHAR(ThreadName), ANSI_TO_TCHAR(Message));
			break;
		case eOmniClientLogLevel_Error:
			TOV_LOG(Error, TEXT("%s[%s]: %s"), ANSI_TO_TCHAR(Component), ANSI_TO_TCHAR(ThreadName), ANSI_TO_TCHAR(Message));
			break;

		default:
			TOV_LOG(Fatal, TEXT("Undefined log message triggered by Omniverse client."))
	}
}


static void
ClientConnectionStatusCallback(
	void* UserData,
	const char* Url,
	OmniClientConnectionStatus Status) OMNICLIENT_CALLBACK_NOEXCEPT
{
	const FString ConnectionStatusString(omniClientGetConnectionStatusString(Status));
	TOV_LOG(Display, TEXT("Omniverse client connection status changed: %s"), *ConnectionStatusString);

	switch (Status)
	{
		case eOmniClientConnectionStatus_Connecting:
			static_cast<UTOmniverseClient*>(UserData)->UpdateConnectionStatus(ANSI_TO_TCHAR(Url), ETOmniverseConnectionStatus::Connecting);
			break;

		case eOmniClientConnectionStatus_Connected:
			static_cast<UTOmniverseClient*>(UserData)->UpdateConnectionStatus(ANSI_TO_TCHAR(Url), ETOmniverseConnectionStatus::Connected);
			break;

		case eOmniClientConnectionStatus_ConnectError:
			static_cast<UTOmniverseClient*>(UserData)->UpdateConnectionStatus(ANSI_TO_TCHAR(Url), ETOmniverseConnectionStatus::ConnectError);
			break;

		case eOmniClientConnectionStatus_Disconnected:
			static_cast<UTOmniverseClient*>(UserData)->UpdateConnectionStatus(ANSI_TO_TCHAR(Url), ETOmniverseConnectionStatus::Disconnected);
			break;

		case eOmniClientConnectionStatus_SignedOut:
			TOV_LOG(Display, TEXT("You have successfully signed out of Nucleus."));
			static_cast<UTOmniverseClient*>(UserData)->UpdateConnectionStatus(ANSI_TO_TCHAR(Url), ETOmniverseConnectionStatus::Disconnected);
			break;

		case eOmniClientConnectionStatus_NoUsername:
		case eOmniClientConnectionStatus_AuthAbort:
		case eOmniClientConnectionStatus_AuthCancelled:
		case eOmniClientConnectionStatus_AuthError:
		case eOmniClientConnectionStatus_AuthFailed:
			TOV_LOG(Error, TEXT("The authentication of Nucleus is failed."));
			static_cast<UTOmniverseClient*>(UserData)->UpdateConnectionStatus(ANSI_TO_TCHAR(Url), ETOmniverseConnectionStatus::ConnectError);
			break;

		case eOmniClientConnectionStatus_ServerIncompatible:
			TOV_LOG(Error, TEXT("The Nucleus server is not compatible with this version of the client library."));
			static_cast<UTOmniverseClient*>(UserData)->UpdateConnectionStatus(ANSI_TO_TCHAR(Url), ETOmniverseConnectionStatus::ConnectError);
			break;

		case eOmniClientConnectionStatus_InvalidHost:
			TOV_LOG(Error, TEXT("The Nucleus host name is invalid."));
			static_cast<UTOmniverseClient*>(UserData)->UpdateConnectionStatus(ANSI_TO_TCHAR(Url), ETOmniverseConnectionStatus::ConnectError);
			break;

		default:
			ensureMsgf(false, TEXT("Undefined connection status triggered by Omniverse client."));
	}
}


static void
ClientLiveQueuedCallback(
	void* UserData,
	struct OmniClientLiveUpdateInfo* Info) OMNICLIENT_CALLBACK_NOEXCEPT
{
	char UrlBuffer[CLIENT_MAX_URL_SIZE];
	size_t UrlSize = sizeof(UrlBuffer);
	if (Info->updateType != eOmniClientLiveUpdateType_Remote &&
		Info->updateType != eOmniClientLiveUpdateType_Local) { return; }

	if (!omniClientMakeUrl(Info->url, UrlBuffer, &UrlSize))
	{
		TOV_LOG(Error, TEXT("ClientLiveQueuedCallback: URL buffer overflow"));
		return;	
	}

	static_cast<UTOmniverseClient*>(UserData)->HandleLiveQueued(
		UTF8_TO_TCHAR(UrlBuffer), Info->sequenceNum, Info->serverTime,
		Info->updateType == eOmniClientLiveUpdateType_Remote);
}


UTOmniverseClient::UTOmniverseClient(const FObjectInitializer& ObjectInitializer)
	: Super(ObjectInitializer)
{
	bInitialized = false;
	bLivelink = false;
	ConnectionStatus = ETOmniverseConnectionStatus::Disconnected;
}


void
UTOmniverseClient::PostInitProperties()
{
	Super::PostInitProperties();
	omniClientSetLogLevel(eOmniClientLogLevel_Debug);
	omniClientSetLogCallback(ClientLogCallback);
	omniClientRegisterConnectionStatusCallback(this, ClientConnectionStatusCallback);
	omniClientLiveRegisterQueuedCallback2(this, ClientLiveQueuedCallback);

	// Try to initialize the client module.
	if (omniClientInitialize(kOmniClientVersion))
	{
		bInitialized = true;
	}
	else
	{
		TOV_LOG(Error, TEXT("Omniverse Client Initialization Failed!"));
	}
}


void
UTOmniverseClient::BeginDestroy()
{
	DisableLivelink();
	LiveWaitForPendingUpdates();

	omniClientRegisterConnectionStatusCallback(nullptr, nullptr);
	omniClientLiveRegisterQueuedCallback2(nullptr, nullptr);
	omniClientShutdown();
	omniClientSetLogCallback(nullptr);

	ConnectionStatus = ETOmniverseConnectionStatus::Disconnected;
	bInitialized = false;
	Super::BeginDestroy();
}


FString
UTOmniverseClient::GetFullUserWorkspace()
{
	if (IsInitialized() && IsConnected())
	{
		FString ProjectName = GetDefault<UTOmniverseSettings>()->ProjectName.ToUpper();
		FString WorkspaceRoot = ProjectName.IsEmpty() ?
			TEXT("Users") : FString::Printf(TEXT("Projects/%s/Users"), *ProjectName);

		return GetFullUrl(WorkspaceRoot / ServerInfo.Username, true, false);
	}

	TOV_LOG(Error, TEXT("Have not connected to the server, failing to get the workspace URL"));
	return TEXT("");
}


FString
UTOmniverseClient::GetFullProjectWorkspace()
{
	if (IsInitialized() && IsConnected())
	{
		FString ProjectName = GetDefault<UTOmniverseSettings>()->ProjectName.ToUpper();
		FString WorkspaceRoot = ProjectName.IsEmpty() ?
			TEXT("Library") : FString::Printf(TEXT("Projects/%s"), *ProjectName);

		return GetFullUrl(WorkspaceRoot, true, false);
	}

	TOV_LOG(Error, TEXT("Have not connected to the server, failing to get the workspace URL"));
	return TEXT("");
}


uint64
UTOmniverseClient::GetSequenceNum(const FString& InUrl)
{
	FLiveUpdateInfo* UpdateInfo = LiveUpdateCache.Find(InUrl);
	return UpdateInfo ? UpdateInfo->SequenceNum : 0;
}


void
UTOmniverseClient::Connect()
{
	FString FullUrl = FString::Printf(TEXT("omniverse://%s"), *GetDefault<UTOmniverseSettings>()->NucleusAddress);
	omniClientReconnect(TCHAR_TO_UTF8(*FullUrl));
}


void
UTOmniverseClient::Disconnect()
{
	FString FullUrl = FString::Printf(TEXT("omniverse://%s"), *GetDefault<UTOmniverseSettings>()->NucleusAddress);
	omniClientSignOut(TCHAR_TO_UTF8(*FullUrl));
}


/* TODO: The Livelink tick is better to be controlled by the "bInitialized" and "ConnectionStatus"
 *		 as well. However, since it would not be a harmful defect, we can refactor it later.
 */


void
UTOmniverseClient::EnableLivelink()
{
	if (IsInitialized() && !bLivelink)
	{
		TickDelegateHandle = FCoreDelegates::OnBeginFrame.AddUObject(this, &UTOmniverseClient::LiveProcess);
		bLivelink = true;
	}
}


void
UTOmniverseClient::DisableLivelink()
{
	if (bLivelink)
	{
		FCoreDelegates::OnBeginFrame.Remove(TickDelegateHandle);
		bLivelink = false;
	}
}


void
UTOmniverseClient::LiveProcess()
{
	FTUSDMutex& GlobalMutex = FTUSDMiscModule::GetGlobalMutex();
	if (IsInitialized() && IsConnected())
	{
		if (!GlobalMutex.TryLock(TEXT("LiveProcess")))
		{
			TOV_DEBUG(TEXT("The global mutex is locked by %s, refuse to call live process"), GlobalMutex.GetOwner());
			return;
		}

		omniClientLiveProcess();
		GlobalMutex.Unlock();
	}
}


bool UTOmniverseClient::LiveProcessUpTo(const FString& Url, const int64 ServerTime)
{
	FTUSDMutex& GlobalMutex = FTUSDMiscModule::GetGlobalMutex();
	if (IsInitialized() && IsConnected())
	{
		if (!GlobalMutex.TryLock(TEXT("LiveProcessUpTo")))
		{
			TOV_LOG(Error, TEXT("The global mutex is locked by %s, refuse to call live process up to"), GlobalMutex.GetOwner());
			return false;
		}

		FString FullUrl = GetFullUrl(Url, true, false);
		omniClientLiveProcessUpTo(
			TCHAR_TO_UTF8(*FullUrl),
			ServerTime ? ServerTime : omniClientLiveGetLatestServerTime(TCHAR_TO_UTF8(*FullUrl)));

		GlobalMutex.Unlock();
		return true;
	}

	return false;
}


bool
UTOmniverseClient::LiveWaitForPendingUpdates()
{
	if (IsInitialized() && IsConnected())
	{
		FTUSDMutex& GlobalMutex = FTUSDMiscModule::GetGlobalMutex();
		if (!GlobalMutex.TryLock(TEXT("LiveWaitForPendingUpdates")))
		{
			TOV_LOG(Error, TEXT("The global mutex is locked by %s, refuse to call live wait for pending updates"), GlobalMutex.GetOwner());
			return false;
		}

		omniClientLiveWaitForPendingUpdates();
		GlobalMutex.Unlock();
		return true;
	}

	return false;
}


FString
UTOmniverseClient::GetFullUrl(
	const FString& InUrl, bool bNormalize, bool bDiskCheck)
{
	FString OutUrl = InUrl;
	if (!OutUrl.StartsWith(TEXT("omniverse://")))
	{
		if (bDiskCheck && FPaths::DirectoryExists(FPaths::GetPath(OutUrl)))
		{
			TOV_LOG(Display, TEXT("%s is a valid disk path, skip prefixing omniverse identifier"), *OutUrl)
			return OutUrl;
		}

		OutUrl = FString::Printf(TEXT("omniverse://%s/%s"), *GetDefault<UTOmniverseSettings>()->NucleusAddress, *OutUrl);
	}

	return bNormalize ? NormalizeUrl(OutUrl) : OutUrl;
}


FString
UTOmniverseClient::NormalizeUrl(const FString& InUrl)
{
	char UrlBuffer[CLIENT_MAX_URL_SIZE];
	size_t UrlSize = sizeof(UrlBuffer);
	if (!omniClientNormalizeUrl(TCHAR_TO_UTF8(*InUrl), UrlBuffer, &UrlSize))
	{
		TOV_LOG(Error, TEXT("NormalizeUrl: URL buffer overflow"));
		return InUrl;
	}

	return UTF8_TO_TCHAR(UrlBuffer);
}


FString
UTOmniverseClient::MakeRelativeUrl(
	const FString& BaseUrl, const FString& OtherUrl)
{
	size_t ExpectedSize = 0;
	omniClientMakeRelativeUrl(TCHAR_TO_UTF8(*BaseUrl), TCHAR_TO_UTF8(*OtherUrl), nullptr, &ExpectedSize);
	std::string Result;
	Result.resize(ExpectedSize);
	omniClientMakeRelativeUrl(TCHAR_TO_UTF8(*BaseUrl), TCHAR_TO_UTF8(*OtherUrl), &Result[0], &ExpectedSize);

	// Remove trailing zero
	if (ExpectedSize > 0)
	{
		Result.erase(Result.size() - 1);
	}

	FString ResultString = UTF8_TO_TCHAR(Result.c_str());
	ResultString = ResultString.Replace(TEXT("\\"), TEXT("/"));
	return ResultString;
}


bool
UTOmniverseClient::IsUrlExisted(const FString& InUrl)
{
	struct FUEUserDataHolder
	{
		bool bResult;
		bool bFileExisted;
		FString Url;
	} UEHolder {false, false, InUrl};

	OmniClientRequestId RequestId = omniClientStat(
		TCHAR_TO_UTF8(*GetFullUrl(InUrl)), &UEHolder,
		[](void* UserData, OmniClientResult Result, const struct OmniClientListEntry* Entry) OMNICLIENT_CALLBACK_NOEXCEPT
		{
			FUEUserDataHolder* UEHolder = static_cast<FUEUserDataHolder*>(UserData);
			UEHolder->bResult = Result <= eOmniClientResult_OkLatest;

			if (!UEHolder->bResult)
			{
				// Avoid "Error:" in the string that cause it to be marked red in the log.
				FString ResultStatus = UTF8_TO_TCHAR(omniClientGetResultString(Result));
				TOV_DEBUG(TEXT("Failed to check the url existence \"%s\", status: %s"),
					*UEHolder->Url, *ResultStatus.Replace(TEXT("Error:"), TEXT("Error -")));
			}

			UEHolder->bFileExisted = UEHolder->bResult && Entry;
		});

	if (RequestId != kInvalidRequestId)
	{
		omniClientWait(RequestId);
		return UEHolder.bFileExisted;
	}

	TOV_LOG(Error, TEXT("The url existence request is invalid: %s"), *InUrl);
	return false;
}


bool
UTOmniverseClient::CheckUrlAccess(const FString& InUrl, bool& bRead, bool& bWrite)
{
	struct FUAUserDataHolder
	{
		bool bResult;
		bool bRead;
		bool bWrite;
		FString Url;
	} UAHolder {false, false, false, InUrl};

	OmniClientRequestId RequestId = omniClientStat(
		TCHAR_TO_UTF8(*GetFullUrl(InUrl)), &UAHolder,
		[](void* UserData, OmniClientResult Result, const struct OmniClientListEntry* Entry) OMNICLIENT_CALLBACK_NOEXCEPT
		{
			FUAUserDataHolder* UAHolder = static_cast<FUAUserDataHolder*>(UserData);
			UAHolder->bResult = Result <= eOmniClientResult_OkLatest;

			if (!UAHolder->bResult && Entry)
			{
				TOV_LOG(Warning, TEXT("Failed to check the url access \"%s\", status: %s"),
					*UAHolder->Url, UTF8_TO_TCHAR(omniClientGetResultString(Result)));
			}

			UAHolder->bRead = !!(Entry->access & (fOmniClientAccess_Read | fOmniClientAccess_Admin));
			UAHolder->bWrite = !!(Entry->access & (fOmniClientAccess_Write | fOmniClientAccess_Admin));
		});

	if (RequestId != kInvalidRequestId)
	{
		omniClientWait(RequestId);
		bRead = UAHolder.bResult && UAHolder.bRead;
		bWrite = UAHolder.bResult && UAHolder.bWrite;
		return UAHolder.bResult;
	}

	TOV_LOG(Error, TEXT("The url existence request is invalid: %s"), *InUrl);
	bRead = false;
	bWrite = false;
	return false;
}


TArray<FString>
UTOmniverseClient::ListUrl(const FString& InUrl)
{
	struct FLUUserDataHolder
	{
		bool bResult;
		FString Url;
		TArray<FString> Entries;
	} LUHolder {false, InUrl, {}};

	OmniClientRequestId RequestId = omniClientList(
		TCHAR_TO_UTF8(*GetFullUrl(InUrl)), &LUHolder,
		[](void* UserData, OmniClientResult Result, uint32_t NumEntries, struct OmniClientListEntry const* Entries) OMNICLIENT_CALLBACK_NOEXCEPT
		{
			FLUUserDataHolder* LUHolder = static_cast<FLUUserDataHolder*>(UserData);
			LUHolder->bResult = Result <= eOmniClientResult_OkLatest;

			if (!LUHolder->bResult)
			{
				TOV_LOG(Error, TEXT("Failed to list url \"%s\", status: %s"),
					*LUHolder->Url, UTF8_TO_TCHAR(omniClientGetResultString(Result)));
			}
			else if (NumEntries && Entries)
			{
				for (uint32_t Index = 0; Index < NumEntries; ++Index)
				{
					FString EntryPath = UTF8_TO_TCHAR(Entries[Index].relativePath);
					LUHolder->Entries.Add(EntryPath);
				}
			}
		});

	if (RequestId != kInvalidRequestId)
	{
		omniClientWait(RequestId);
		return LUHolder.Entries;
	}

	TOV_LOG(Error, TEXT("The list url request is invalid: %s"), *InUrl);
	return TArray<FString>();
}


void
UTOmniverseClient::SetRetries(int32 MaxMs, int32 BaseMs, int32 JitterMs)
{
	OmniClientRetryBehavior RetryBehavior;
	RetryBehavior.maxMs = MaxMs;
	RetryBehavior.baseMs = BaseMs;
	RetryBehavior.jitterMs = JitterMs;
	omniClientSetRetries(RetryBehavior);
}


bool
UTOmniverseClient::LockFile(const FString& InUrl)
{
	OmniClientResult Result = eOmniClientResult_Error;
	omniClientWait(omniClientLock(
		TCHAR_TO_UTF8(*GetFullUrl(InUrl)), &Result, [](void* UserData, OmniClientResult Result) OMNICLIENT_CALLBACK_NOEXCEPT
		{
			*static_cast<OmniClientResult*>(UserData) = Result;
		}));

	if (Result <= eOmniClientResult_OkLatest)
	{
		return true;
	}
	TOV_LOG(Error, TEXT("Failed to lock \"%s\", status: %s"), *InUrl, UTF8_TO_TCHAR(omniClientGetResultString(Result)));
	return false;
}


bool
UTOmniverseClient::UnlockFile(const FString& InUrl)
{
	OmniClientResult Result = eOmniClientResult_Error;
	omniClientWait(omniClientUnlock(
		TCHAR_TO_UTF8(*GetFullUrl(InUrl)), &Result, [](void* UserData, OmniClientResult Result) OMNICLIENT_CALLBACK_NOEXCEPT
		{
			*static_cast<OmniClientResult*>(UserData) = Result;
		}));

	if (Result <= eOmniClientResult_OkLatest)
	{
		return true;
	}
	TOV_LOG(Error, TEXT("Failed to unlock \"%s\", status: %s"), *InUrl, UTF8_TO_TCHAR(omniClientGetResultString(Result)));
	return false;
}


bool
UTOmniverseClient::DeleteDir(const FString& InUrl,
	bool bAsync, RequestCallback Callback)
{
	FUserDataHolder* UDHolder = new FUserDataHolder{false, bAsync, InUrl, Callback};
	OmniClientRequestId RequestId = omniClientDelete(
		TCHAR_TO_UTF8(*GetFullUrl(InUrl)), UDHolder,
		[](void* UserData, OmniClientResult Result) OMNICLIENT_CALLBACK_NOEXCEPT
		{
			FUserDataHolder* UDHolder = static_cast<FUserDataHolder*>(UserData);
			UDHolder->bResult = Result <= eOmniClientResult_OkLatest;

			if (!UDHolder->bResult)
			{
				TOV_LOG(Error, TEXT("Failed to delete dir \"%s\", status: %s"),
					*UDHolder->Url, UTF8_TO_TCHAR(omniClientGetResultString(Result)));
			}

			if (UDHolder->bAsync)
			{
				if (UDHolder->Callback) { UDHolder->Callback(UDHolder->bResult); }
				delete UDHolder;
			}
		});

	if (RequestId != kInvalidRequestId)
	{
		if (!bAsync)
		{
			omniClientWait(RequestId);
			bool bResult = UDHolder->bResult;
			delete UDHolder;
			return bResult;
		}

		return true;
	}

	TOV_LOG(Error, TEXT("The delete dir request is invalid: %s"), *InUrl);
	delete UDHolder;
	return false;
}


bool
UTOmniverseClient::ObliterateDir(const FString& InUrl, bool bObliterateCheckpoints,
	bool bAsync, RequestCallback Callback)
{
	FUserDataHolder* UDHolder = new FUserDataHolder{false, bAsync, InUrl, Callback};
	OmniClientRequestId RequestId = omniClientObliterate(
		TCHAR_TO_UTF8(*GetFullUrl(InUrl)), bObliterateCheckpoints, UDHolder,
		[](void* UserData, OmniClientResult Result) OMNICLIENT_CALLBACK_NOEXCEPT
		{
			FUserDataHolder* UDHolder = static_cast<FUserDataHolder*>(UserData);
			UDHolder->bResult = Result <= eOmniClientResult_OkLatest;

			if (!UDHolder->bResult)
			{
				TOV_LOG(Error, TEXT("Failed to obliterate dir \"%s\", status: %s"),
					*UDHolder->Url, UTF8_TO_TCHAR(omniClientGetResultString(Result)));
			}

			if (UDHolder->bAsync)
			{
				if (UDHolder->Callback) { UDHolder->Callback(UDHolder->bResult); }
				delete UDHolder;
			}
		});

	if (RequestId != kInvalidRequestId)
	{
		if (!bAsync)
		{
			omniClientWait(RequestId);
			bool bResult = UDHolder->bResult;
			delete UDHolder;
			return bResult;
		}

		return true;
	}

	TOV_LOG(Error, TEXT("The obliterate dir request is invalid: %s"), *InUrl);
	delete UDHolder;
	return false;
}


bool
UTOmniverseClient::CopyDir(const FString& SrcUrl, const FString& DstUrl, bool bOverwrite,
	bool bAsync, RequestCallback Callback)
{
	FUserDataHolder* UDHolder = new FUserDataHolder{false, bAsync, SrcUrl, Callback};
	OmniClientRequestId RequestId = omniClientCopy(
		TCHAR_TO_UTF8(*GetFullUrl(SrcUrl)),
		TCHAR_TO_UTF8(*GetFullUrl(DstUrl)),
		UDHolder, [](void* UserData, OmniClientResult Result) OMNICLIENT_CALLBACK_NOEXCEPT
		{
			FUserDataHolder* UDHolder = static_cast<FUserDataHolder*>(UserData);
			UDHolder->bResult = Result <= eOmniClientResult_OkLatest;

			if (!UDHolder->bResult)
			{
				TOV_LOG(Error, TEXT("Failed to copy dir \"%s\", status: %s"),
					*UDHolder->Url, UTF8_TO_TCHAR(omniClientGetResultString(Result)));
			}

			if (UDHolder->bAsync)
			{
				if (UDHolder->Callback) { UDHolder->Callback(UDHolder->bResult); }
				delete UDHolder;
			}
		},
		bOverwrite ? eOmniClientCopy_Overwrite : eOmniClientCopy_ErrorIfExists,
		TCHAR_TO_UTF8(*FString::Printf(TEXT("Copied from %s"), *SrcUrl)));

	if (RequestId != kInvalidRequestId)
	{
		if (!bAsync)
		{
			omniClientWait(RequestId);
			bool bResult = UDHolder->bResult;
			delete UDHolder;
			return bResult;
		}

		return true;
	}

	TOV_LOG(Error, TEXT("The copy dir request is invalid: %s"), *SrcUrl);
	delete UDHolder;
	return false;
}


bool
UTOmniverseClient::MoveDir(const FString& SrcUrl, const FString& DstUrl, bool bOverwrite,
	bool bAsync, RequestCallback Callback)
{
	FUserDataHolder* UDHolder = new FUserDataHolder{false, bAsync, SrcUrl, Callback};
	OmniClientRequestId RequestId = omniClientMove(
		TCHAR_TO_UTF8(*GetFullUrl(SrcUrl)),
		TCHAR_TO_UTF8(*GetFullUrl(DstUrl)),
		UDHolder, [](void* UserData, OmniClientResult Result, bool bCopied) OMNICLIENT_CALLBACK_NOEXCEPT
		{
			FUserDataHolder* UDHolder = static_cast<FUserDataHolder*>(UserData);
			UDHolder->bResult = Result <= eOmniClientResult_OkLatest;

			if (!UDHolder->bResult)
			{
				TOV_LOG(Error, TEXT("Failed to move dir \"%s\", status: %s"),
					*UDHolder->Url, UTF8_TO_TCHAR(omniClientGetResultString(Result)));
			}

			if (UDHolder->bAsync)
			{
				if (UDHolder->Callback) { UDHolder->Callback(UDHolder->bResult); }
				delete UDHolder;
			}
		},
		bOverwrite ? eOmniClientCopy_Overwrite : eOmniClientCopy_ErrorIfExists,
		TCHAR_TO_UTF8(*FString::Printf(TEXT("Moved from %s"), *SrcUrl)));

	if (RequestId != kInvalidRequestId)
	{
		if (!bAsync)
		{
			omniClientWait(RequestId);
			bool bResult = UDHolder->bResult;
			delete UDHolder;
			return bResult;
		}

		return true;
	}

	TOV_LOG(Error, TEXT("The move dir request is invalid: %s"), *SrcUrl);
	delete UDHolder;
	return false;
}


bool
UTOmniverseClient::WriteFile(const FString& InUrl, uint8* Content, size_t ContentSize,
	bool bAsync, RequestCallback Callback)
{
	OmniClientContent OmniContent = {
		Content,
		ContentSize,
		FMemory::Free
	};

	FUserDataHolder* UDHolder = new FUserDataHolder{false, bAsync, InUrl, Callback};
	OmniClientRequestId RequestId = omniClientWriteFile(
		TCHAR_TO_UTF8(*GetFullUrl(InUrl)), &OmniContent, UDHolder,
		[](void* UserData, OmniClientResult Result) OMNICLIENT_CALLBACK_NOEXCEPT
		{
			FUserDataHolder* UDHolder = static_cast<FUserDataHolder*>(UserData);
			UDHolder->bResult = Result <= eOmniClientResult_OkLatest;

			if (!UDHolder->bResult)
			{
				TOV_LOG(Error, TEXT("Failed to write file \"%s\", status: %s"),
					*UDHolder->Url, UTF8_TO_TCHAR(omniClientGetResultString(Result)));
			}

			if (UDHolder->bAsync)
			{
				if (UDHolder->Callback) { UDHolder->Callback(UDHolder->bResult); }
				delete UDHolder;
			}
		}
	);

	if (RequestId != kInvalidRequestId)
	{
		if (!bAsync)
		{
			omniClientWait(RequestId);
			bool bResult = UDHolder->bResult;
			delete UDHolder;
			return bResult;
		}

		return true;
	}

	TOV_LOG(Error, TEXT("The write file request is invalid: %s"), *InUrl);
	delete UDHolder;
	return false;
}


bool
UTOmniverseClient::ReadFile(const FString& InUrl, TArray<uint8>* OutArray,
	bool bAsync, TFunction<void(bool bSuccess, void* ReadData, size_t ContentSize)> Callback)
{
	struct FRFUserDataHolder
	{
		bool bResult;
		bool bAsync;
		FString Url;
		TArray<uint8>* OutArray;
		TFunction<void(bool bSuccess, void* ReadData, size_t ContentSize)> Callback;
	};

	FRFUserDataHolder* RFHolder = new FRFUserDataHolder{false, bAsync, InUrl, OutArray, Callback};
	OmniClientRequestId RequestId = omniClientReadFile(
		TCHAR_TO_UTF8(*GetFullUrl(InUrl)), RFHolder,
		[](void* UserData, OmniClientResult Result, const char* Version, OmniClientContent* Content) OMNICLIENT_CALLBACK_NOEXCEPT
		{
			FRFUserDataHolder* RFHolder = static_cast<FRFUserDataHolder*>(UserData);
			RFHolder->bResult = Result <= eOmniClientResult_OkLatest;

			if (!RFHolder->bResult)
			{
				TOV_LOG(Error, TEXT("Failed to read file \"%s\", status: %s"),
					*RFHolder->Url, UTF8_TO_TCHAR(omniClientGetResultString(Result)));
			}
			else if (Content == nullptr || Content->buffer == nullptr || Content->size == 0)
			{
				TOV_LOG(Error, TEXT("File \"%s\" is empty"), *RFHolder->Url);
				RFHolder->bResult = false;
			}

			if (RFHolder->bAsync)
			{
				if (RFHolder->bResult && RFHolder->Callback)
				{
					RFHolder->Callback(true, Content->buffer, Content->size);
				}
				else if (RFHolder->Callback)
				{
					RFHolder->Callback(false, nullptr, 0);
				}

				delete RFHolder;
			}
			else if (RFHolder->bResult && RFHolder->OutArray)
			{
				RFHolder->OutArray->SetNum(Content->size);
				FMemory::Memcpy(RFHolder->OutArray->GetData(), Content->buffer, Content->size);
			}
		}
	);

	if (RequestId != kInvalidRequestId)
	{
		if (!bAsync)
		{
			omniClientWait(RequestId);
			bool bResult = RFHolder->bResult;
			delete RFHolder;
			return bResult;
		}

		return true;
	}

	TOV_LOG(Error, TEXT("The read file request is invalid: %s"), *InUrl);
	delete RFHolder;
	return false;
}


TMap<FString, uint32_t>
UTOmniverseClient::GetACLs(const FString& InUrl)
{
	struct FGAUserDataHolder
	{
		bool bResult;
		FString Url;
		TMap<FString, uint32_t> ACLMap;
	} GAHolder {false, InUrl, {}};

	OmniClientRequestId RequestId = omniClientGetAcls(
		TCHAR_TO_UTF8(*GetFullUrl(InUrl)), &GAHolder,
		[](void* UserData, OmniClientResult Result, uint32_t NumEntries, struct OmniClientAclEntry* Entries) OMNICLIENT_CALLBACK_NOEXCEPT
		{
			FGAUserDataHolder* GAHolder = static_cast<FGAUserDataHolder*>(UserData);
			GAHolder->bResult = Result <= eOmniClientResult_OkLatest;

			if (!GAHolder->bResult)
			{
				TOV_LOG(Error, TEXT("Failed to get ACLs of url \"%s\", status: %s"),
					*GAHolder->Url, UTF8_TO_TCHAR(omniClientGetResultString(Result)));
			}
			else if (NumEntries && Entries)
			{
				for (uint32_t Index = 0; Index < NumEntries; ++Index)
				{
					FString Name = UTF8_TO_TCHAR(Entries[Index].name);
					GAHolder->ACLMap.Emplace(Name, Entries[Index].access);
				}
			}
		});

	if (RequestId != kInvalidRequestId)
	{
		omniClientWait(RequestId);
		return GAHolder.ACLMap;
	}

	TOV_LOG(Error, TEXT("The get ACLs request is invalid: %s"), *InUrl);
	return TMap<FString, uint32>();
}


TArray<FString>
UTOmniverseClient::GetUserGroups(const FString& InUrl, const FString& User)
{
	struct FUGUserDataHolder
	{
		bool bResult;
		FString User;
		TArray<FString> Groups;
	} UGHolder {false, User, {}};

	OmniClientRequestId RequestId = omniClientGetUserGroups(
		TCHAR_TO_UTF8(*GetFullUrl(InUrl)), TCHAR_TO_UTF8(*User), &UGHolder,
		[](void* UserData, OmniClientResult Result, uint64_t Count, const char** Groups) OMNICLIENT_CALLBACK_NOEXCEPT
		{
			FUGUserDataHolder* UGHolder = static_cast<FUGUserDataHolder*>(UserData);
			UGHolder->bResult = Result <= eOmniClientResult_OkLatest;

			if (!UGHolder->bResult)
			{
				TOV_LOG(Error, TEXT("Failed to get groups of user \"%s\", status: %s"),
					*UGHolder->User, UTF8_TO_TCHAR(omniClientGetResultString(Result)));
			}
			else if (Count && Groups)
			{
				for (uint32_t Index = 0; Index < Count; ++Index)
				{
					FString Group = UTF8_TO_TCHAR(Groups[Index]);
					UGHolder->Groups.Add(Group);
				}
			}
		});


	if (RequestId != kInvalidRequestId)
	{
		omniClientWait(RequestId);
		return UGHolder.Groups;
	}

	TOV_LOG(Error, TEXT("The get user groups request is invalid: %s[%s]"), *InUrl, *User);
	return TArray<FString>();	
}


void
UTOmniverseClient::UpdateConnectionStatus(
	const FString& Url,
	ETOmniverseConnectionStatus InConnectionStatus)
{
	const auto PrevConnectionStatus = ConnectionStatus;
	switch (InConnectionStatus)
	{
		case ETOmniverseConnectionStatus::Disconnected:
			TOV_LOG(Warning, TEXT("Disconnect with Nucleus server."));
			GetMutableDefault<UTOmniverseSettings>()->OnModified.RemoveAll(this);
			ServerInfo.Reset();
			DisableLivelink();
			break;

		case ETOmniverseConnectionStatus::Connecting:
			break;

		case ETOmniverseConnectionStatus::Connected:
			omniClientWait(omniClientGetServerInfo(TCHAR_TO_UTF8(*Url), &ServerInfo,
				[](void* UserData, OmniClientResult Result, const OmniClientServerInfo* Info) OMNICLIENT_CALLBACK_NOEXCEPT
				{
					FServerInfoType* ServerInfoPtr = static_cast<FServerInfoType*>(UserData);

					if (Result == eOmniClientResult_Ok && Info && UserData)
					{
						ServerInfoPtr->Version = ANSI_TO_TCHAR(Info->version);
						ServerInfoPtr->Username = ANSI_TO_TCHAR(Info->username);
						ServerInfoPtr->AuthToken = ANSI_TO_TCHAR(Info->authToken);
						ServerInfoPtr->bCacheEnabled = Info->cacheEnabled;
						ServerInfoPtr->bOmniObjectsEnabled = Info->omniObjectsEnabled;
						ServerInfoPtr->bCheckpointsEnabled = Info->checkpointsEnabled;
					}

					else
					{
						TOV_LOG(Error, TEXT("Connected with Nucleus server but failed to get server info."));
					}
				}));

			SetRetries(GetDefault<UTOmniverseSettings>()->RetryMaxMilliseconds);
			GetMutableDefault<UTOmniverseSettings>()->OnModified.AddUObject(this, &UTOmniverseClient::HandleSettingsModified);
			OnConnected.Broadcast();

			if (GetDefault<UTOmniverseSettings>()->bLivelinkByDefault)
			{
				EnableLivelink();
			}
			break;

		case ETOmniverseConnectionStatus::ConnectError:
			TOV_LOG(Error, TEXT("Failed to connect with Nucleus server."));
			GetMutableDefault<UTOmniverseSettings>()->OnModified.RemoveAll(this);
			ServerInfo.Reset();
			DisableLivelink();
			Disconnect();
			break;
		default:
			break;
	}

	ConnectionStatus = InConnectionStatus;
	if (PrevConnectionStatus != ConnectionStatus)
	{
		const auto CurConnectionStatus = ConnectionStatus;
		AsyncTask(ENamedThreads::Type::GameThread, [this, PrevConnectionStatus, CurConnectionStatus]
		{
			OnConnectionStatusChanged.Broadcast(CurConnectionStatus, PrevConnectionStatus);
		});
	}
}


void
UTOmniverseClient::HandleLiveQueued(const FString& Url, uint64 SequenceNum, uint64 ServerTime, bool bUpdateTypeRemote)
{
	TOV_DEBUG(TEXT("Client Live %s Queued [%d]: %s"), bUpdateTypeRemote ? TEXT("Remote") : TEXT("Local"), SequenceNum, *Url);
	if (SequenceNum == 0)
	{
		TOV_LOG(Warning, TEXT("The monitored live object has been deleted: \"%s\""), *Url);
		LiveUpdateCache.Remove(Url);
		return;
	}

	LiveUpdateCache.Emplace(Url, FLiveUpdateInfo(SequenceNum, ServerTime, bUpdateTypeRemote));
}


void
UTOmniverseClient::HandleSettingsModified(UObject* InObject, FProperty* InProperty)
{
	if (InProperty->GetName().Equals(TEXT("RetryMaxMilliseconds")))
	{
		int32 RetryMaxMilliseconds = GetDefault<UTOmniverseSettings>()->RetryMaxMilliseconds;
		SetRetries(RetryMaxMilliseconds);
		TOV_LOG(Display, TEXT("Set client max retry to %d milliseconds"), RetryMaxMilliseconds);
	}

	else if (InProperty->GetName().Equals(TEXT("NucleusAddress")))
	{
		TOV_LOG(Warning, TEXT("The address change will not take effect, plese reconnect to Nucleus"));
	}
}
