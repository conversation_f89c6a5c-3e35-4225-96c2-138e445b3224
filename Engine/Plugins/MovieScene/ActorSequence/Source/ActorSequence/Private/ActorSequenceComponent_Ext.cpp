// @SStudio leiizhao merge - BEGIN 补充CG添加接口

#include "ActorSequenceComponent.h"
#include "ActorSequence.h"
#include "ActorSequencePlayer.h"
#include "Net/UnrealNetwork.h"

void UActorSequenceComponent::InitBinding()
{
	if (!BindingOverrides)
	{
		BindingOverrides = NewObject<UMovieSceneBindingOverrides>(this, "BindingOverride");
	}
}

float UActorSequenceComponent::GetLength()
{
	if (!Sequence)
	{
		return 0;
	}

	if (UMovieScene* MovieScene = Sequence->GetMovieScene())
	{
		TRange<FFrameNumber> PlaybackRange = MovieScene->GetPlaybackRange();
		FFrameRate LegacyFrameRate = GetLegacyConversionFrameRate();
		return LegacyFrameRate.AsSeconds(PlaybackRange.GetUpperBoundValue().Value) - LegacyFrameRate.AsSeconds(PlaybackRange.GetLowerBoundValue().Value);
	}

	return 0;
}

void UActorSequenceComponent::StartPlay(float StartTime, bool bOpenTick)
{
	// @SStudio ziruyyan - BEGIN CJUE5 Update
	// UE_Server宏下依然会在游戏中造成ActorSequence相关的Server Bug，且PIE中不会出现问题
// #if !UE_SERVER
	
	InitPlayer();

	check(SequencePlayer);
	if (!Sequence)
	{
		return;
	}

	// ReplaceBinding();
	SequencePlayer->SetPlaybackClient(this);
	SequencePlayer->SetPlaybackSettings(PlaybackSettings);
	// @SStudio dreamwzhang - BEGIN 修复循环播放时没做范围判断，在超出范围时通过GetsegmentFromTime获取的section异常，无法求值
	//【【sequence】【avatar】他人视角下从远处跑过来后在秋千上的人状态异常】 https://tapd.woa.com/tapd_fe/70154368/bug/detail/1070154368143617858
	SequencePlayer->SetPlaybackPostionWithloop(StartTime);
	SequencePlayer->Play();
	//SequencePlayer->SetTimeRange(StartTime,SequencePlayer->GetLength());
	//PlaySequence();
	// @SStudio dreamwzhang - END

	// @SStudio ziruyyan - BEGIN CJUE5 Update
	// SequencePlayer->SetPlaybackClient(this);
	//
	// // Initialize this player for tick as soon as possible to ensure that a persistent
	// // reference to the tick manager is maintained
	// SequencePlayer->InitializeForTick(this);
	//
	// SequencePlayer->SetPlaybackPostionWithloop(StartTime);
	//
	// SequencePlayer->Play();
	// // @SStudio ziruyyan - END
	//
	// if (bOpenTick)
	// {
	// 	SetComponentTickEnabled(true);
	// }


	// @SStudio ziruyyan - END
	if (bOpenTick)
	{
		SetComponentTickEnabled(true);
	}
// @SStudio clickwang - BEGIN:
// #endif //!UE_SERVER
// @SStudio clikcwang - END
}

void UActorSequenceComponent::StopPlay()
{
	InitPlayer();

	check(SequencePlayer);
	if (!SequencePlayer)
	{
		return;
	}

	SequencePlayer->Stop();
	SetComponentTickEnabled(false);
}

void UActorSequenceComponent::AddBinding(FMovieSceneObjectBindingID Binding, UObject* Object, bool bAllowBindingsFromAsset)
{
	if (!Object) return;

	UE_LOG(LogTemp, Log, TEXT("UActorSequenceComponent::AddBinding :binding object %s "), *Object->GetName());

	InitBinding();

	BindingOverrides->AddBinding(Binding, Object);
	if (SequencePlayer)
	{
		SequencePlayer->State.Invalidate(Binding.GetGuid(), Binding.GetRelativeSequenceID());
	}

}

void UActorSequenceComponent::ReplaceBinding()
{
	if (BindingOverrides)
	{
		const TArray<FMovieSceneBindingOverrideData>& BindingData =  BindingOverrides->GetBindingData();
		if (BindingData.Num() > 0)
		{
			// Get Pawn Skeletal Component
			TArray<AActor*> ChildrenActors;
			GetOwner()->GetAttachedActors(ChildrenActors);
			AActor* Pawn = nullptr;
	
			if (ChildrenActors.Num() > 0)
			{
				Pawn = ChildrenActors[0];
				USkeletalMeshComponent* PawnSkeletalComponent = Pawn->FindComponentByClass<USkeletalMeshComponent>();
				if (PawnSkeletalComponent)
				{
					UObject* Component = Cast<UObject>(PawnSkeletalComponent);
					for (const FMovieSceneBindingOverrideData& OverrideData: BindingData)
					{
						FMovieSceneObjectBindingID Binding = OverrideData.ObjectBindingId;
					
						TSoftObjectPtr<UObject> Object = OverrideData.Object;

						if (Sequence->GetMovieScene()->FindBinding(Binding.GetGuid()))
						{
							// // Create New Possessable
							FGuid BindPossess = Sequence->GetMovieScene()->AddPossessable("AddBindPossess",PawnSkeletalComponent->GetClass());
							Sequence->BindPossessableObject(BindPossess,*Component,GetOwner());
							// Replace Possessable
							Sequence->GetMovieScene()->ReplacePossessable(Binding.GetGuid(),*Sequence->GetMovieScene()->FindPossessable(BindPossess));
							// Replace ObjectBinding
							Sequence->GetMovieScene()->ReplaceBinding(Binding.GetGuid(),*Sequence->GetMovieScene()->FindBinding(BindPossess));
						}
					}
				}
			}
		}
	}
}

void UActorSequenceComponent::RemoveBinding(FMovieSceneObjectBindingID Binding, UObject* Object)
{
	if (!Object) return;

	InitBinding();

	BindingOverrides->RemoveBinding(Binding, Object);
	if (SequencePlayer)
	{
		SequencePlayer->State.Invalidate(Binding.GetGuid(), Binding.GetRelativeSequenceID());
	}
}

void UActorSequenceComponent::ResetBinding(FMovieSceneObjectBindingID Binding)
{
	InitBinding();

	BindingOverrides->ResetBinding(Binding);
	if (SequencePlayer)
	{
		SequencePlayer->State.Invalidate(Binding.GetGuid(), Binding.GetRelativeSequenceID());
	}
}

void UActorSequenceComponent::ResetBindings()
{
	InitBinding();

	const TArray<FMovieSceneBindingOverrideData>& Data = BindingOverrides->GetBindingData();
	if (SequencePlayer)
	{
		for (auto&Var : Data)
		{
			SequencePlayer->State.Invalidate(Var.ObjectBindingId.GetGuid(), Var.ObjectBindingId.GetRelativeSequenceID());
		}
	}

	BindingOverrides->ResetBindings();

}

void UActorSequenceComponent::InitPlayer()
{
	if (Sequence && !SequencePlayer)
	{
		InitBinding();

		PlaybackSettings.BindingOverrides = BindingOverrides;
		if (GetNetMode() != NM_DedicatedServer)
		{
			GetWorld()->GetTimerManager().SetTimerForNextTick(this, &UActorSequenceComponent::OnRep_RepBindingData);
		}

		SequencePlayer = NewObject<UActorSequencePlayer>(this, "SequencePlayer");
		SequencePlayer->Initialize(Sequence, PlaybackSettings);

		SequencePlayer->OnFinished.AddUniqueDynamic(this, &UActorSequenceComponent::OnStopOrFinsh);
		SequencePlayer->OnStop.AddUniqueDynamic(this, &UActorSequenceComponent::OnStopOrFinsh);
	}
}

void UActorSequenceComponent::OnRep_RepBindingData()
{
	InitPlayer();
	InitBinding();
	if (!BindingOverrides)
	{
		return;
	}

	for (auto& Var : PreRepBindingData)
	{
		if (RepBindingData.Contains(Var))
		{
			continue;
		}

		ReMapObject = Var.Object.Get();
		if (RemapDelegate.IsBound())
		{

			RemapDelegate.Broadcast();
		}
		BindingOverrides->RemoveBinding(Var.ObjectBindingId, ReMapObject);

		if (SequencePlayer)
		{
			SequencePlayer->State.Invalidate(Var.ObjectBindingId.GetGuid(), Var.ObjectBindingId.GetRelativeSequenceID());
		}
	}

	for (auto& Var : RepBindingData)
	{
		if (PreRepBindingData.Contains(Var))
		{
			continue;
		}

		ReMapObject = Var.Object.Get();
		if (RemapDelegate.IsBound())
		{

			RemapDelegate.Broadcast();
		}
		BindingOverrides->AddBinding(Var.ObjectBindingId, ReMapObject);

		if (SequencePlayer)
		{
			SequencePlayer->State.Invalidate(Var.ObjectBindingId.GetGuid(), Var.ObjectBindingId.GetRelativeSequenceID());
		}
	}

	ReMapObject = nullptr;
	PreRepBindingData = RepBindingData;
}

void UActorSequenceComponent::OnStopOrFinsh()
{
	SetComponentTickEnabled(false);
}

void UActorSequenceComponent::GetLifetimeReplicatedProps(TArray<FLifetimeProperty>& OutLifetimeProps) const
{
	Super::GetLifetimeReplicatedProps(OutLifetimeProps);

	DOREPLIFETIME(UActorSequenceComponent, RepBindingData);
}

// @SStudio leiizhao merge - END 补充CG添加接口

// @SStudio chrisshi - BEGIN
void UActorSequenceComponent::TickSequence(float DeltaTime)
{
	if (SequencePlayer)
	{
		SequencePlayer->Update(DeltaTime);
	}
}

void UActorSequenceComponent::AddBindingServer(FMovieSceneObjectBindingID Binding, UObject* Object, bool bAllowBindingsFromAsset /*= false*/)
{
	if (!bReplicates)
	{
		SetIsReplicated(true);
	}

	if (Object)
	{
		FMovieSceneBindingOverrideData NewBinding;
		NewBinding.ObjectBindingId = Binding;
		NewBinding.Object = Object;
		RepBindingData.Add(NewBinding);
	}
}

void UActorSequenceComponent::RemoveBindingServer(FMovieSceneObjectBindingID Binding, UObject* Object)
{
	int32 NumRemoved = RepBindingData.RemoveAll([=](const FMovieSceneBindingOverrideData& InBindingData) {
		return InBindingData.Object == Object && Binding == InBindingData.ObjectBindingId;
		});
}

void UActorSequenceComponent::ResetBindingServer(FMovieSceneObjectBindingID Binding)
{
	int32 NumRemoved = RepBindingData.RemoveAll([=](const FMovieSceneBindingOverrideData& InBindingData) {
		return Binding == InBindingData.ObjectBindingId;
		});
}

void UActorSequenceComponent::ResetBindingsServer()
{
	RepBindingData.Reset();
}
// @SStudio chrisshi - END
