#pragma once

#include "CoreMinimal.h"
#include "UObject/ObjectMacros.h"
#include "GameFramework/Actor.h"
#include "FracturedTypeDef.h"
#include "FracturedMeshActorBase.h"
#include "FracturedSkeletalMeshActor.generated.h"

class UFracturedSkinnedMeshComponent;

USTRUCT()
struct FFSkeletalMeshNetData
{
	GENERATED_USTRUCT_BODY()

	UPROPERTY()
	TArray<float>								FragmentsHP;

	UPROPERTY()
	int32										ImpactFragmentIndex;

	UPROPERTY()
	FVector										ImpactWorldPos;

	UPROPERTY()
	FVector										ImpactWorldDir;

	UPROPERTY()
	float										ImpulseForce;

	UPROPERTY()
	float										ImpactTime;

	FFSkeletalMeshNetData()
		: ImpactFragmentIndex(-1)
		, ImpactWorldPos()
		, ImpactWorldDir()
		, ImpulseForce(0.f)
		, ImpactTime(0.f)
	{
	}
};

UCLASS(hidecategories = (Input), showcategories = ("Input|MouseInput", "Input|TouchInput"), ConversionRoot, ComponentWrapperClass, meta = (ChildCanTick))
class PHOTONDESTRUCTIBLE_API AFracturedSkeletalMeshActor : public AActor, public IFracturedMeshActorBase
{
	GENERATED_UCLASS_BODY()

public:
	UPROPERTY(Category = FracturedMesh, VisibleAnywhere, BlueprintReadOnly, meta = (ExposeFunctionCategories = "Mesh,Rendering,Physics,Components|FracturedSkeletalMesh", AllowPrivateAccess = "true"))
	class UFracturedSkinnedMeshComponent*		FracturedSkinnedMeshComponent;

	UPROPERTY(Category = FracturedMesh, EditAnywhere)
	float										SingleFragmentHP;

	UPROPERTY(Category = FracturedMesh, EditAnywhere, meta = (ToolTip = "ennable for internal impact operation, otherwise you should trigger Client_OnImpact by yourself"))
	uint32										EnableImpactDamage : 1;

	UPROPERTY(Category = FracturedMesh, EditAnywhere, meta = (ToolTip = "for internal impact operation, used only if EnableImpactDamage is ON"))
	float										ImpactDamageValue;

	UPROPERTY(Category = FracturedMesh, EditAnywhere)
	float										ImpactImpulseForce;

	UPROPERTY(ReplicatedUsing = OnRep_FSMNetData)
	FFSkeletalMeshNetData						FSMNetData;

public:
	virtual ~AFracturedSkeletalMeshActor();

	UFracturedSkinnedMeshComponent*				GetFracturedComponent() const { return FracturedSkinnedMeshComponent; }
	virtual void								PostInitializeComponents() override;
	virtual void								GetLifetimeReplicatedProps(TArray<FLifetimeProperty>& OutLifetimeProps) const override;

	UFUNCTION()
	void										OnRep_FSMNetData();

	/* Server */
	UFUNCTION()
	void										Server_OnActorHitAction(AActor* SelfActor, AActor* OtherActor, FVector NormalImpulse, const FHitResult& Hit);

	/* Client */
	UFUNCTION(NetMulticast, Reliable)
	void										Client_OnImpact(int32 FragmentIndex, const FVector& ImpactWorldPos, const FVector& ImpactWorldDir, float ImpulseForce, float InFirstImpactTime);
};
