#pragma once

#include "CoreMinimal.h"
#include "PhotonDestructibleSurfaceActorBase.h"
#include "PhotonDestructibleSurfaceActor.generated.h"

UCLASS(ClassGroup = (PhotonDestructible), meta = (BlueprintSpawnableComponent))
class PHOTONDESTRUCTIBLE_API APhotonDestructibleSurfaceActor : public AActor, public IPhotonDestructibleSurfaceActorBase
{
	GENERATED_UCLASS_BODY()
public:
	UPROPERTY(Category = PhotonDestructible, VisibleAnywhere, BlueprintReadOnly, meta = (AllowPrivateAccess = "true"))
	UPhotonDestructibleSurfaceComponent*			SurfaceComponent;

public:
	virtual UPhotonDestructibleSurfaceComponent*			GetSurfaceComponent() const override { return SurfaceComponent; }
};

UCLASS(ClassGroup = (PhotonDestructible), meta = (BlueprintSpawnableComponent))
class PHOTONDESTRUCTIBLE_API APhotonDestructibleInstancedSurfaceActor : public AActor, public IPhotonDestructibleInstancedSurfaceActorBase
{
	GENERATED_UCLASS_BODY()
public:
	UPROPERTY(Category = PhotonDestructible, VisibleAnywhere, BlueprintReadOnly, meta = (AllowPrivateAccess = "true"))
	UPhotonDestructibleInstancedSurfaceComponent*	InstancedSurfaceComponent;

public:
	virtual UPhotonDestructibleInstancedSurfaceComponent*	GetInstancedSurfaceComponent() const override { return InstancedSurfaceComponent; }
};
