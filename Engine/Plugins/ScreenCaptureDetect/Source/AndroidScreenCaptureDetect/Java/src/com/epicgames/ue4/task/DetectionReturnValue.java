
package com.epicgames.ue4.task;

import android.graphics.Bitmap;


public class DetectionReturnValue {

    private Bitmap watermarkBitmap;
    private String watermarkString;

    public DetectionReturnValue() {

    }

    public DetectionReturnValue(Bitmap watermarkBitmap, String watermarkString) {
        this.watermarkBitmap = watermarkBitmap;
        this.watermarkString = watermarkString;
    }

    public Bitmap getWatermarkBitmap() {
        return watermarkBitmap;
    }

    protected void setWatermarkBitmap(Bitmap watermarkBitmap) {
        this.watermarkBitmap = watermarkBitmap;
    }

    public String getWatermarkString() {
        return watermarkString;
    }

    protected void setWatermarkString(String watermarkString) {
        this.watermarkString = watermarkString;
    }
}
