// Copyright Epic Games, Inc. All Rights Reserved.

#pragma once

#include "LumenSceneLighting.ush"
#include "../EyeAdaptationCommon.ush"
#include "/Engine/Shared/LightDefinitions.h"

RWStructuredBuffer<uint> RWShadowMaskTiles;
StructuredBuffer<uint> ShadowMaskTiles;

struct FShadowTrace
{
	uint LightTileIndex;
	uint2 LightTileCoord;
};

FShadowTrace UnpackShadowTrace(uint PackedValue)
{
	FShadowTrace ShadowTrace;
	ShadowTrace.LightTileIndex = PackedValue & 0xFFFFFF;
	ShadowTrace.LightTileCoord.x = (PackedValue >> 24) & 0xF;
	ShadowTrace.LightTileCoord.y = (PackedValue >> 28) & 0xF;
	return ShadowTrace;
}

uint PackShadowTrace(FShadowTrace ShadowTrace)
{
	uint PackedValue = 0;
	PackedValue = ShadowTrace.LightTileIndex & 0xFFFFFF;
	PackedValue |= (ShadowTrace.LightTileCoord.x & 0xF) << 24;
	PackedValue |= (ShadowTrace.LightTileCoord.y & 0xF) << 28;
	return PackedValue;
}

#define SHADOW_MASK_RAY_BITS 8
#define SHADOW_MASK_RAY_BITS_MASK ((1u << SHADOW_MASK_RAY_BITS) - 1)
#define SHADOW_MASK_CARD_TILE_DWORDS (SHADOW_MASK_RAY_BITS * CARD_TILE_SIZE * CARD_TILE_SIZE / 32)
#define SHADOW_FACTOR_BITS 7
#define SHADOW_FACTOR_BITS_MASK ((1u << SHADOW_FACTOR_BITS) - 1)
#define SHADOW_FACTOR_COMPLETE_BITS 1
#define SHADOW_FACTOR_COMPLETE_BITS_MASK (((1u << SHADOW_FACTOR_COMPLETE_BITS) - 1) << SHADOW_FACTOR_BITS)

struct FShadowMaskRay
{
	float ShadowFactor;
	bool bShadowFactorComplete;
};

void ReadShadowMaskRayRW(uint CardTileIndex, uint2 CoordInCardTile, inout FShadowMaskRay ShadowMaskRay)
{
	uint BitOffset = SHADOW_MASK_RAY_BITS * (CoordInCardTile.x + CoordInCardTile.y * CARD_TILE_SIZE);

	uint ShadowMask = RWShadowMaskTiles[SHADOW_MASK_CARD_TILE_DWORDS * CardTileIndex + BitOffset / 32];
	ShadowMask = ShadowMask >> (BitOffset % 32);

	ShadowMaskRay.ShadowFactor = float(ShadowMask & SHADOW_FACTOR_BITS_MASK) / SHADOW_FACTOR_BITS_MASK;
	ShadowMaskRay.bShadowFactorComplete = (ShadowMask & SHADOW_FACTOR_COMPLETE_BITS_MASK) != 0;
}

void ReadShadowMaskRay(uint CardTileIndex, uint2 CoordInCardTile, inout FShadowMaskRay ShadowMaskRay)
{
	uint BitOffset = SHADOW_MASK_RAY_BITS * (CoordInCardTile.x + CoordInCardTile.y * CARD_TILE_SIZE);

	uint ShadowMask = ShadowMaskTiles[SHADOW_MASK_CARD_TILE_DWORDS * CardTileIndex + BitOffset / 32];
	ShadowMask = ShadowMask >> (BitOffset % 32);

	ShadowMaskRay.ShadowFactor = float(ShadowMask & SHADOW_FACTOR_BITS_MASK) / SHADOW_FACTOR_BITS_MASK;
	ShadowMaskRay.bShadowFactorComplete = (ShadowMask & SHADOW_FACTOR_COMPLETE_BITS_MASK) != 0;
}

void WriteShadowMaskRay(FShadowMaskRay Ray, uint CardTileIndex, uint2 CoordInCardTile, const bool bClearExistingMask)
{
	uint Mask = uint(Ray.ShadowFactor * SHADOW_FACTOR_BITS_MASK);

	if (Ray.bShadowFactorComplete)
	{
		Mask |= SHADOW_FACTOR_COMPLETE_BITS_MASK;
	}

	uint BitOffset = SHADOW_MASK_RAY_BITS * (CoordInCardTile.x + CoordInCardTile.y * CARD_TILE_SIZE);

	if (bClearExistingMask)
	{
		InterlockedAnd(RWShadowMaskTiles[SHADOW_MASK_CARD_TILE_DWORDS * CardTileIndex + BitOffset / 32], ~(SHADOW_MASK_RAY_BITS_MASK << (BitOffset % 32)));
	}

	if (Mask != 0)
	{
		InterlockedOr(RWShadowMaskTiles[SHADOW_MASK_CARD_TILE_DWORDS * CardTileIndex + BitOffset / 32], Mask << (BitOffset % 32));
	}
}

// Must match FLumenPackedLight in LumenSceneDirectLighting.cpp
struct FLumenPackedLight
{
	float3 WorldPosition; // LUMEN_LWC_TODO
	float InvRadius;

	float3 Color;
	float FalloffExponent;

	float3 Direction;
	float SpecularScale;

	float3 Tangent;
	float SourceRadius;

	float2 SpotAngles;
	float SoftSourceRadius;
	float SourceLength;

	float RectLightBarnCosAngle;
	float RectLightBarnLength;
	uint LightType;
	uint VirtualShadowMapId;

	float4 InfluenceSphere;

	float3 ProxyPosition;
	float ProxyRadius;

	float3 ProxyDirection;
	float RectLightAtlasMaxLevel;
	
	float2 SinCosConeAngleOrRectLightAtlasUVScale;
	float2 RectLightAtlasUVOffset;

	uint LightingChannelMask;
	uint LightFunctionAtlasIndex_bHasShadowMask;
	float IESAtlasIndex;
	float InverseExposureBlend;
};

struct FLumenLight
{
	FDeferredLightData DeferredLightData;

	float4 InfluenceSphere;
	float3 ProxyPosition;
	float3 ProxyDirection;
	float ProxyRadius;
	float CosConeAngle;
	float SinConeAngle;

	uint VirtualShadowMapId;
	uint LightingChannelMask;
	uint bHasShadowMask;
	uint Type;
};

StructuredBuffer<FLumenPackedLight> LumenPackedLights;

FLumenLight LoadLumenLight(uint LightIndex, float3 PreViewTranslation, float Exposure)
{
	FLumenPackedLight PackedLight = LumenPackedLights[LightIndex];

	FDeferredLightData DeferredLightData = (FDeferredLightData)0;
	DeferredLightData.TranslatedWorldPosition = PackedLight.WorldPosition + PreViewTranslation;
	DeferredLightData.InvRadius = PackedLight.InvRadius;
	DeferredLightData.Color = PackedLight.Color * InverseExposureLerp(Exposure, PackedLight.InverseExposureBlend);
	DeferredLightData.FalloffExponent = PackedLight.FalloffExponent;
	DeferredLightData.Direction = PackedLight.Direction;
	DeferredLightData.Tangent = PackedLight.Tangent;
	DeferredLightData.SpotAngles = PackedLight.SpotAngles;
	DeferredLightData.SourceRadius = PackedLight.SourceRadius;
	DeferredLightData.SourceLength = PackedLight.SourceLength;
	DeferredLightData.SoftSourceRadius = PackedLight.SoftSourceRadius;
	DeferredLightData.SpecularScale = PackedLight.SpecularScale;
	DeferredLightData.ContactShadowLength = 0.0f;
	DeferredLightData.ContactShadowLengthInWS = false;
	DeferredLightData.DistanceFadeMAD = 0.0f;
	DeferredLightData.ShadowMapChannelMask = 0.0f;
	DeferredLightData.ShadowedBits = 0;
	DeferredLightData.RectLightData.BarnCosAngle = PackedLight.RectLightBarnCosAngle;
	DeferredLightData.RectLightData.BarnLength = PackedLight.RectLightBarnLength;
	DeferredLightData.RectLightData.AtlasData.AtlasUVScale = PackedLight.SinCosConeAngleOrRectLightAtlasUVScale;
	DeferredLightData.RectLightData.AtlasData.AtlasUVOffset = PackedLight.RectLightAtlasUVOffset;
	DeferredLightData.RectLightData.AtlasData.AtlasMaxLevel = PackedLight.RectLightAtlasMaxLevel;
	DeferredLightData.bInverseSquared = PackedLight.FalloffExponent == 0.0f;
	DeferredLightData.bRadialLight = PackedLight.LightType != LIGHT_TYPE_DIRECTIONAL;
	DeferredLightData.bSpotLight = PackedLight.LightType == LIGHT_TYPE_SPOT;
	DeferredLightData.bRectLight = PackedLight.LightType == LIGHT_TYPE_RECT;
	DeferredLightData.IESAtlasIndex = PackedLight.IESAtlasIndex;
	DeferredLightData.LightFunctionAtlasLightIndex = PackedLight.LightFunctionAtlasIndex_bHasShadowMask & 0x7FFFFFFF;

	FLumenLight LumenLight = (FLumenLight)0;
	LumenLight.DeferredLightData = DeferredLightData;
	LumenLight.InfluenceSphere = PackedLight.InfluenceSphere;
	LumenLight.ProxyPosition = PackedLight.ProxyPosition;
	LumenLight.ProxyRadius = PackedLight.ProxyRadius;
	LumenLight.ProxyDirection = PackedLight.ProxyDirection;
	LumenLight.CosConeAngle = PackedLight.SinCosConeAngleOrRectLightAtlasUVScale.y;
	LumenLight.SinConeAngle = PackedLight.SinCosConeAngleOrRectLightAtlasUVScale.x;
	LumenLight.VirtualShadowMapId = PackedLight.VirtualShadowMapId;
	LumenLight.bHasShadowMask = PackedLight.LightFunctionAtlasIndex_bHasShadowMask >> 31;
	LumenLight.LightingChannelMask = PackedLight.LightingChannelMask;
	LumenLight.Type = PackedLight.LightType;
	return LumenLight;
}

#if LIGHT_FUNCTION
/** Fade distance in x, disabled brightness in y, output for preview shadows mask in z. */
float3 LightFunctionParameters2;

float GetLumenLightFunction(float3 TranslatedWorldPosition) 
{
	float4 LightVector = mul(float4(TranslatedWorldPosition, 1), LightFunctionTranslatedWorldToLight);
	float3 LightFunction = GetLightFunctionColor(LightVector.xyz / LightVector.w, TranslatedWorldPosition);

	float GreyScale = dot(LightFunction, .3333f).x;

	// Calculate radial view distance for stable fading
	float ViewDistance = GetDistanceToCameraFromViewVector(PrimaryView.TranslatedWorldCameraOrigin - TranslatedWorldPosition);

	float DistanceFadeAlpha = saturate((LightFunctionParameters2.x - ViewDistance) / (LightFunctionParameters2.x * .2f));
	// Fade to disabled based on LightFunctionFadeDistance
	GreyScale = lerp(LightFunctionParameters2.y, GreyScale, DistanceFadeAlpha);

	// Fade to disabled based on ShadowFadeFraction
	GreyScale = lerp(LightFunctionParameters2.y, GreyScale, LightFunctionParameters.y);

	// We want to get the same light function contribution as if it was written to a unorm8 render target so we saturate. This will also avoid amplifying other shadow factors.
	return saturate(GreyScale);
}
#endif
