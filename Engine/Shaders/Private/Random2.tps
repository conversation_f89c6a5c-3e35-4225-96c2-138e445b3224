<?xml version="1.0" encoding="utf-8"?>
<TpsData xmlns:xsd="http://www.w3.org/2001/XMLSchema" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance">
  <Name>GPU Random Numbers via the Tiny Encryption Algorithm and perlin noise code 01-05 </Name>
  <Location>/Engine/Shaders/Private/Random.usf</Location>
  <Date>2016-06-09T18:27:34.0825813-04:00</Date>
  <Function>Function that computes pseudo random numbers with only math few instructions.</Function>
  <Justification>Very useful for shader programming, all kind of procedural textures, even without UV mapping, can be created with no memory cost</Justification>
  <Eula>None available, see http://citeseerx.ist.psu.edu/viewdoc/download?doi=10.1.1.447.8057&rep=rep1&type=pdf</Eula>
  <RedistributeTo>
    <EndUserGroup>Licensees</EndUserGroup>
    <EndUserGroup>Git</EndUserGroup>
    <EndUserGroup>P4</EndUserGroup>
  </RedistributeTo>
  <LicenseFolder>None</LicenseFolder>
</TpsData>