// Copyright 1998-2017 Epic Games, Inc. All Rights Reserved.
// @SStudio dreamwzhang - 适配template
#pragma once

#include "CoreMinimal.h"
#include "UObject/ObjectMacros.h"
#include "Curves/KeyHandle.h"
#include "MovieSceneSection.h"
#include "Animation/AnimSequenceBase.h"
#include "MovieSceneSkeletalAnimationSection.generated.h"

USTRUCT(BlueprintType)
struct MOVIESCENETRACKS_API FMovieSceneSkeletalAnimationParams
{
	GENERATED_BODY()

	FMovieSceneSkeletalAnimationParams();

	/** Gets the animation duration, modified by play rate */
	float GetDuration() const { return FMath::IsNearlyZero(PlayRate) || Animation == nullptr ? 0.f : Animation->SequenceLength / PlayRate; }

	/** Gets the animation sequence length, not modified by play rate */
	float GetSequenceLength() const { return Animation != nullptr ? Animation->SequenceLength : 0.f; }

	/**
	 * Convert a sequence frame to a time in seconds inside the animation clip, taking into account start/end offsets,
	 * looping, etc.
	 */
	double MapTimeToAnimation(const UMovieSceneSection* InSection, FFrameTime InPosition, FFrameRate InFrameRate) const;
	
	/**
	 * As above, but with already computed section bounds.
	 */
	// @SStudio dreamwzhang - BEGIN 增加SubFrame参数
	double MapTimeToAnimation(FFrameNumber InSectionStartTime, FFrameNumber InSectionEndTime,
		float InSectionStartSubFrame, float InSectionEndSubFrame, FFrameTime InPosition, FFrameRate InFrameRate) const;
	// @SStudio dreamwzhang - END
	
	/** The animation this section plays */
	UPROPERTY(EditAnywhere, Category="Animation", meta=(AllowedClasses = "AnimSequence, AnimComposite"))
	TObjectPtr<UAnimSequenceBase> Animation;
	
	/** The offset into the beginning of the animation clip for the first loop of play. */
	UPROPERTY(BlueprintReadWrite, EditAnywhere, Category="Animation")
	FFrameNumber FirstLoopStartFrameOffset;
	
	/** The offset into the beginning of the animation clip */
	UPROPERTY(EditAnywhere, Category="Animation")
	FFrameNumber StartFrameOffset;

	/** The offset into the end of the animation clip */
	UPROPERTY(EditAnywhere, Category="Animation")
	FFrameNumber EndFrameOffset;

	/** The playback rate of the animation clip */
	UPROPERTY(EditAnywhere, Category="Animation")
	float PlayRate;

	/** Reverse the playback of the animation clip */
	UPROPERTY(EditAnywhere, Category="Animation")
	uint32 bReverse:1;

	/** The slot name to use for the animation */
	UPROPERTY( EditAnywhere, Category = "Animation" )
	FName SlotName;

	/** The weight curve for this animation section */
	UPROPERTY( EditAnywhere, Category = "Animation" )
	FRichCurve Weight;

	/** BlendOutTimeWhenStop */
	UPROPERTY(EditAnywhere, Category = "Animation")
	float BlendOutTime;

	/** clear the cached pose */
	UPROPERTY(EditAnywhere, Category = "Animation")
	uint32 bClearPose : 1;

	/** Apply Anim To SubAnim */
	UPROPERTY(EditAnywhere, Category = "Animation")
	uint32 bApplySubAnim : 1;

	/** Apply Anim To Avatar */
	UPROPERTY(EditAnywhere, Category = "Animation")
	TArray<int32> ApplyAvatarSlot;

	/** Apply Anim To SubAnim */
	UPROPERTY(EditAnywhere, Category = "Animation")
	TArray<int32> DisableBoneResolve;
	
	UPROPERTY()
	float StartOffset_DEPRECATED;

	UPROPERTY()
	float EndOffset_DEPRECATED;
};

/**
 * Movie scene section that control skeletal animation
 */
UCLASS()
class MOVIESCENETRACKS_API UMovieSceneSkeletalAnimationSection
	: public UMovieSceneSection
{
	GENERATED_UCLASS_BODY()

public:

	UPROPERTY(EditAnywhere, Category = "Animation", meta=(ShowOnlyInnerProperties))
	FMovieSceneSkeletalAnimationParams Params;

public:

	/** Get Frame Time as Animation Time*/
	double MapTimeToAnimation(FFrameTime InPosition, FFrameRate InFrameRate) const;
	double MapTimeToAnimation(FFrameTime InPosition) const;
	//~ MovieSceneSection interface
	virtual TOptional<TRange<FFrameNumber> > GetAutoSizeRange() const override;
	virtual void TrimSection(FQualifiedFrameTime TrimTime, bool bTrimLeft, bool bDeleteKeys) override;
	virtual void MoveSection( float DeltaPosition, TSet<FKeyHandle>& KeyHandles ) override;
	virtual void DilateSection( float DilationFactor, float Origin, TSet<FKeyHandle>& KeyHandles  ) override;
	virtual UMovieSceneSection* SplitSection(FQualifiedFrameTime SplitTime, bool bDeleteKeys) override;
	virtual void GetKeyHandles(TSet<FKeyHandle>& OutKeyHandles, TRange<float> TimeRange) const override;
	virtual void GetSnapTimes(TArray<FFrameNumber>& OutSnapTimes, bool bGetSectionBorders) const override;
	virtual TOptional<FFrameTime> GetOffsetTime() const override;
	virtual void MigrateFrameTimes(FFrameRate SourceRate, FFrameRate DestinationRate) override;
	virtual TOptional<float> GetKeyTime( FKeyHandle KeyHandle ) const override { return TOptional<float>(); }
	virtual void SetKeyTime( FKeyHandle KeyHandle, float Time ) override { }
	virtual FMovieSceneEvalTemplatePtr GenerateTemplate() const override;

	/** ~UObject interface */
	virtual void PostLoad() override;
	virtual void Serialize(FArchive& Ar) override;
private:

	//~ UObject interface

#if WITH_EDITOR

	virtual void PreEditChange(FProperty* PropertyAboutToChange) override;
	virtual void PostEditChangeProperty(FPropertyChangedEvent& PropertyChangedEvent) override;
public:
	float PreviousPlayRate;
#endif

private:

	UPROPERTY()
	TObjectPtr<class UAnimSequence> AnimSequence_DEPRECATED;

	UPROPERTY()
	TObjectPtr<UAnimSequenceBase> Animation_DEPRECATED;

	UPROPERTY()
	float StartOffset_DEPRECATED;

	UPROPERTY()
	float EndOffset_DEPRECATED;

	UPROPERTY()
	float PlayRate_DEPRECATED;

	UPROPERTY()
	uint32 bReverse_DEPRECATED:1;

	UPROPERTY()
	uint32 bClearPose_DEPRECATED : 1;

	UPROPERTY()
	FName SlotName_DEPRECATED;
};
