// Copyright 1998-2017 Epic Games, Inc. All Rights Reserved.
// @SStudio dreamwzhang - 适配template
#include "Sections/MovieSceneSkeletalAnimationSection.h"
#include "Animation/AnimSequence.h"
#include "Evaluation/MovieSceneSkeletalAnimationTemplate.h"
#include "MovieScene.h"

#define LOCTEXT_NAMESPACE "MovieSceneSkeletalAnimationSection"

namespace
{
	FName DefaultSlotName( "DefaultSlot" );
	float SkeletalDeprecatedMagicNumber = TNumericLimits<float>::Lowest();
}

FMovieSceneSkeletalAnimationParams::FMovieSceneSkeletalAnimationParams()
{
	Animation = nullptr;
	StartOffset_DEPRECATED = SkeletalDeprecatedMagicNumber;
	EndOffset_DEPRECATED = SkeletalDeprecatedMagicNumber;
	PlayRate = 1.f;
	bReverse = false;
	SlotName = DefaultSlotName;
	Weight.SetDefaultValue(1.f);
	BlendOutTime = 0;
	bClearPose = true;
	bApplySubAnim = false;
	DisableBoneResolve = {};
	ApplyAvatarSlot = {};
}


double FMovieSceneSkeletalAnimationParams::MapTimeToAnimation(const UMovieSceneSection* InSection, FFrameTime InPosition, FFrameRate InFrameRate) const
{
	const FFrameNumber SectionStartTime = InSection->GetInclusiveStartFrame();
	const FFrameNumber SectionEndTime = InSection->GetExclusiveEndFrame();
	// @SStudio dreamwzhang - BEGIN 
	const float SectionStartSubFrame = InSection->SectionRange.GetLowerSubFrame().GetValue();
	const float SectionEndSubFrame = InSection->SectionRange.GetUpperSubFrame().GetValue();
	return MapTimeToAnimation(SectionStartTime, SectionEndTime,SectionStartSubFrame, SectionEndSubFrame, InPosition, InFrameRate);
	// @SStudio dreamwzhang - END
}

double FMovieSceneSkeletalAnimationParams::MapTimeToAnimation(FFrameNumber InSectionStartTime, FFrameNumber InSectionEndTime,float InSectionStartSubFrame, float InSectionEndSubFrame, FFrameTime InPosition, FFrameRate InFrameRate) const
{
	// Get Animation Length and frame time
	if (Animation)
	{
		const FFrameTime AnimationLength = GetSequenceLength() * InFrameRate;
		const int32 LengthInFrames = AnimationLength.FrameNumber.Value + (int)(AnimationLength.GetSubFrame() + 0.5f) + 1;
		// @SStudio dreamwzhang - BEGIN 动画section里多段动画时，StartFrame非0情况下，当美术配置动画的StartFrame在上一段动画结束之前，会出现Fmod计算错误，例如回到起始帧
		// 需要用 FrameTime 计算，避免丢失 SubFrame 导致误判断为需要循环，Fmod计算后不在最后一帧
		FFrameTime InSectionStartFrame = InSectionStartSubFrame > 0.f?  FFrameTime(InSectionStartTime,InSectionStartSubFrame) : FFrameTime(InSectionStartTime);
		FFrameTime InSectionEndFrame = InSectionEndSubFrame > 0.f?  FFrameTime(InSectionEndTime-1,InSectionEndSubFrame) : FFrameTime(InSectionEndTime);
		// Make sure InPosition FrameTime doesn't underflow InSectionStartTime or overflow InSectionEndTime
		InPosition = FMath::Clamp(InPosition, InSectionStartFrame,InSectionEndFrame);
		// @SStudio dreamwzhang - END

		// Gather helper values
		// @SStudio dreamwzhang - BEGIN 忽略动画的playrate
		//const float SectionPlayRate = PlayRate * Animation->RateScale;
		const float SectionPlayRate = PlayRate;
		// @SStudio dreamwzhang - END
		const float AnimPlayRate = FMath::IsNearlyZero(SectionPlayRate) ? 1.0f : SectionPlayRate;
		const float SeqLength = GetSequenceLength() - InFrameRate.AsSeconds(StartFrameOffset + EndFrameOffset);
		// @SStudio dreamwzhang - BEGIN 动画section里多段动画时，StartFrame非0情况下，当美术配置动画的StartFrame在上一段动画结束之前，会出现Fmod计算错误，例如回到起始帧
		// 需要用 FrameTime 计算，避免丢失 SubFrame 导致误判断为需要循环，Fmod计算后不在最后一帧
		// we only play end if we are not looping, and assuming we are looping if Length is greater than default length;
		const bool bLooping = (InSectionEndFrame - InSectionStartFrame + StartFrameOffset + EndFrameOffset) > LengthInFrames;
		// @SStudio dreamwzhang - END
		// The Time from the beginning of InSectionStartTime to InPosition in seconds
		float SecondsFromSectionStart = FFrameTime::FromDecimal((InPosition - InSectionStartFrame).AsDecimal() * AnimPlayRate) / InFrameRate;

		// Logic for reversed animation
		if (bReverse)
		{
			// Duration of this section 
			float SectionDuration = (((InSectionEndFrame - InSectionStartFrame) * AnimPlayRate) / InFrameRate);
			SecondsFromSectionStart = SectionDuration - SecondsFromSectionStart;
		}

		SecondsFromSectionStart += InFrameRate.AsSeconds(FirstLoopStartFrameOffset);

		// Make sure Seconds is in range
		if (SeqLength > 0.0 && (bLooping || !FMath::IsNearlyEqual(SecondsFromSectionStart, SeqLength, 1e-4)))
		{
			//double SecondsBeforeMod = SecondsFromSectionStart;
			SecondsFromSectionStart = FMath::Fmod(SecondsFromSectionStart, SeqLength);
			//UE_LOG(LogMovieScene, Warning, TEXT("MapTimeToAnimation AnimationName :%s, SecondsBeforeMod %f , SecondsAfterMod %f"),
			//		*this->Animation.GetFName().ToString(),SecondsBeforeMod, SecondsFromSectionStart);
		}

		// Add the StartFrameOffset to the current seconds in the section to get the right animation frame
		SecondsFromSectionStart += InFrameRate.AsSeconds(StartFrameOffset);
		return SecondsFromSectionStart;
	}
	return 0.0;
}

UMovieSceneSkeletalAnimationSection::UMovieSceneSkeletalAnimationSection( const FObjectInitializer& ObjectInitializer )
	: Super( ObjectInitializer )
{
	AnimSequence_DEPRECATED = nullptr;
	Animation_DEPRECATED = nullptr;
	StartOffset_DEPRECATED = 0.f;
	EndOffset_DEPRECATED = 0.f;
	PlayRate_DEPRECATED = 1.f;
	bReverse_DEPRECATED = false;
	bClearPose_DEPRECATED = true;
	SlotName_DEPRECATED = DefaultSlotName;
	BlendType = EMovieSceneBlendType::Absolute;
	EvalOptions.EnableAndSetCompletionMode(EMovieSceneCompletionMode::RestoreState);

#if WITH_EDITOR
	PreviousPlayRate = Params.PlayRate;
#endif
}

void UMovieSceneSkeletalAnimationSection::Serialize(FArchive& Ar)
{
	Ar.UsingCustomVersion(FSequencerObjectVersion::GUID);
	Super::Serialize(Ar);
}

void UMovieSceneSkeletalAnimationSection::PostLoad()
{
	if (AnimSequence_DEPRECATED)
	{
		Params.Animation = AnimSequence_DEPRECATED;
	}

	if (Animation_DEPRECATED != nullptr)
	{
		Params.Animation = Animation_DEPRECATED;
	}

	if (StartOffset_DEPRECATED != 0.f)
	{
		Params.StartOffset_DEPRECATED = StartOffset_DEPRECATED;
	}

	if (EndOffset_DEPRECATED != 0.f)
	{
		Params.EndOffset_DEPRECATED = EndOffset_DEPRECATED;
	}

	if (PlayRate_DEPRECATED != 1.f)
	{
		Params.PlayRate = PlayRate_DEPRECATED;
	}

	if (bReverse_DEPRECATED != false)
	{
		Params.bReverse = bReverse_DEPRECATED;
	}

	if (SlotName_DEPRECATED != DefaultSlotName)
	{
		Params.SlotName = SlotName_DEPRECATED;
	}

	if (bClearPose_DEPRECATED != true)
	{
		Params.bClearPose = bClearPose_DEPRECATED;
	}
	
	UMovieScene* MovieScene = GetTypedOuter<UMovieScene>();

	if (MovieScene)
	{
		FFrameRate DisplayRate = MovieScene->GetDisplayRate();
		FFrameRate TickResolution = MovieScene->GetTickResolution();

		if (Params.StartOffset_DEPRECATED != SkeletalDeprecatedMagicNumber)
		{
			Params.StartFrameOffset = ConvertFrameTime(FFrameTime::FromDecimal(DisplayRate.AsDecimal() * Params.StartOffset_DEPRECATED), DisplayRate, TickResolution).FrameNumber;

			Params.StartOffset_DEPRECATED = SkeletalDeprecatedMagicNumber;
		}

		if (Params.EndOffset_DEPRECATED != SkeletalDeprecatedMagicNumber)
		{
			Params.EndFrameOffset = ConvertFrameTime(FFrameTime::FromDecimal(DisplayRate.AsDecimal() * Params.EndOffset_DEPRECATED), DisplayRate, TickResolution).FrameNumber;

			Params.EndOffset_DEPRECATED = SkeletalDeprecatedMagicNumber;
		}
	}

	// if version is less than this
	if (GetLinkerCustomVersion(FSequencerObjectVersion::GUID) < FSequencerObjectVersion::ConvertEnableRootMotionToForceRootLock)
	{
		UAnimSequence* AnimSeq = Cast<UAnimSequence>(Params.Animation);
		if (AnimSeq && AnimSeq->bEnableRootMotion && !AnimSeq->bForceRootLock)
		{
			// this is not ideal, but previously single player node was using this flag to whether or not to extract root motion
			// with new anim sequencer instance, this would break because we use the instance flag to extract root motion or not
			// so instead of setting that flag, we use bForceRootLock flag to asset
			// this can have side effect, where users didn't want that to be on to start with
			// So we'll notify users to let them know this has to be saved
			AnimSeq->bForceRootLock = true;
			AnimSeq->MarkPackageDirty();
			// warning to users
#if WITH_EDITOR			
			if (!IsRunningGame())
			{
				static FName NAME_LoadErrors("LoadErrors");
				FMessageLog LoadErrors(NAME_LoadErrors);

				TSharedRef<FTokenizedMessage> Message = LoadErrors.Warning();
				Message->AddToken(FTextToken::Create(LOCTEXT("RootMotionFixUp1", "The Animation ")));
				Message->AddToken(FAssetNameToken::Create(AnimSeq->GetPathName(), FText::FromString(GetNameSafe(AnimSeq))));
				Message->AddToken(FTextToken::Create(LOCTEXT("RootMotionFixUp2", "will be set to ForceRootLock on. Please save the animation if you want to keep this change.")));
				Message->SetSeverity(EMessageSeverity::Warning);
				LoadErrors.Notify();
			}
#endif // WITH_EDITOR

			UE_LOG(LogMovieScene, Warning, TEXT("%s Animation has set ForceRootLock to be used in Sequencer. If this animation is used in anywhere else using root motion, that will cause conflict."), *AnimSeq->GetName());
		}
	}

	Super::PostLoad();
}

FMovieSceneEvalTemplatePtr UMovieSceneSkeletalAnimationSection::GenerateTemplate() const
{
	return FMovieSceneSkeletalAnimationSectionTemplate(*this);
}

double UMovieSceneSkeletalAnimationSection::MapTimeToAnimation(FFrameTime InPosition, FFrameRate InFrameRate) const
{
	return Params.MapTimeToAnimation(this, InPosition, InFrameRate);
}

double UMovieSceneSkeletalAnimationSection::MapTimeToAnimation(FFrameTime InPosition) const
{
	FFrameRate FrameRate = GetTypedOuter<UMovieScene>()->GetTickResolution();
	return Params.MapTimeToAnimation(this, InPosition, FrameRate);
}

TOptional<TRange<FFrameNumber> > UMovieSceneSkeletalAnimationSection::GetAutoSizeRange() const
{
	const FFrameRate FrameRate = GetTypedOuter<UMovieScene>()->GetTickResolution();
	const float AnimPlayRate = FMath::IsNearlyZero(Params.PlayRate) || Params.Animation == nullptr ? 1.0f : Params.PlayRate * Params.Animation->RateScale;

	const FFrameTime UnscaledAnimationLength = FMath::Max(Params.GetSequenceLength() * FrameRate - Params.StartFrameOffset - Params.EndFrameOffset, FFrameTime(1));
	const FFrameTime AnimationLength = UnscaledAnimationLength / AnimPlayRate;
	const int32 IFrameNumber = AnimationLength.FrameNumber.Value + (int)(AnimationLength.GetSubFrame() + 0.5f);

	return TRange<FFrameNumber>(GetInclusiveStartFrame(), GetInclusiveStartFrame() + IFrameNumber + 1);
}

void UMovieSceneSkeletalAnimationSection::TrimSection(FQualifiedFrameTime TrimTime, bool bTrimLeft, bool bDeleteKeys)
{
	SetFlags(RF_Transactional);

	if (TryModify())
	{
		Super::TrimSection(TrimTime, bTrimLeft, bDeleteKeys);
	}
}

void UMovieSceneSkeletalAnimationSection::MoveSection( float DeltaTime, TSet<FKeyHandle>& KeyHandles )
{
	Super::MoveSection(DeltaTime, KeyHandles);

	Params.Weight.ShiftCurve(DeltaTime, KeyHandles);
}


void UMovieSceneSkeletalAnimationSection::DilateSection( float DilationFactor, float Origin, TSet<FKeyHandle>& KeyHandles )
{
	Params.PlayRate /= DilationFactor;

	Super::DilateSection(DilationFactor, Origin, KeyHandles);

	Params.Weight.ScaleCurve(Origin, DilationFactor, KeyHandles);
}

//计算分割后新 Section 的动画起始偏移量（StartOffset），以保证动画播放的连续性
UMovieSceneSection* UMovieSceneSkeletalAnimationSection::SplitSection(FQualifiedFrameTime SplitTime, bool bDeleteKeys)
{
	//handle FirstLoopStartFrameOffset
	const FFrameNumber InitialFirstLoopStartFrameOffset = Params.FirstLoopStartFrameOffset;

	FFrameRate FrameRate = GetTypedOuter<UMovieScene>()->GetTickResolution();

	const FFrameNumber NewOffset = HasStartFrame() ? GetFirstLoopStartOffsetAtTrimTime(SplitTime, Params, GetInclusiveStartFrame(), FrameRate) : 0;

	UMovieSceneSkeletalAnimationSection* NewSection = Cast<UMovieSceneSkeletalAnimationSection>(Super::SplitSection(SplitTime, bDeleteKeys));
	if (NewSection != nullptr)
	{
		NewSection->Params.FirstLoopStartFrameOffset = NewOffset;
	}

	// Restore original offset modified by splitting
	Params.FirstLoopStartFrameOffset = InitialFirstLoopStartFrameOffset;
	return NewSection;
}


void UMovieSceneSkeletalAnimationSection::GetKeyHandles(TSet<FKeyHandle>& OutKeyHandles, TRange<float> TimeRange) const
{
	TRange<FFrameNumber> FrameNumberRange;

	const UMovieScene* MovieScene = GetTypedOuter<UMovieScene>();
	check(MovieScene != nullptr);
	FrameNumberRange.SetLowerBoundValue(MovieScene->GetTickResolution().AsFrameNumber(TimeRange.GetLowerBoundValue()));
	FrameNumberRange.SetUpperBoundValue(MovieScene->GetTickResolution().AsFrameNumber(TimeRange.GetUpperBoundValue()));
	if (!FrameNumberRange.Overlaps(GetRange()))
	{
		return;
	}

	for (auto It(Params.Weight.GetKeyHandleIterator()); It; ++It)
	{
		float Time = Params.Weight.GetKeyTime(*It);
		if (TimeRange.Contains(Time))
		{
			OutKeyHandles.Add(*It);
		}
	}
}


void UMovieSceneSkeletalAnimationSection::GetSnapTimes(TArray<FFrameNumber>& OutSnapTimes, bool bGetSectionBorders) const
{
	Super::GetSnapTimes(OutSnapTimes, bGetSectionBorders);

	const FFrameRate   FrameRate  = GetTypedOuter<UMovieScene>()->GetTickResolution();
	const FFrameNumber StartFrame = GetInclusiveStartFrame();
	const FFrameNumber EndFrame   = GetExclusiveEndFrame() - 1; // -1 because we don't need to add the end frame twice

	const float AnimPlayRate     = FMath::IsNearlyZero(Params.PlayRate) || Params.Animation == nullptr ? 1.0f : Params.PlayRate * Params.Animation->RateScale;
	const float SeqLengthSeconds = Params.GetSequenceLength() - FrameRate.AsSeconds(Params.StartFrameOffset + Params.EndFrameOffset) / AnimPlayRate;
	const float FirstLoopSeqLengthSeconds = SeqLengthSeconds - FrameRate.AsSeconds(Params.FirstLoopStartFrameOffset) / AnimPlayRate;

	const FFrameTime SequenceFrameLength = SeqLengthSeconds * FrameRate;
	const FFrameTime FirstLoopSequenceFrameLength = FirstLoopSeqLengthSeconds * FrameRate;
	if (SequenceFrameLength.FrameNumber > 1)
	{
		// Snap to the repeat times
		bool IsFirstLoop = true;
		FFrameTime CurrentTime = StartFrame;
		while (CurrentTime < EndFrame)
		{
			OutSnapTimes.Add(CurrentTime.FrameNumber);
			if (IsFirstLoop)
			{
				CurrentTime += FirstLoopSequenceFrameLength;
				IsFirstLoop = false;
			}
			else
			{
				CurrentTime += SequenceFrameLength;
			}
		}
	}
}

TOptional<FFrameTime> UMovieSceneSkeletalAnimationSection::GetOffsetTime() const
{
	return TOptional<FFrameTime>(Params.FirstLoopStartFrameOffset);
}

void UMovieSceneSkeletalAnimationSection::MigrateFrameTimes(FFrameRate SourceRate, FFrameRate DestinationRate)
{
	if (Params.StartFrameOffset.Value > 0)
	{
		FFrameNumber NewStartFrameOffset = ConvertFrameTime(FFrameTime(Params.StartFrameOffset), SourceRate, DestinationRate).FloorToFrame();
		Params.StartFrameOffset = NewStartFrameOffset;
	}

	if (Params.EndFrameOffset.Value > 0)
	{
		FFrameNumber NewEndFrameOffset = ConvertFrameTime(FFrameTime(Params.EndFrameOffset), SourceRate, DestinationRate).FloorToFrame();
		Params.EndFrameOffset = NewEndFrameOffset;
	}
	if (Params.FirstLoopStartFrameOffset.Value > 0)
	{
		FFrameNumber NewFirstLoopStartFrameOffset = ConvertFrameTime(FFrameTime(Params.FirstLoopStartFrameOffset), SourceRate, DestinationRate).FloorToFrame();
		Params.FirstLoopStartFrameOffset = NewFirstLoopStartFrameOffset;
	}
}

#if WITH_EDITOR
void UMovieSceneSkeletalAnimationSection::PreEditChange(FProperty* PropertyAboutToChange)
{
	// Store the current play rate so that we can compute the amount to compensate the section end time when the play rate changes
	PreviousPlayRate = Params.PlayRate;

	Super::PreEditChange(PropertyAboutToChange);
}

void UMovieSceneSkeletalAnimationSection::PostEditChangeProperty(FPropertyChangedEvent& PropertyChangedEvent)
{
	// Adjust the duration automatically if the play rate changes
	if (PropertyChangedEvent.Property != nullptr &&
		PropertyChangedEvent.Property->GetFName() == TEXT("PlayRate"))
	{
		float NewPlayRate = Params.PlayRate;

		if (!FMath::IsNearlyZero(NewPlayRate))
		{
			float CurrentDuration = UE::MovieScene::DiscreteSize(GetRange());
			float NewDuration = CurrentDuration * (PreviousPlayRate / NewPlayRate);
			SetEndFrame( GetInclusiveStartFrame() + FMath::FloorToInt(NewDuration) );
			PreviousPlayRate = NewPlayRate;
		}
	}

	Super::PostEditChangeProperty(PropertyChangedEvent);
}
#endif

#undef LOCTEXT_NAMESPACE