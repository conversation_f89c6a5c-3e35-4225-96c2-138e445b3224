// Copyright 1998-2017 Epic Games, Inc. All Rights Reserved.

/*=============================================================================
	TickProfiler.h: tick profiling support.
=============================================================================*/

#pragma once

#include "CoreMinimal.h"
#include "Engine/EngineBaseTypes.h"
#include "Engine/EngineTypes.h"

#if USE_TICK_PROFILER

#define SCOPE_LOCK_REF(X) FScopeLock ScopeLock(&X);

DECLARE_LOG_CATEGORY_EXTERN(LogTickProfiler, Log, All)

struct FTickFunction;
struct FTimerUnifiedDelegate;

enum ETickProfilingPayloadType
{
	TPTYPE_FrameMarker = 0,
	TPTYPE_NameReference = 1,
	TPTYPE_EndOfStreamMarker = 2,
	TPTYPE_QueuedTick = 3,
	TPTYPE_LogicTick = 4,
	TPTYPE_Timer = 5,
	TPTYPE_StartFrame = 6,
	TPTYPE_RunTickGroup = 7,
	TPTYPE_RunLogicTickGroup = 8,
	TPTYPE_RunAllTimers = 9,
};

struct FTickFunctionTraitFlags
{
	union
	{
		struct
		{
			uint32 bIsHighPriority : 1;
			uint32 bIsAnyThread : 1;
			uint32 bWithPrerequisites : 1;
			uint32 Reserved1 : 1;
			uint32 Reserved2 : 1;
			uint32 Reserved3 : 1;
			uint32 Reserved4 : 1;
			uint32 Reserved5 : 1;
		};
		uint8 Raw;
	};

	FTickFunctionTraitFlags();
};

class ENGINE_API FScopeQueuedTickTracker
{
public:
	FScopeQueuedTickTracker(FTickFunction* InTickFunction);
	~FScopeQueuedTickTracker();
private:
	FTickFunction* TickFunction = nullptr;
	uint32 Cycles = 0;
};

class ENGINE_API FScopeLogicTickTracker
{
public:
	FScopeLogicTickTracker(FTickFunction* InTickFunction);
	~FScopeLogicTickTracker();
private:
	FTickFunction* TickFunction = nullptr;
	uint64 Cycles = 0;
};

class ENGINE_API FScopeTimerTracker
{
public:
	FScopeTimerTracker(FTimerUnifiedDelegate* InTimerUnifiedDelegate);
	~FScopeTimerTracker();
private:
	FTimerUnifiedDelegate* TimerUnifiedDelegate = nullptr;
	uint64 Cycles = 0;
};

class ENGINE_API FScopeStartFrameTracker
{
public:
	FScopeStartFrameTracker();
	~FScopeStartFrameTracker();
private:
	uint64 Cycles = 0;
};

class ENGINE_API FScopeRunTickGroupTracker
{
public:
	FScopeRunTickGroupTracker(ETickingGroup InGroup);
	~FScopeRunTickGroupTracker();
private:
	ETickingGroup Group;
	uint64 Cycles = 0;
};

class ENGINE_API FScopeRunLogicTickGroupTracker
{
public:
	FScopeRunLogicTickGroupTracker(ETickingGroup InGroup);
	~FScopeRunLogicTickGroupTracker();
private:
	ETickingGroup Group;
	uint64 Cycles = 0;
};

class ENGINE_API FScopeRunAllTimersTracker
{
public:
	FScopeRunAllTimersTracker();
	~FScopeRunAllTimersTracker();
private:
	uint64 Cycles = 0;
};

#define SCOPE_QUEUED_TICK_TRACKER(TickFunction) FScopeQueuedTickTracker SQT(TickFunction)

#define SCOPE_LOGIC_TICK_TRACKER(TickFunction) FScopeLogicTickTracker SLT(TickFunction)

#define SCOPE_TIMER_TRACKER(TimerUnifiedDelegate) FScopeTimerTracker TT(TimerUnifiedDelegate)

#define SCOPE_START_FRAME_TRACKER FScopeStartFrameTracker SFT

#define SCOPE_RUN_TICK_GROUP_TRACKER(Group) FScopeRunTickGroupTracker RTGT(Group)

#define SCOPE_RUN_LOGIC_TICK_GROUP_TRACKER(Group) FScopeRunLogicTickGroupTracker RLTGT(Group)

#define SCOPE_RUN_ALL_TIMERS_TRACKER FScopeRunAllTimersTracker RATT

/*=============================================================================
	Tick profiler header.
=============================================================================*/

class FTickProfilerHeader
{
public:
	
	/** Magic to ensure we're opening the right file.	*/
	uint32	Magic;
	/** Version number to detect version mismatches.	*/
	uint32	Version;

	/** Tag, set via -tickprofiler=TAG				*/
	FString Tag;
	/** Game name, e.g. Example							*/
	FString GameName;

	/** Constructor.									*/
	FTickProfilerHeader();

	/** Resets the header info for a new session.		*/
	void Reset();

	/**
	 * Serialization operator.
	 *
	 * @param	Ar			Archive to serialize to
	 * @param	Header		Header to serialize
	 * @return	Passed in archive
	 */
	friend FArchive& operator << ( FArchive& Ar, FTickProfilerHeader& Header );
};

/*=============================================================================
	FTickProfiler
=============================================================================*/

/**
 * Tick profiler, using serialized token emission like e.g. script and malloc profiler.
 */
class FTickProfiler
{
private:
	/** File writer used to serialize data.															*/
	FArchive*								FileWriter;

	/** Critical section to sequence tracking.														*/
	FCriticalSection						CriticalSection;

	/** Mapping from name to index in name array.													*/
	TMap<FString,int32>						NameToNameTableIndexMap;

	/** Array of unique names.																		*/
	TArray<FString>							NameArray;

	/** Whether tracking is enabled.																*/
	bool									bIsTrackingEnabled;	

	/** Header for the current session.																*/
	FTickProfilerHeader					CurrentHeader;

	/** Delegate handle of TrackFrameBegin.															*/
	FDelegateHandle TrackFrameBeginHandle;

	/**
	 * Returns index of passed in name into name array. If not found, adds it.
	 *
	 * @param	Name	Name to find index for
	 * @return	Index of passed in name
	 */
	int32 GetNameTableIndex( FString& Name );

public:
	
	FTickProfiler();

	void EnableTracking(bool bShouldEnableTracking);

	ENGINE_API void TrackFrameBegin();

	ENGINE_API void TrackQueuedTick(FTickFunction* TickFunction, float TimeCost);

	ENGINE_API void TrackLogicTick(FTickFunction* TickFunction, float TimeCost);

	ENGINE_API void TrackTimer(FTimerUnifiedDelegate* TimerUnifiedDelegate, float TimeCost);

	ENGINE_API void TrackStartFrame(float TimeCost);

	ENGINE_API void TrackRunTickGroup(ETickingGroup InGroup, float TimeCost);

	ENGINE_API void TrackRunLogicTickGroup(ETickingGroup InGroup, float TimeCost);

	ENGINE_API void TrackRunAllTimers(float TimeCost);

	bool FORCEINLINE IsTrackingEnabled() const { return bIsTrackingEnabled; }

	void Exec(const TArray<FString>& Args, UWorld* World);
};

/** Global tick profiler instance. */
extern ENGINE_API FTickProfiler GTickProfiler;

#else	// USE_TICK_PROFILER

#define SCOPE_QUEUED_TICK_TRACKER(TickFunction)

#define SCOPE_LOGIC_TICK_TRACKER(TickFunction)

#define SCOPE_TIMER_TRACKER(TimerUnifiedDelegate)

#define SCOPE_START_FRAME_TRACKER

#define SCOPE_RUN_TICK_GROUP_TRACKER(Group)

#define SCOPE_RUN_LOGIC_TICK_GROUP_TRACKER(Group)

#define SCOPE_RUN_ALL_TIMERS_TRACKER

#endif
