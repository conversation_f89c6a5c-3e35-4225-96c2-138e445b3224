// Fill out your copyright notice in the Description page of Project Settings.


#include "Engine/ConfigOverriderFor120fps.h"
#include "Engine/Texture2D.h"
#include "DeviceProfiles/DeviceProfileManager.h"
#include "DeviceProfiles/DeviceProfile.h"

DEFINE_LOG_CATEGORY(LogConfigOverriderFor120fps);

static int32 GRefreshAllSamplerStates = 1;
static FAutoConsoleVariableRef CVarCVarRefreshAllSamplerStates(
	TEXT("r.RefreshSamplerStatesInConfigRecover"),
	GRefreshAllSamplerStates,
	TEXT(""));
static int32 GCVarEnableTextureFilterOverrider = 1;
static FAutoConsoleVariableRef CVarEnableTextureFilterOverrider(
	TEXT("r.EnableTextureFilterOverrider"),
	GCVarEnableTextureFilterOverrider,
	TEXT("修改纹理过滤. 0: off, 1: 120帧模式开启, 2: 节能模式开启"));


UConfigOverriderFor120fps* UConfigOverriderFor120fps::Singleton = nullptr;

UConfigOverriderFor120fps* UConfigOverriderFor120fps::Get()
{
	if(Singleton == nullptr)
	{
		Singleton = NewObject<UConfigOverriderFor120fps>();
		Singleton->AddToRoot();
	}
	return Singleton;
}

void UConfigOverriderFor120fps::Enable120fpsConfigs_Implementation(bool bEnable)
{
	UE_LOG(LogConfigOverriderFor120fps, Log, TEXT("Enable120fpsConfigs: %s"), bEnable ? TEXT("true") : TEXT("false"));
	if(bEnable)
	{
		if(!bHadApplyConfigFor120fps)
		{
			for(FConfigOverriderSetting& Config : ConfigFor120fps)
			{
				IConsoleVariable* CVar = IConsoleManager::Get().FindConsoleVariable(*Config.Key);
				if(CVar)
				{
					UE_LOG(LogTemp, Log, TEXT("ApplyConfigFor120fps %s: %f"), *Config.Key, Config.Value);
					Config.CacheValue = CVar->GetFloat();
					CVar->Set(Config.Value, ECVF_SetByConsole);
				}
			}
			bHadApplyConfigFor120fps = true;

			if(GCVarEnableTextureFilterOverrider == 1)
			{
				EnableTextureFilterOverrider(true);
			}
		}
	}
	else
	{
		RecoverConfigs();
	}
}

void UConfigOverriderFor120fps::EnableEnergySavingModeConfigs_Implementation(bool bEnable)
{
	UE_LOG(LogConfigOverriderFor120fps, Log, TEXT("EnableEnergySavingModeConfigs: %s"), bEnable ? TEXT("true") : TEXT("false"));
	if(bEnable)
	{
		if(!bHadApplyForEnergySaving)
		{
			for(FConfigOverriderSetting& Config : ConfigForEnergySaving)
			{
				IConsoleVariable* CVar = IConsoleManager::Get().FindConsoleVariable(*Config.Key);
				if(CVar)
				{
					UE_LOG(LogConfigOverriderFor120fps, Log, TEXT("ApplyForEnergySaving %s: %f"), *Config.Key, Config.Value);
					Config.CacheValue = CVar->GetFloat();
					CVar->Set(Config.Value, ECVF_SetByConsole);
				}
			}
			bHadApplyForEnergySaving = true;

			if(GCVarEnableTextureFilterOverrider == 2)
			{
				EnableTextureFilterOverrider(true);
			}
		}
	}
	else
	{
		RecoverEnergySavingModeConfigs();
	}
}

void UConfigOverriderFor120fps::RecoverConfigs_Implementation()
{
	UE_LOG(LogConfigOverriderFor120fps, Log, TEXT("RecoverConfigs"));

	Recover120fpsConfigs();
	RecoverEnergySavingModeConfigs();
}

void UConfigOverriderFor120fps::Recover120fpsConfigs_Implementation()
{
	UE_LOG(LogConfigOverriderFor120fps, Log, TEXT("Recover120fpsConfigs"));
	if(bHadApplyConfigFor120fps)
	{
		for(FConfigOverriderSetting& Config : ConfigFor120fps)
		{
			UE_LOG(LogTemp, Log, TEXT("RecoverConfigs %s: %f"), *Config.Key, Config.Value);
			IConsoleVariable* CVar = IConsoleManager::Get().FindConsoleVariable(*Config.Key);
			if(CVar)
			{
				CVar->Set(Config.CacheValue, ECVF_SetByConsole);
			}
		}
		bHadApplyConfigFor120fps = false;
		
		if(GCVarEnableTextureFilterOverrider == 1)
		{
			EnableTextureFilterOverrider(false);
		}
	}
}

void UConfigOverriderFor120fps::RecoverEnergySavingModeConfigs_Implementation()
{
	UE_LOG(LogConfigOverriderFor120fps, Log, TEXT("RecoverEnergySavingModeConfigs"));
	if(bHadApplyForEnergySaving)
	{
		for(FConfigOverriderSetting& Config : ConfigForEnergySaving)
		{
			UE_LOG(LogConfigOverriderFor120fps, Log, TEXT("RecoverConfigs %s: %f"), *Config.Key, Config.Value);
			IConsoleVariable* CVar = IConsoleManager::Get().FindConsoleVariable(*Config.Key);
			if(CVar)
			{
				CVar->Set(Config.CacheValue, ECVF_SetByConsole);
			}
		}
		bHadApplyForEnergySaving = false;
		
		if(GCVarEnableTextureFilterOverrider == 2)
		{
			EnableTextureFilterOverrider(false);
		}
	}
}

void UConfigOverriderFor120fps::EnableTextureFilterOverrider_Implementation(bool bEnable)
{
#if !UE_SERVER
	UTextureLODSettings* TextureLODSettings = UDeviceProfileManager::Get().GetActiveProfile()->GetTextureLODSettings();
	if(bEnable)
	{
		TextureLODSettings->OverrideTextureFilter(TextureLODGroupFilterOverride);
	}
	else
	{
		TextureLODSettings->RecoverOverrideTextureFilter();
	}
	if(GRefreshAllSamplerStates)
	{
		UTexture2D::RefreshAllSamplerStates();
	}
#endif
}

void ConfigOverriderFor120fpsTest(const TArray<FString>& Args, UWorld* World, FOutputDevice& Ar)
{
	if(Args.Num() < 2)
	{
		return;
	}
	UConfigOverriderFor120fps* ConfigOverrider = UConfigOverriderFor120fps::Get();
	
	bool bEnable120fps = FCString::Atoi(*Args[0]) == 1 ? true : false;
	bool bEnableEnergySavingModeConfigs = FCString::Atoi(*Args[1]) == 1 ? true : false;
	
	ConfigOverrider->Enable120fpsConfigs(bEnable120fps);
	ConfigOverrider->EnableEnergySavingModeConfigs(bEnableEnergySavingModeConfigs);
}

static FAutoConsoleCommand ConfigOverriderFor120fpsTestCmd(
	TEXT("ConfigOverriderFor120fpsTest"),
	TEXT(""),
	FConsoleCommandWithWorldArgsAndOutputDeviceDelegate::CreateStatic(ConfigOverriderFor120fpsTest)
);

void TextureFilterTest(const TArray<FString>& Args, UWorld* World, FOutputDevice& Ar)
{
	UE_LOG(LogTemp, Log, TEXT("TextureFilterTest"));
	if(Args.Num() < 1)
		return;
		
	static TArray<FTextureLODGroup> TextureLODGroupsCache;
	if(Args[0] == TEXT("0"))
	{
		// Recover all
		if(TextureLODGroupsCache.Num() > 0)
		{
			UTextureLODSettings* TextureLODSettings = UDeviceProfileManager::Get().GetActiveProfile()->GetTextureLODSettings();
			TextureLODSettings->TextureLODGroups = TextureLODGroupsCache;
		}
		UTexture2D::RefreshAllSamplerStates();
		return;
	}
	
	if(Args.Num() < 2)
		return;
		
	TMap<FString, TArray<TextureGroup>> Groups;
	Groups.Add(TEXT("World"), TArray<TextureGroup>{TEXTUREGROUP_World, TEXTUREGROUP_WorldNormalMap, TEXTUREGROUP_WorldSpecular,
		TEXTUREGROUP_WorldHD, TEXTUREGROUP_WorldSpecularHD, TEXTUREGROUP_WorldNormalMapHD, TEXTUREGROUP_HierarchicalLOD, TEXTUREGROUP_MaskedBillboard});
	Groups.Add(TEXT("char"), TArray<TextureGroup>{TEXTUREGROUP_Character, TEXTUREGROUP_CharacterNormalMap, TEXTUREGROUP_CharacterSpecular,
		TEXTUREGROUP_CharacterHD, TEXTUREGROUP_CharacterNormalMapHD, TEXTUREGROUP_CharacterSpecularHD});
	Groups.Add(TEXT("weapon"), TArray<TextureGroup>{TEXTUREGROUP_Weapon, TEXTUREGROUP_WeaponNormalMap, TEXTUREGROUP_WeaponSpecular,
		TEXTUREGROUP_WeaponHD, TEXTUREGROUP_WeaponNormalMapHD, TEXTUREGROUP_WeaponSpecularHD});
	Groups.Add(TEXT("vehicle"), TArray<TextureGroup>{TEXTUREGROUP_Vehicle, TEXTUREGROUP_VehicleNormalMap, TEXTUREGROUP_VehicleSpecular,
		TEXTUREGROUP_WeaponHD, TEXTUREGROUP_WeaponNormalMapHD, TEXTUREGROUP_WeaponSpecularHD});
	Groups.Add(TEXT("terrain"), TArray<TextureGroup>{TEXTUREGROUP_Terrain_Heightmap, TEXTUREGROUP_Terrain_Weightmap, TEXTUREGROUP_TerrainAlbedo});
	Groups.Add(TEXT("else"), TArray<TextureGroup>{TEXTUREGROUP_Effects, TEXTUREGROUP_EffectsNotFiltered, TEXTUREGROUP_Skybox, TEXTUREGROUP_UI, TEXTUREGROUP_Lightmap,
		TEXTUREGROUP_RenderTarget, TEXTUREGROUP_MobileFlattened, TEXTUREGROUP_ProcBuilding_Face, TEXTUREGROUP_ProcBuilding_LightMap, TEXTUREGROUP_Shadowmap,
		TEXTUREGROUP_ColorLookupTable, TEXTUREGROUP_Bokeh, TEXTUREGROUP_IESLightProfile, TEXTUREGROUP_Pixels2D});

	if(Groups.Find(Args[0]))
	{
		TArray<TextureGroup> TextureGroupToSwitch = Groups[Args[0]];
		UTextureLODSettings* TextureLODSettings = UDeviceProfileManager::Get().GetActiveProfile()->GetTextureLODSettings();
		int32 FilterParam = FCString::Atoi(*Args[1]);
		if(FilterParam == -1 && TextureLODGroupsCache.Num() > 0)
		{
			// Recover
			for(TextureGroup Group : TextureGroupToSwitch)
			{
				if(Group < TextureLODSettings->TextureLODGroups.Num() && Group < TextureLODGroupsCache.Num())
					TextureLODSettings->TextureLODGroups[Group].Filter = TextureLODGroupsCache[Group].Filter;
			}
		}
		else if(FilterParam >= 0 && FilterParam <= 4)
		{
			ETextureSamplerFilter Filter = (ETextureSamplerFilter)FilterParam;
		
			if(TextureLODGroupsCache.Num() == 0)
			{
				TextureLODGroupsCache = TextureLODSettings->TextureLODGroups;
			}
			for(TextureGroup Group : TextureGroupToSwitch)
			{
				if(Group < TextureLODSettings->TextureLODGroups.Num())
					TextureLODSettings->TextureLODGroups[Group].Filter = Filter;
			}
		}
		UTexture2D::RefreshAllSamplerStates();
	}
}

static FAutoConsoleCommand TextureFilterTestCmd(
	TEXT("TextureFilterTest"),
	TEXT(""),
	FConsoleCommandWithWorldArgsAndOutputDeviceDelegate::CreateStatic(TextureFilterTest)
);
