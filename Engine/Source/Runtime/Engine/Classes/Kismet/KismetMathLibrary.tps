<?xml version="1.0" encoding="utf-8"?>
<TpsData xmlns:xsd="http://www.w3.org/2001/XMLSchema" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance">
  <Name>All code listed on the GeometricTools website including Interesection of a Sphere and Cone</Name>
  <Location>/Engine/Source/Runtime/Engine/Classes/Kismet/KismetMathLibrary.h</Location>
  <Date>2016-06-07T15:34:43.1036454-04:00</Date>
  <Function>It is a function that returns true if a given sphere and a given cone are intersecting.</Function>
  <Justification>Such code is needed to implement an required optimization for the cubemap angular filtering.</Justification>
  <Eula>http://www.boost.org/doc/libs/1_49_0/LICENSE_1_0.txt</Eula>
  <RedistributeTo>
    <EndUserGroup>Licensees</EndUserGroup>
    <EndUserGroup>Git</EndUserGroup>
    <EndUserGroup>P4</EndUserGroup>
  </RedistributeTo>
  <LicenseFolder>/Engine/Source/ThirdParty/Licenses/BoostSoftwarev1_License.txt</LicenseFolder>
</TpsData>