// Copyright Epic Games, Inc. All Rights Reserved.
#pragma once
#include "CoreMinimal.h"
#include "Materials/MaterialExpressionTextureSampleParameter.h"
#include "MaterialExpressionTextureSampleParameter2DBatch.generated.h"

// @LQTech brainfkli - BEGIN Runtime Material Texture Batching
UCLASS(Category=Texture, hidecategories=Object, MinimalAPI)
class UMaterialExpressionTextureSampleParameter2DBatch : public UMaterialExpressionTextureSampleParameter
{
	GENERATED_UCLASS_BODY()

	UPROPERTY(meta = (RequiredInput = "false", ToolTip = "Defaults to 0 if not specified"))
	FExpressionInput TextureIndex;
	
#if WITH_EDITOR
	//~ Begin UMaterialExpression Interface
	virtual void GetCaption(TArray<FString>& OutCaptions) const override;
	virtual FExpressionInput* GetInput(int32 InputIndex) override;
	virtual FName GetInputName(int32 InputIndex) const override;
	virtual uint32 GetInputType(int32 InputIndex) override;
	virtual void GetConnectorToolTip(int32 InputIndex, int32 OutputIndex, TArray<FString>& OutToolTip) override;
	//~ End UMaterialExpression Interface
	
	//~ Begin UMaterialExpressionTextureSampleParameter Interface
	virtual bool TextureIsValid(UTexture* InTexture, FString& OutMessage) override;
	virtual void SetDefaultTexture() override;
	virtual int32 Compile(FMaterialCompiler* Compiler, int32 OutputIndex) override;
	//~ End UMaterialExpressionTextureSampleParameter Interface
#endif
};
// @LQTech brainfkli - END Runtime Material Texture Batching
