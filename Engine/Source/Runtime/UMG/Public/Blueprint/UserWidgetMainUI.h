// Copyright 1998-2017 Epic Games, Inc. All Rights Reserved.

#pragma once

#include "CoreMinimal.h"
#include "UObject/ObjectMacros.h"
#include "Blueprint/UserWidget.h"
#include "UserWidgetUI.h"
#include "UserWidgetMainUI.generated.h"

/**
 * The user widget main UI
 */
UCLASS(BlueprintType, Blueprintable)
class UMG_API UUserWidgetMainUI : public UUserWidgetUI
 {
	GENERATED_UCLASS_BODY()

 public:

	 virtual void SaveLayoutData(TArray<UWidget*>& Widgets) override;
	 
};