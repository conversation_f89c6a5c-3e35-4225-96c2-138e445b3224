// Copyright Epic Games, Inc. All Rights Reserved.

#include "Components/Button.h"

#include "Binding/States/WidgetStateBitfield.h"
#include "Binding/States/WidgetStateRegistration.h"
#include "Widgets/SNullWidget.h"
#include "Widgets/DeclarativeSyntaxSupport.h"
#include "Widgets/Input/SButton.h"
#include "GameFramework/InputSettings.h"
#include "Components/ButtonSlot.h"
#include "Styling/DefaultStyleCache.h"
#include "Styling/UMGCoreStyle.h"
#include "Blueprint/WidgetTree.h"
#include "TimerManager.h"
#include "ProfilingDebugging/CsvProfilerCommon.h"
#include "Framework/Notifications/NotificationManager.h"
#include "Widgets/Notifications/SNotificationList.h"
#include "Blueprint/WidgetTree.h"

#include UE_INLINE_GENERATED_CPP_BY_NAME(Button)

#define LOCTEXT_NAMESPACE "UMG"


// @SStudio rodmanyyang - BEGIN --story=********* 【PC】【开发】精准互动模式——精准互动UI及键位支持 https://tapd.woa.com/r/t?id=*********&type=story 添加UButton在ReleaseSlateResource时解绑输入的机制
static int32 PCUnbindWidgetInputSwitch = 1;
static FAutoConsoleVariableRef CVarEscCollapseWidgetMethod(
	TEXT("pc.EnableUnbindButtonInput"),
	PCUnbindWidgetInputSwitch,
	TEXT(""),
	ECVF_Default);
// @SStudio rodmanyyang - END

/////////////////////////////////////////////////////
// UButton

UButton::UButton(const FObjectInitializer& ObjectInitializer)
	: Super(ObjectInitializer)
{
PRAGMA_DISABLE_DEPRECATION_WARNINGS
	WidgetStyle = UE::Slate::Private::FDefaultStyleCache::GetRuntime().GetButtonStyle();
PRAGMA_ENABLE_DEPRECATION_WARNINGS

#if WITH_EDITOR 
	if (IsEditorWidget())
	{
PRAGMA_DISABLE_DEPRECATION_WARNINGS
		WidgetStyle = UE::Slate::Private::FDefaultStyleCache::GetEditor().GetButtonStyle();
PRAGMA_ENABLE_DEPRECATION_WARNINGS

		// The CDO isn't an editor widget and thus won't use the editor style, call post edit change to mark difference from CDO
		PostEditChange();
	}
#endif // WITH_EDITOR

PRAGMA_DISABLE_DEPRECATION_WARNINGS
	ColorAndOpacity = FLinearColor::White;
	BackgroundColor = FLinearColor::White;

	ClickMethod = EButtonClickMethod::DownAndUp;
	TouchMethod = EButtonTouchMethod::DownAndUp;

	// @SStudio yunjiewang - BEGIN PC适配: 部分页面Esc无法退出或者返回上一级 (author levingong)
	ListenEscMethod = EListenEscMethod::None;
	// @SStudio yunjiewang - END

	IsFocusable = true;
PRAGMA_ENABLE_DEPRECATION_WARNINGS

#if WITH_EDITORONLY_DATA
	AccessibleBehavior = ESlateAccessibleBehavior::Summary;
	bCanChildrenBeAccessible = false;
#endif
	// @SStudio yunjiewang - BEGIN 添加按钮支持点击事件透过的功能 (author jeffreyfang)
	IsPassMouseEvent = false;
	// @SStudio yunjiewang - END 添加按钮支持点击事件透过的功能
	// @SStudio yunjiewang - BEGIN 预览界面 (author leviwli)
	ReleasedReason = 0;
	// @SStudio yunjiewang - END 预览界面

	// @SStudio zachma - BEGIN: 
	IsImgAlphaBtn = false;
	// @SStudio zachma - END.

// @SStudio rodmanyyang - BEGIN --story=123589308 【PC】【CG32】PC全界面适配-大厅悬停音效
	bIsShowHover = true;
// @SStudio rodmanyyang - END
}

void UButton::ReleaseSlateResources(bool bReleaseChildren)
{
	Super::ReleaseSlateResources(bReleaseChildren);

	MyButton.Reset();

	// @SStudio rodmanyyang - BEGIN  --story=********* 【PC】【开发】精准互动模式——精准互动UI及键位支持 https://tapd.woa.com/r/t?id=*********&type=story 添加UButton在ReleaseSlateResource时解绑输入的机制
	if (PCUnbindWidgetInputSwitch)
	{
		InputActionBindings.UnBindWidget(this);
	}
	// @SStudio rodmanyyang - END
}

TSharedRef<SWidget> UButton::RebuildWidget()
{
PRAGMA_DISABLE_DEPRECATION_WARNINGS
	MyButton = SNew(SButton)
		.OnClicked(BIND_UOBJECT_DELEGATE(FOnClicked, SlateHandleClicked))
		.OnPressed(BIND_UOBJECT_DELEGATE(FSimpleDelegate, SlateHandlePressed))
		.OnReleased(BIND_UOBJECT_DELEGATE(FSimpleDelegate, SlateHandleReleased))
		.OnHovered_UObject( this, &ThisClass::SlateHandleHovered )
		.OnUnhovered_UObject( this, &ThisClass::SlateHandleUnhovered )
		.ButtonStyle(&WidgetStyle)
		.ClickMethod(ClickMethod)
		.TouchMethod(TouchMethod)
		.PressMethod(PressMethod)
		.IsFocusable(IsFocusable)
		// @SStudio yunjiewang - BEGIN 添加按钮支持点击事件透过的功能 (author jeffreyfang)
		.IsPassMouseEvent(IsPassMouseEvent)
		// @SStudio yunjiewang - END 添加按钮支持点击事件透过的功能
		// @SStudio yunjiewang - BEGIN CJAPI
		.IsImgAlphaBtn(IsImgAlphaBtn)
		// @SStudio yunjiewang - END CJAPI
		// @SStudio rodmanyyang - BEGIN --story=123589308 【PC】【CG32】PC全界面适配-大厅悬停音效
		.IsShowHover(bIsShowHover)
		// @SStudio rodmanyyang - END
		;
PRAGMA_ENABLE_DEPRECATION_WARNINGS
	if ( GetChildrenCount() > 0 )
	{
		Cast<UButtonSlot>(GetContentSlot())->BuildSlot(MyButton.ToSharedRef());
	}
	
	return MyButton.ToSharedRef();
}

void UButton::SynchronizeProperties()
{
	Super::SynchronizeProperties();

	if (!MyButton.IsValid())
	{
		return;
	}

PRAGMA_DISABLE_DEPRECATION_WARNINGS
	MyButton->SetButtonStyle(&WidgetStyle);
	MyButton->SetColorAndOpacity( ColorAndOpacity );
	MyButton->SetBorderBackgroundColor( BackgroundColor );
	MyButton->SetClickMethod(ClickMethod);
	MyButton->SetTouchMethod(TouchMethod);
	MyButton->SetPressMethod(PressMethod);

	MyButton->SetOnMouseButtonDown(BIND_UOBJECT_DELEGATE(FPointerEventHandler, HandleMouseButtonDown));
	MyButton->SetOnMouseButtonUp(BIND_UOBJECT_DELEGATE(FPointerEventHandler, HandleMouseButtonUp));
	// @SStudio yunjiewang - BEGIN 枪械系统: 玩家按住开火键，不能调整准心位置 (author andrewdeng)
	MyButton->SetOnMouseMove(BIND_UOBJECT_DELEGATE(FPointerEventHandler, HandleMouseMove));
	// @SStudio yunjiewang - END
	MyButton->SetCheckClickHandler(BIND_UOBJECT_DELEGATE(FPointerEventHandler, HandleOnCheckClick));
	// @SStudio rodmanyyang - BEGIN --story=123589308 【PC】【CG32】PC全界面适配-大厅悬停音效
	MyButton->SetShowHover(bIsShowHover);
	// @SStudio rodmanyyang - END
PRAGMA_ENABLE_DEPRECATION_WARNINGS
}

// @SStudio rodmanyyang - BEGIN --story=123589308 【PC】【CG32】PC全界面适配-大厅悬停音效
void UButton::SetShowHover(bool InShowHover)
{
	bIsShowHover = InShowHover;
	if (MyButton.IsValid()) {
		MyButton->SetShowHover(InShowHover);
	}
}
// @SStudio rodmanyyang - END

// @SStudio yunjiewang - BEGIN button绑定inputaction (author amazingqi)
void UButton::OnWidgetRebuilt()
{
	Super::OnWidgetRebuilt();

	InputActionBindings.BindWidget(this);
}
// @SStudio yunjiewang - END

UClass* UButton::GetSlotClass() const
{
	return UButtonSlot::StaticClass();
}

void UButton::OnSlotAdded(UPanelSlot* InSlot)
{
	// Add the child to the live slot if it already exists
	if ( MyButton.IsValid() )
	{
		CastChecked<UButtonSlot>(InSlot)->BuildSlot(MyButton.ToSharedRef());
	}
}

void UButton::OnSlotRemoved(UPanelSlot* InSlot)
{
	// Remove the widget from the live slot if it exists.
	if ( MyButton.IsValid() )
	{
		MyButton->SetContent(SNullWidget::NullWidget);
	}
}

PRAGMA_DISABLE_DEPRECATION_WARNINGS
void UButton::SetStyle(const FButtonStyle& InStyle)
{
	WidgetStyle = InStyle;
	if ( MyButton.IsValid() )
	{
		MyButton->SetButtonStyle(&WidgetStyle);
	}
}

// @SStudio hackgao - BEGIN 修复研究所个性操作按键显示效果残留问题
void UButton::ClearButtonStyleResourceHandle()
{
	if (MyButton.IsValid())
	{
		MyButton->ClearButtonStyleResourceHandle();
	}
}
// @SStudio hackgao - END

const FButtonStyle& UButton::GetStyle() const
{
	return WidgetStyle;
}

void UButton::SetColorAndOpacity(FLinearColor InColorAndOpacity)
{
	ColorAndOpacity = InColorAndOpacity;
	if ( MyButton.IsValid() )
	{
		MyButton->SetColorAndOpacity(InColorAndOpacity);
	}
}

FLinearColor UButton::GetColorAndOpacity() const
{
	return ColorAndOpacity;
}

void UButton::SetBackgroundColor(FLinearColor InBackgroundColor)
{
	BackgroundColor = InBackgroundColor;
	if ( MyButton.IsValid() )
	{
		MyButton->SetBorderBackgroundColor(InBackgroundColor);
	}
}

FLinearColor UButton::GetBackgroundColor() const
{
	return BackgroundColor;
}
PRAGMA_ENABLE_DEPRECATION_WARNINGS

bool UButton::IsPressed() const
{
	if ( MyButton.IsValid() )
	{
		return MyButton->IsPressed();
	}

	return false;
}

PRAGMA_DISABLE_DEPRECATION_WARNINGS
void UButton::SetClickMethod(EButtonClickMethod::Type InClickMethod)
{
	ClickMethod = InClickMethod;
	if ( MyButton.IsValid() )
	{
		MyButton->SetClickMethod(ClickMethod);
	}
}

EButtonClickMethod::Type UButton::GetClickMethod() const
{
	return ClickMethod;
}

void UButton::SetTouchMethod(EButtonTouchMethod::Type InTouchMethod)
{
	TouchMethod = InTouchMethod;
	if ( MyButton.IsValid() )
	{
		MyButton->SetTouchMethod(TouchMethod);
	}
}

EButtonTouchMethod::Type UButton::GetTouchMethod() const
{
	return TouchMethod;
}

void UButton::SetPressMethod(EButtonPressMethod::Type InPressMethod)
{
	PressMethod = InPressMethod;
	if ( MyButton.IsValid() )
	{
		MyButton->SetPressMethod(PressMethod);
	}
}

EButtonPressMethod::Type UButton::GetPressMethod() const
{
	return PressMethod;
}

bool UButton::GetIsFocusable() const
{
	return IsFocusable;
}

void UButton::InitIsFocusable(bool InIsFocusable)
{
	IsFocusable = InIsFocusable;
}

PRAGMA_ENABLE_DEPRECATION_WARNINGS

void UButton::PostLoad()
{
	Super::PostLoad();

	if ( GetChildrenCount() > 0 )
	{
		//TODO UMG Pre-Release Upgrade, now buttons have slots of their own.  Convert existing slot to new slot.
		if ( UPanelSlot* PanelSlot = GetContentSlot() )
		{
			UButtonSlot* ButtonSlot = Cast<UButtonSlot>(PanelSlot);
			if ( ButtonSlot == NULL )
			{
				ButtonSlot = NewObject<UButtonSlot>(this);
				ButtonSlot->Content = GetContentSlot()->Content;
				ButtonSlot->Content->Slot = ButtonSlot;
				Slots[0] = ButtonSlot;
			}
		}
	}
}

FReply UButton::SlateHandleClicked()
{
	// @SStudio yunjiewang - BEGIN flush监控：按钮点击允许flush (author chasepeng)
	ALLOW_FLUSHING;
	// @SStudio yunjiewang - END
	// @SStudio yunjiewang - BEGIN 通用UI按钮控件每帧最多只响应一次事件，以解决低帧率下模态框无法拦截快速点击的情况 (author virgilwen)
	if (IsThisFrameClicked == false)
	{
		CSV_SCOPED_HITCH_DATA_MSG_COMMON(OnButtonClicked, GetFName());
		OnClicked.Broadcast();
		if (GetWorld())
		{
			IsThisFrameClicked = true;
			TWeakObjectPtr<UButton> WeakThisCap(this);
			GetWorld()->GetTimerManager().SetTimerForNextTick(FTimerDelegate::CreateLambda([WeakThisCap]() {
				if (WeakThisCap.IsValid())
				{
					auto StrongThis = WeakThisCap.Get();
					StrongThis->IsThisFrameClicked = false;
				}
			}));
		}
	}
	// @SStudio yunjiewang - END
	// @SStudio yunjiewang - BEGIN 赛事OBS: 在恢复比赛界面点击取消，然后恢复比赛按钮无法再次点击 (author enicalin)
	else if (GetWorld() && GetWorld()->GetNetMode() == NM_Client)
	{
		static auto* CVarClientPauseInfoPtr = IConsoleManager::Get().FindConsoleVariable(TEXT("g.ClientPauseInfo"));
		static const auto* CVarEnableClientPausePtr = IConsoleManager::Get().FindConsoleVariable(TEXT("g.EnableClientPause"));
		if (CVarEnableClientPausePtr && CVarEnableClientPausePtr->GetInt() >= 1 &&
			CVarClientPauseInfoPtr && CVarClientPauseInfoPtr->GetInt() >= 1)
		{
			CSV_SCOPED_HITCH_DATA_MSG_COMMON(OnButtonClicked, GetFName());
			OnClicked.Broadcast();
		}
	}
	// @SStudio yunjiewang - END

	return FReply::Handled();
}

void UButton::SlateHandlePressed()
{
	// @SStudio yunjiewang - BEGIN flush监控：按钮点击允许flush (author chasepeng)
	ALLOW_FLUSHING;
	// @SStudio yunjiewang - END

	OnPressed.Broadcast();
	BroadcastBinaryPostStateChange(UWidgetPressedStateRegistration::Bit, true);


}

void UButton::SlateHandleReleased()
{
	// @SStudio yunjiewang - BEGIN flush监控：按钮点击允许flush (author chasepeng)
	ALLOW_FLUSHING;
	// @SStudio yunjiewang - END
	// SStudio yunjiewang - BEGIN 预览界面 (author leviwli)
	if (MyButton.IsValid())
	{
		ReleasedReason = (uint8)MyButton->GetReleasedReason();
	}
	// SStudio yunjiewang - END 预览界面

	OnReleased.Broadcast();
	BroadcastBinaryPostStateChange(UWidgetPressedStateRegistration::Bit, false);
}

void UButton::SlateHandleHovered()
{
	// @SStudio yunjiewang - BEGIN flush监控：按钮点击允许flush (author chasepeng)
	ALLOW_FLUSHING;
	// @SStudio yunjiewang - END
	OnHovered.Broadcast();
	BroadcastBinaryPostStateChange(UWidgetHoveredStateRegistration::Bit, true);
}

void UButton::SlateHandleUnhovered()
{
	// @SStudio yunjiewang - BEGIN flush监控：按钮点击允许flush (author chasepeng)
	ALLOW_FLUSHING;
	// @SStudio yunjiewang - END
	OnUnhovered.Broadcast();
	BroadcastBinaryPostStateChange(UWidgetHoveredStateRegistration::Bit, false);
}

#if WITH_ACCESSIBILITY
TSharedPtr<SWidget> UButton::GetAccessibleWidget() const
{
	return MyButton;
}
#endif

#if WITH_EDITOR

const FText UButton::GetPaletteCategory()
{
	return LOCTEXT("Common", "Common");
}

void UButton::PostEditChangeProperty(FPropertyChangedEvent& PropertyChangedEvent)
{
	Super::PostEditChangeProperty(PropertyChangedEvent);

	if (PropertyChangedEvent.Property &&
	PropertyChangedEvent.Property->GetFName() == GET_MEMBER_NAME_CHECKED(UButton, IsImgAlphaBtn))
	{
		if (!IsImgAlphaBtn)
		{
			bUseCustomSettings = false;
		}
	}

	// @SStudio shawnxjin - BEGIN -- 提交信息：svn CGUE5031 1015944
	//【UGCMobile】属性面板调整 https://tapd.woa.com/20386762/prong/stories/view/1020386762118075315 异性按钮自定义Texture增加压缩类型判断和提示
	if (PropertyChangedEvent.Property && PropertyChangedEvent.Property->GetFName() == GET_MEMBER_NAME_CHECKED(UButton, CustomHitAreaTexture)
		&& CustomHitAreaTexture && CustomHitAreaTexture->CompressionSettings != TC_EditorIcon)
	{
		CustomHitAreaTexture = nullptr;

		auto Message = FString("CompressionSettings Must Be UserInterface2D");
		FNotificationInfo Info(FText::FromString(Message));
		Info.ExpireDuration = 2.0f;
		FSlateNotificationManager::Get().AddNotification(Info);
	}
	// @SStudio shawnxjin - END
}

#endif

/////////////////////////////////////////////////////

#undef LOCTEXT_NAMESPACE

