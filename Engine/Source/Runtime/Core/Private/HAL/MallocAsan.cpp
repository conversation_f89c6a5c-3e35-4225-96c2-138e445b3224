// Copyright 2019 Tencent, Inc. All Rights Reserved.

#include "HAL/MallocAsan.h"
#include "Misc/AssertionMacros.h"
#include "Math/UnrealMathUtility.h"
#include "Templates/AlignmentTemplates.h"

#include "Misc/ScopeLock.h"
#include "HAL/MemoryBase.h"
#include "CoreGlobals.h"

#if PLATFORM_LINUX
#include <malloc.h>
#endif

#if PLATFORM_IOS
#include "mach/mach.h"
#endif

#if PLATFORM_WINDOWS
#include "Windows/WindowsHWrapper.h"
#endif

FMallocAsan::FMallocAsan()
{
#if PLATFORM_WINDOWS
	intptr_t	CrtHeapHandle = _get_heap_handle();
	ULONG		EnableLFH = 2;
	HeapSetInformation((void*)CrtHeapHandle, HeapCompatibilityInformation, &EnableLFH, sizeof(EnableLFH));
#endif
    
    NodeSize = sizeof(FMallocAsanAddressInfo);
    NodeCount = ASAN_BLOCK_SIZE / NodeSize - 1;
    
#if USE_MALLOC_FREE_MISMATCH
    for(int32 Idx = 0; Idx < ASAN_BUCKET_MAX; Idx++)
    {
        FMallocAsanBucket* Bucket = &BucketList[Idx];
        Bucket->BlockPtr = Bucket->BlockPtr = mmap(nullptr, ASAN_BLOCK_SIZE, PROT_READ | PROT_WRITE, MAP_PRIVATE | MAP_ANON, -1, 0);
        check(Bucket->BlockPtr != nullptr);
        for(int i = 0; i< NodeCount; i++)
        {
            FMallocAsanAddressInfo* Info = (FMallocAsanAddressInfo*)((uint8*)Bucket->BlockPtr + i*NodeSize);
            if(Info != nullptr)
            {
                Info->Ptr = nullptr;
                Info->Tag = USE_SDK_MALLOC;
            }
        }
        Bucket->UsedCount = 0;
    }
#endif
    
    UsedCountMax = 0;
    UsedBucketMax = 0;
    
    MallocCount = 0;
    FreeCount = 0;
    ReallocCount = 0;
    
    CheckFreeAddressCount = 0;
    CheckSuccessCount = 0;
    
    FindAddressCount = 0;
    FindAddressFaildCount = 0;
    
    AddAddressCount = 0;
    AddAddressFaildCount = 0;
    
    RecordMemSize = ASAN_RECORD_MEM_SIZE;
    
    UE_LOG(LogTemp, Log, TEXT("FMallocAsan, Created"));
}

FMallocAsan::~FMallocAsan()
{
#if USE_MALLOC_FREE_MISMATCH
    for(int32 Idx = 0; Idx < ASAN_BUCKET_MAX; Idx++)
    {
        FMallocAsanBucket* Bucket = &BucketList[Idx];
        if (Bucket->BlockPtr != nullptr)
        {
            munmap(Bucket->BlockPtr, ASAN_BLOCK_SIZE);
            Bucket->BlockPtr = nullptr;
        }
    }
#endif
}

void* FMallocAsan::Malloc(SIZE_T Size, uint32 Alignment)
{
    Alignment = FMath::Max(Size >= 16 ? (uint32)16 : (uint32)8, Alignment);
#if USE_MALLOC_FREE_MISMATCH
    FScopeLock ScopedLock(&BucketGuard);
    
    void* Result = malloc(Size);
    if(Result != nullptr)
    {
        MallocCount++;
        AddAddressInfo(Result);
    }
    
#elif PLATFORM_LINUX
	void* Result;
	if (UNLIKELY(posix_memalign(&Result, Alignment, Size) != 0))
	{
		Result = nullptr;
	}
#else
	void* Ptr = malloc(Size + Alignment + sizeof(void*) + sizeof(SIZE_T));
	check(Ptr);
	void* Result = Align((uint8*)Ptr + sizeof(void*) + sizeof(SIZE_T), Alignment);
	*((void**)((uint8*)Result - sizeof(void*))) = Ptr;
	*((SIZE_T*)((uint8*)Result - sizeof(void*) - sizeof(SIZE_T))) = Size;
#endif

	if (Result == nullptr)
	{
		FPlatformMemory::OnOutOfMemory(Size, Alignment);
	}
	return Result;
}

void* FMallocAsan::Realloc(void* Ptr, SIZE_T NewSize, uint32 Alignment)
{
    FScopeLock ScopedLock(&BucketGuard);
	void* Result;
	Alignment = FMath::Max(NewSize >= 16 ? (uint32)16 : (uint32)8, Alignment);
    
#if USE_MALLOC_FREE_MISMATCH
    if (Ptr && NewSize)
    {
        ReallocCount++;
        Result = Malloc(NewSize, Alignment);
        SIZE_T PtrSize = malloc_size(Ptr);
        FMemory::Memcpy(Result, Ptr, FMath::Min(NewSize, PtrSize));
        Free(Ptr);
    }
    else if (Ptr == nullptr)
    {
        Result = Malloc(NewSize, Alignment);
    }
    else
    {
        Free(Ptr);
        Result = nullptr;
    }
#elif PLATFORM_LINUX
	if (Ptr && NewSize)
	{
		SIZE_T UsableSize = malloc_usable_size(Ptr);
		if (UNLIKELY(posix_memalign(&Result, Alignment, NewSize) != 0))
		{
			Result = nullptr;
		}
		if (LIKELY(UsableSize))
		{
			FMemory::Memcpy(Result, Ptr, FMath::Min(NewSize, UsableSize));
		}
		free(Ptr);
	}
	else if (Ptr == nullptr)
	{
		if (UNLIKELY(posix_memalign(&Result, Alignment, NewSize) != 0))
		{
			Result = nullptr;
		}
	}
	else
	{
		free(Ptr);
		Result = nullptr;
	}
#else
	if (Ptr && NewSize)
	{
		Result = Malloc(NewSize, Alignment);
		SIZE_T PtrSize = 0;
		GetAllocationSize(Ptr, PtrSize);
		FMemory::Memcpy(Result, Ptr, FMath::Min(NewSize, PtrSize));
		Free(Ptr);
	}
	else if (Ptr == nullptr)
	{
		Result = Malloc(NewSize, Alignment);
	}
	else
	{
		free(*((void**)((uint8*)Ptr - sizeof(void*))));
		Result = nullptr;
	}
#endif
	if (Result == nullptr && NewSize != 0)
	{
		FPlatformMemory::OnOutOfMemory(NewSize, Alignment);
	}

	return Result;
}

void FMallocAsan::Free(void* Ptr)
{
	IncrementTotalFreeCalls();
#if USE_MALLOC_FREE_MISMATCH
    FScopeLock ScopedLock(&BucketGuard);
    if(Ptr == nullptr)
    {
        return;
    }
    
    FreeCount++;
    
    UPTRINT Key = (UPTRINT)Ptr;
    int64 Idx = (int64)Key;
    Idx = Idx % ASAN_BUCKET_MAX;
    FMallocAsanBucket* Bucket = &BucketList[Idx];
    check(Bucket != nullptr);
    
    FMallocAsanAddressInfo* Info = FindAddressInfo(Ptr);
    check(Info != nullptr);
    check(Info->Tag == USE_UE4_FMALLOC);
    
    CheckSuccessCount++;
    Info->Tag = USE_UE4_CHECKED;
    Bucket->UsedCount--;
    
	free(Ptr);
    
#elif PLATFORM_LINUX
	free(Ptr);
#else
	if (Ptr)
	{
		free(*((void**)((uint8*)Ptr - sizeof(void*))));
	}
#endif
}

void FMallocAsan::CheckFreeAddress(void* Ptr)
{
#if USE_MALLOC_FREE_MISMATCH
    if(Ptr == nullptr)
    {
        return;
    }
    
    FScopeLock ScopedLock(&BucketGuard);
    CheckFreeAddressCount++;
    
    UPTRINT Key = (UPTRINT)Ptr;
    int64 Idx = (int64)Key;
    Idx = Idx % ASAN_BUCKET_MAX;
    FMallocAsanBucket* Bucket = &BucketList[Idx];
    check(Bucket != nullptr);
    
    FMallocAsanAddressInfo* Info = FindAddressInfo(Ptr);
    if(Info != nullptr)
    {
        if(Info->Tag == USE_UE4_CHECKED)
        {
            return;
        }
        
        CheckSuccessCount++;
        check(Info->Tag == USE_SDK_MALLOC);
        Bucket->UsedCount--;
        Info->Tag = USE_UE4_CHECKED;
    }

#endif
}

FMallocAsanAddressInfo* FMallocAsan::FindAddressInfo(void* Ptr)
{
    FMallocAsanAddressInfo* Info = nullptr;
#if USE_MALLOC_FREE_MISMATCH
    if(Ptr == nullptr)
    {
        return nullptr;
    }
    FindAddressCount++;
    
    UPTRINT Key = (UPTRINT)Ptr;
    int64 Idx = (int64)Key;
    Idx = Idx % ASAN_BUCKET_MAX;
    FMallocAsanBucket* Bucket = &BucketList[Idx];
    check(Bucket->BlockPtr != nullptr);
    
    for(int i = 0; i< NodeCount; i++)
    {
        Info = (FMallocAsanAddressInfo*)((uint8*)Bucket->BlockPtr + i*NodeSize);
        if(Info != nullptr && Info->Ptr == Ptr)
        {
            break;
        }
    }
    
    if(Info == nullptr)
    {
        FindAddressFaildCount++;
    }
    
#endif
    return Info;
}

void FMallocAsan::AddAddressInfo(void* Ptr)
{
#if USE_MALLOC_FREE_MISMATCH
    FScopeLock ScopedLock(&BucketGuard);
    FMallocAsanAddressInfo* Info = nullptr;
    if(Ptr == nullptr)
    {
        return;
    }
    
    AddAddressCount++;
    
    UPTRINT Key = (UPTRINT)Ptr;
    int64 Idx = (int64)Key;
    Idx = Idx % ASAN_BUCKET_MAX;
    if(Idx > UsedBucketMax)
    {
        UsedBucketMax = Idx;
    }
    FMallocAsanBucket* Bucket = &BucketList[Idx];
    check(Bucket->BlockPtr != nullptr);
    for(int i = 0; i< NodeCount; i++)
    {
        Info = (FMallocAsanAddressInfo*)((uint8*)Bucket->BlockPtr + i*NodeSize);
        if(Info != nullptr && (Info->Ptr == nullptr || Info->Tag == USE_UE4_CHECKED))
        {
            Info->Ptr = Ptr;
            Info->Tag = USE_UE4_FMALLOC;
            Bucket->UsedCount++;
            
            if(Bucket->UsedCount > UsedCountMax)
            {
                UsedCountMax = Bucket->UsedCount;
            }
            
            if(UsedCountMax >= NodeCount)
            {
                for(int j = 0; j< NodeCount; j++)
                {
                    Info = (FMallocAsanAddressInfo*)((uint8*)Bucket->BlockPtr + j*NodeSize);
                    UE_LOG(LogTemp, Log, TEXT("AddressInfo: Bucket=%d, UsedCount=%d"), j, Bucket->UsedCount);
                }
                check(UsedCountMax < NodeCount);
            }
            break;
        }
    }
    
    if(Info == nullptr)
    {
        AddAddressFaildCount++;
    }
#endif
}

bool FMallocAsan::GetAllocationSize(void *Original, SIZE_T &SizeOut)
{
    if (!Original)
    {
        return false;
    }
#if    USE_MALLOC_FREE_MISMATCH
    SizeOut = malloc_size(Original);
#elif PLATFORM_LINUX
    SizeOut = malloc_usable_size(Original);
#else
    SizeOut = *((SIZE_T*)((uint8*)Original - sizeof(void*) - sizeof(SIZE_T)));
#endif
    return true;
}

bool FMallocAsan::IsInternallyThreadSafe() const
{
#if PLATFORM_MAC
    return true;
#elif PLATFORM_IOS
    return true;
#elif PLATFORM_LINUX
    return true;    // malloc()/free() is thread-safe on Linux
#else
    return false;
#endif
}

bool FMallocAsan::ValidateHeap()
{
#if PLATFORM_WINDOWS
    int32 Result = _heapchk();
    check(Result != _HEAPBADBEGIN);
    check(Result != _HEAPBADNODE);
    check(Result != _HEAPBADPTR);
    check(Result != _HEAPEMPTY);
    check(Result == _HEAPOK);
#else
    return true;
#endif
    return true;
}
