// Copyright 1998-2017 Epic Games, Inc. All Rights Reserved.


#include "SNewLuaDialog.h"
#include "Misc/MessageDialog.h"
#include "HAL/FileManager.h"
#include "Misc/App.h"
#include "SlateOptMacros.h"
#include "Widgets/Layout/SSeparator.h"
#include "Widgets/Images/SImage.h"
#include "Widgets/Layout/SGridPanel.h"
#include "Widgets/Input/SEditableTextBox.h"
#include "Widgets/Input/SButton.h"
#include "Widgets/Input/SCheckBox.h"
#include "Framework/Docking/TabManager.h"
#include "Editor/EditorStyle/Public/EditorStyleSet.h"
#include "Runtime/Engine/Classes/Engine/Blueprint.h"
#include "Editor/UnrealEd/Classes//Editor/EditorPerProjectUserSettings.h"
#include "Runtime/Engine/Classes/Engine/BlueprintGeneratedClass.h"
#include "Interfaces/IProjectManager.h"
#include "SGetSuggestedIDEWidget.h"
#include "Editor/UnrealEd/Public/SourceCodeNavigation.h"
//#include "ClassViewerModule.h"
//#include "ClassViewerFilter.h"
#include "Editor/ContentBrowser/Public/IContentBrowserSingleton.h"
#include "Editor/ContentBrowser/Public/ContentBrowserModule.h"
//#include "Private/SClassViewer.h"
#include "DesktopPlatformModule.h"
#include "IDocumentation.h"
//#include "EditorClassUtils.h"
#include "UObject/UObjectHash.h"
//#include "Widgets/Workflow/SWizard.h"
//#include "Widgets/Input/SHyperlink.h"
//#include "TutorialMetaData.h"
#include "Editor/UnrealEd/Public/Kismet2/KismetEditorUtilities.h"
#include "AssetRegistry/AssetRegistryModule.h"
#include "Editor/UnrealEd/Public/Toolkits/AssetEditorManager.h"
#include "Framework/Notifications/NotificationManager.h"
#include "Widgets/Notifications/SNotificationList.h"
#include "Widgets/Layout/SScrollBox.h"
#include "Misc/FileHelper.h"
#include "Misc/Paths.h"
#include "Runtime/Slate/Public/Widgets/Input/SHyperlink.h"
#include "Editor/EditorStyle/Public/EditorStyleSet.h"

#define LOCTEXT_NAMESPACE "GameProjectGeneration"

BEGIN_SLATE_FUNCTION_BUILD_OPTIMIZATION
void SNewLuaDialog::Construct(const FArguments& InArgs)
{
	BPPath = TEXT("/Game/UMG/UI_BP/Test/TestHello_UIBP");
	NewLuaPath = FPaths::ConvertRelativePathToFull(FPaths::ProjectDir()) + TEXT("Source/Lua/client/module/umg/");
	NewLuaName = "";
	IsStatusLobby = true;
	IsStatusFighting = false;
	IsStatusLogin = false;
	IsConcernGameState = false;
	IsPushPanel = false;
	IsCreateEntryFile = false;
	IsCreateLogicFile = false;
	UIContainerTypeTargets.Add(MakeShareable(new FString(TEXT("Default"))));
	UIContainerTypeTargets.Add(MakeShareable(new FString(TEXT("Top"))));
	UIContainerTypeTargets.Add(MakeShareable(new FString(TEXT("Bottom"))));
	EntryFileName = "";
	LogicDirName = "";
	LogicFileName = "";
	LogicTableName = "";

	BPFilePathPicker = SNew(SFilePathPicker)
		.BrowseButtonImage(FEditorStyle::GetBrush("PropertyWindow.Button_Ellipsis"))
		.BrowseDirectory(TEXT("../../../../Survive/Content/UMG/"))
		.BrowseButtonStyle(FEditorStyle::Get(), "HoverHintOnly")
		.BrowseTitle(LOCTEXT("PropertyEditorTitle", "File picker..."))
		.BrowseButtonToolTip(LOCTEXT("BPFileButtonToolTipText", "Choose a Widget Blueprint:"))
		.FilePath(this, &SNewLuaDialog::HandleBPFilePathPickerFilePath)
		.FileTypeFilter(TEXT("Widget Blueprint (*.uasset)|*.uasset"))
		.OnPathPicked(this, &SNewLuaDialog::HandleBPFilePathPickerPathPicked);

	ResultText = SNew(STextBlock)
		.Text(FText::FromString(""));

	ChildSlot
	[
		SNew(SBorder)
		.Padding(18)
		.BorderImage(FEditorStyle::GetBrush("Docking.Tab.ContentAreaBrush"))
		[
			SNew(SVerticalBox)

			+ SVerticalBox::Slot()
			.Padding(0, 10)
			[
				SNew(SVerticalBox)

				+ SVerticalBox::Slot()
				.AutoHeight()
				.Padding(0, 0, 0, 5)
				[
					SNew(STextBlock)
					.TextStyle(FEditorStyle::Get(), "NewClassDialog.PageTitle")
					.Text(LOCTEXT("LuaNameDescription", "从UI蓝图创建Lua"))
				]

				+ SVerticalBox::Slot()
				.AutoHeight()
				.Padding(0.0f)
				[
					SNew(SBorder)
					.Padding(FMargin(0.0f, 3.0f, 0.0f, 0.0f))
					.BorderImage(FEditorStyle::GetBrush("DetailsView.CategoryBottom"))
					.BorderBackgroundColor(FLinearColor(0.6f, 0.6f, 0.6f, 1.0f))
				]

				+ SVerticalBox::Slot()
				.AutoHeight()
				.Padding(0, 6)
				[
					SNew(SHorizontalBox)
					+ SHorizontalBox::Slot()
					.HAlign(HAlign_Left)
					[
						SNew(SHorizontalBox)
						+ SHorizontalBox::Slot()
						.HAlign(HAlign_Left)
						.VAlign(VAlign_Center)
						.Padding(0, 0, 0, 0)
						.AutoWidth()
						[
							SNew(STextBlock)
							.TextStyle(FEditorStyle::Get(), "NewClassDialog.SelectedParentClassLabel")
							.Text(LOCTEXT("Choose a Widget Blueprint:", "   选择UI蓝图"))
						]
						
						+ SHorizontalBox::Slot()
						.AutoWidth()
						.Padding(18, 0, 0, 0)
						[
							BPFilePathPicker.ToSharedRef()
						]
					]
				]

				+ SVerticalBox::Slot()
				.AutoHeight()
				.Padding(0, 6)
				[
					SNew(SHorizontalBox)

					+ SHorizontalBox::Slot()
					.HAlign(HAlign_Left)
					.VAlign(VAlign_Center)
					.Padding(0, 0, 0, 0)
					.AutoWidth()
					[
						SNew(STextBlock)
						.TextStyle(FEditorStyle::Get(), "NewClassDialog.SelectedParentClassLabel")
						.Text(LOCTEXT("Choose lua dir:", "选择LUA目录"))
					]

					+ SHorizontalBox::Slot()
					.Padding(17, 0, 0, 0)
					[
						SNew(SEditableTextBox)
						.Text(this, &SNewLuaDialog::OnGetLuaPathText)
						.OnTextChanged(this, &SNewLuaDialog::OnLuaPathTextChanged)
					]

					+ SHorizontalBox::Slot()
					.AutoWidth()
					.Padding(6.0f, 1.0f, 0.0f, 0.0f)
					[
						SNew(SButton)
						.VAlign(VAlign_Center)
						.OnClicked(this, &SNewLuaDialog::HandleChooseFolderButtonClicked)
						.Text(LOCTEXT("BrowseButtonText", "更换目录"))
					]
				]

				+ SVerticalBox::Slot()
				.AutoHeight()
				.Padding(0, 6)
				[
					SNew(SHorizontalBox)

					+ SHorizontalBox::Slot()
					.HAlign(HAlign_Left)
					.VAlign(VAlign_Center)
					.Padding(0, 0, 0, 0)
					.AutoWidth()
					[
						SNew(STextBlock)
						.TextStyle(FEditorStyle::Get(), "NewClassDialog.SelectedParentClassLabel")
						.Text(LOCTEXT("enter lua filename", "    LUA文件名"))
					]
					+ SHorizontalBox::Slot()
					.Padding(18, 0, 300, 0)
					[
						SNew(SEditableTextBox)
						.HintText(LOCTEXT("LuaFileNameHint", "bp_test"))
						.Text(this, &SNewLuaDialog::OnGetLuaFileNameText)
						.OnTextChanged(this, &SNewLuaDialog::OnLuaFileNameTextChanged)
					]
				]

				+ SVerticalBox::Slot()
				.AutoHeight()
				.Padding(0, 6)
				[
					SNew(SHorizontalBox)

					+ SHorizontalBox::Slot()
					.HAlign(HAlign_Left)
					.VAlign(VAlign_Center)
					.Padding(39, 0, 0, 0)
					.AutoWidth()
					[
						SNew(STextBlock)
						.TextStyle(FEditorStyle::Get(), "NewClassDialog.SelectedParentClassLabel")
						.Text(LOCTEXT("enter table name", "Table名"))
					]
					+ SHorizontalBox::Slot()
					.Padding(18, 0, 300, 0)
					[
						SNew(SEditableTextBox)
						.HintText(LOCTEXT("LuaTableNameHint", "TestUI"))
						.Text(this, &SNewLuaDialog::OnGetLuaTableNameText)
						.OnTextChanged(this, &SNewLuaDialog::OnLuaTableNameTextChanged)
					]
				]

				+ SVerticalBox::Slot()
				.AutoHeight()
				.Padding(0, 6)
				[
					SNew(SHorizontalBox)

					+ SHorizontalBox::Slot()
					.HAlign(HAlign_Left)
					.VAlign(VAlign_Center)
					.Padding(0, 0, 0, 0)
					.AutoWidth()
					[
						SNew(STextBlock)
						.TextStyle(FEditorStyle::Get(), "NewClassDialog.SelectedParentClassLabel")
						.Text(LOCTEXT("Choose ui zorder type", "       层级类型"))
					]
					+ SHorizontalBox::Slot()
					.Padding(18, 0, 0, 0)
					.AutoWidth()
					[
						// Button that controls the target for the snapshot operation
						SAssignNew(UIContainerTypeComboBox, SComboBox<TSharedPtr<FString>>)
						.ToolTipText(LOCTEXT("ChooseLuaUIContainerTypeToolTipText", "选择UI层级类型"))
						.OptionsSource(&UIContainerTypeTargets)
						.OnGenerateWidget(this, &SNewLuaDialog::HandleGenerateUIContainerComboItemWidget)
						.OnSelectionChanged(this, &SNewLuaDialog::HandleUIContainerComboSelectionChanged)
						[
							SNew(STextBlock)
							.Text(this, &SNewLuaDialog::GetSelectedUIContainerTargetDisplayName)
						]
					]
				]

				+ SVerticalBox::Slot()
				.AutoHeight()
				.Padding(0, 6)
				[
					SNew(SHorizontalBox)

					+ SHorizontalBox::Slot()
					.HAlign(HAlign_Left)
					.VAlign(VAlign_Center)
					.Padding(0, 0, 0, 0)
					.AutoWidth()
					[
						SNew(STextBlock)
						.TextStyle(FEditorStyle::Get(), "NewClassDialog.SelectedParentClassLabel")
						.Text(LOCTEXT("enter ui zorder", "           ZOrder"))
					]

					+ SHorizontalBox::Slot()
					.Padding(18, 0, 432, 0)
					[
						SNew(SEditableTextBox)
						.HintText(LOCTEXT("LuaUIZorderHint", "1-200"))
						.OnTextChanged(this, &SNewLuaDialog::OnLuaUIZOrderChanged)
					]
				]

				+ SVerticalBox::Slot()
				.AutoHeight()
				.Padding(0, 6)
				[
					SNew(SHorizontalBox)

					+ SHorizontalBox::Slot()
					.HAlign(HAlign_Left)
					.VAlign(VAlign_Center)
					.Padding(0, 0, 0, 0)
					.AutoWidth()
					[
						SNew(STextBlock)
						.TextStyle(FEditorStyle::Get(), "NewClassDialog.SelectedParentClassLabel")
						.Text(LOCTEXT("ui in gamestatus", "       出现场景"))
					]

					+ SHorizontalBox::Slot()
					.AutoWidth()
					.Padding(18, 0, 0, 0)
					[
						SNew(SCheckBox)
						.OnCheckStateChanged(this, &SNewLuaDialog::OnToggleStatusLobby)
						.IsChecked(this, &SNewLuaDialog::IsStatusLobbyChecked)
					]
					+ SHorizontalBox::Slot()
					.AutoWidth()
					[
						SNew(STextBlock)
						.TextStyle(FEditorStyle::Get(), "NewClassDialog.SelectedParentClassLabel")
						.Text(LOCTEXT("gamestatuslobby", "Lobby"))
					]
					+ SHorizontalBox::Slot()
					.AutoWidth()
					.Padding(18, 0, 0, 0)
					[
						SNew(SCheckBox)
						.OnCheckStateChanged(this, &SNewLuaDialog::OnToggleStatusLogin)
						.IsChecked(this, &SNewLuaDialog::IsStatusLoginChecked)
					]
					+ SHorizontalBox::Slot()
					.AutoWidth()
					[
						SNew(STextBlock)
						.TextStyle(FEditorStyle::Get(), "NewClassDialog.SelectedParentClassLabel")
						.Text(LOCTEXT("gamestatuslogin", "Login"))
					]
					+ SHorizontalBox::Slot()
					.AutoWidth()
					.Padding(18, 0, 0, 0)
					[
						SNew(SCheckBox)
						.OnCheckStateChanged(this, &SNewLuaDialog::OnToggleStatusFighting)
						.IsChecked(this, &SNewLuaDialog::IsStatusFightingChecked)
					]
					+ SHorizontalBox::Slot()
					.AutoWidth()
					[
						SNew(STextBlock)
						.TextStyle(FEditorStyle::Get(), "NewClassDialog.SelectedParentClassLabel")
						.Text(LOCTEXT("gamestatusfighting", "Fighting"))
					]

					+ SHorizontalBox::Slot()
					.AutoWidth()
					.Padding(18, 0, 0, 0)
					[
						SNew(SCheckBox)
						.OnCheckStateChanged(this, &SNewLuaDialog::OnToggleStatusTraining)
						.IsChecked(this, &SNewLuaDialog::IsStatusTrainingChecked)
					]
					+ SHorizontalBox::Slot()
					.AutoWidth()
					[
						SNew(STextBlock)
						.TextStyle(FEditorStyle::Get(), "NewClassDialog.SelectedParentClassLabel")
						.Text(LOCTEXT("gamestatustraining", "Training"))
					]
				]

				+ SVerticalBox::Slot()
				.AutoHeight()
				.Padding(0, 6)
				[
					SNew(SHorizontalBox)

					+ SHorizontalBox::Slot()
					.HAlign(HAlign_Left)
					.VAlign(VAlign_Center)
					.Padding(0, 0, 0, 0)
					.AutoWidth()
					[
						SNew(STextBlock)
						.TextStyle(FEditorStyle::Get(), "NewClassDialog.SelectedParentClassLabel")
						.Text(LOCTEXT("is concern gamestate", "关注场景切换"))
					]
					+ SHorizontalBox::Slot()
					.Padding(14, 0, 0, 0)
					[
						SNew(SCheckBox)
						.OnCheckStateChanged(this, &SNewLuaDialog::OnToggleConcernGameState)
						.IsChecked(this, &SNewLuaDialog::IsConcernGameStateChecked)
					]
				]

				+ SVerticalBox::Slot()
				.AutoHeight()
				.Padding(0, 6)
				[
					SNew(SHorizontalBox)

					+ SHorizontalBox::Slot()
					.HAlign(HAlign_Left)
					.VAlign(VAlign_Center)
					.Padding(0, 0, 0, 0)
					.AutoWidth()
					[
						SNew(STextBlock)
						.TextStyle(FEditorStyle::Get(), "NewClassDialog.SelectedParentClassLabel")
						.Text(LOCTEXT("is push panel", "    PushPanel"))
					]
					+ SHorizontalBox::Slot()
					.Padding(18, 0, 0, 0)
					[
						SNew(SCheckBox)
						.OnCheckStateChanged(this, &SNewLuaDialog::OnTogglePushPanel)
						.IsChecked(this, &SNewLuaDialog::IsPushPanelChecked)
					]
				]

				+ SVerticalBox::Slot()
				.AutoHeight()
				.Padding(FMargin(0, 0, 0, 0))
				[
					SNew(SBorder)
					.Padding(FMargin(0.0f, 1.0f, 0.0f, 0.0f))
					.BorderImage(FEditorStyle::GetBrush("DetailsView.CategoryBottom"))
					.BorderBackgroundColor(FLinearColor(0.8f, 0.8f, 0.8f, 0.6f))
				]

				+ SVerticalBox::Slot()
				.AutoHeight()
				.Padding(0, 6)
				[
					SNew(SHorizontalBox)

					+ SHorizontalBox::Slot()
					.HAlign(HAlign_Left)
					.VAlign(VAlign_Center)
					.Padding(0, 0, 0, 0)
					.AutoWidth()
					[
						SNew(STextBlock)
						.TextStyle(FEditorStyle::Get(), "NewClassDialog.SelectedParentClassLabel")
						.Text(LOCTEXT("create module entry", "创建Entry文件 "))
					]
					+ SHorizontalBox::Slot()
					.FillWidth(0.2f)
					.Padding(5, 0)
					[
						SNew(SCheckBox)
						.OnCheckStateChanged(this, &SNewLuaDialog::OnToggleCreateEntryFile)
						.IsChecked(this, &SNewLuaDialog::IsCreateEntryFileChecked)
					]
					+ SHorizontalBox::Slot()
					.Padding(4, 0, 300, 0)
					.FillWidth(1.5f)
					[
						SNew(SEditableTextBox)
						.HintText(LOCTEXT("LuaEntryFileNameHint", "Entry文件名称"))
						.Text(this, &SNewLuaDialog::OnGetLuaEntryNameText)
						.OnTextChanged(this, &SNewLuaDialog::OnLuaEntryNameTextChanged)
						.IsEnabled_Lambda([this]() { return IsCreateEntryFile; })
					]
				]

				+ SVerticalBox::Slot()
				.AutoHeight()
				.Padding(0, 6)
				[
					SNew(SHorizontalBox)

					+ SHorizontalBox::Slot()
					.HAlign(HAlign_Left)
					.VAlign(VAlign_Center)
					.Padding(0, 0, 0, 0)
					.AutoWidth()
					[
						SNew(STextBlock)
						.TextStyle(FEditorStyle::Get(), "NewClassDialog.SelectedParentClassLabel")
						.Text(LOCTEXT("create module logic", "创建Logic文件"))
					]
					+ SHorizontalBox::Slot()
					.FillWidth(0.2f)
					.Padding(7, 0)
					[
						SNew(SCheckBox)
						.OnCheckStateChanged(this, &SNewLuaDialog::OnToggleCreateLogicFile)
						.IsChecked(this, &SNewLuaDialog::IsCreateLogicFileChecked)
					]
					+ SHorizontalBox::Slot()
					.Padding(2, 0, 0, 0)
					.FillWidth(1.0f)
					[
						SNew(SEditableTextBox)
						.HintText(LOCTEXT("LuaLogicDirNameHint", "新目录名称"))
						.Text(this, &SNewLuaDialog::OnGetLuaLogicDirNameText)
						.OnTextChanged(this, &SNewLuaDialog::OnLuaLogicDirNameTextChanged)
						.IsEnabled_Lambda([this]() { return IsCreateLogicFile; })
					]
					+ SHorizontalBox::Slot()
					.VAlign(VAlign_Center)
					.AutoWidth()
					[
						SNew(STextBlock)
						.Text(LOCTEXT("LogicFilePath", "/"))
					]
					+ SHorizontalBox::Slot()
					.Padding(0, 0)
					.FillWidth(1.5f)
					[
						SNew(SEditableTextBox)
						.HintText(LOCTEXT("LuaLogicFileNameHint", "Logic文件名称"))
						.Text(this, &SNewLuaDialog::OnGetLuaLogicFileNameText)
						.OnTextChanged(this, &SNewLuaDialog::OnLuaLogicFileNameTextChanged)
						.IsEnabled_Lambda([this]() { return IsCreateLogicFile; })
					]
					+ SHorizontalBox::Slot()
					.Padding(4, 0)
					.FillWidth(1.5f)
					[
						SNew(SEditableTextBox)
						.HintText(LOCTEXT("LuaLogicTableNameHint", "Table名称"))
						.Text(this, &SNewLuaDialog::OnGetLuaLogicTableNameText)
						.OnTextChanged(this, &SNewLuaDialog::OnLuaLogicTableNameTextChanged)
						.IsEnabled_Lambda([this]() { return IsCreateLogicFile; })
					]
				]

				+ SVerticalBox::Slot()
				.AutoHeight()
				.Padding(FMargin(0, 2, 0, 0))
				[
					SNew(SBorder)
					.Padding(FMargin(0.0f, 3.0f, 0.0f, 0.0f))
					.BorderImage(FEditorStyle::GetBrush("DetailsView.CategoryBottom"))
					.BorderBackgroundColor(FLinearColor(0.6f, 0.6f, 0.6f, 1.0f))
				]

				+ SVerticalBox::Slot()
				.FillHeight(1.5f)
				.Padding(FMargin(10, 2, 0, 0))
				[
					SNew(SScrollBox)
					.Orientation(Orient_Vertical)
					+ SScrollBox::Slot()
					[
						SNew(SScrollBox)
						.Orientation(Orient_Horizontal)
						+ SScrollBox::Slot()
						[
							ResultText.ToSharedRef()
						]
					]
				]

				+ SVerticalBox::Slot()
				.FillHeight(1.2f)
				.Padding(FMargin(220, 2, 220, 0))
				[
					SNew(SButton)
					.VAlign(VAlign_Center)
					.HAlign(HAlign_Center)
					.Text(LOCTEXT("LuaFileDialogFinish", "创建文件"))
					.OnClicked(this, &SNewLuaDialog::HandleFinishClicked)
				]

				+ SVerticalBox::Slot()
				.AutoHeight()
				.Padding(FMargin(220, 10, 120, 0))
				[
					SNew(SBox)
					.HAlign(HAlign_Left)
					.VAlign(VAlign_Center)
					.Padding(FMargin(50.0f, 0.0f))
					[
						SNew(SHyperlink)
						.Text(LOCTEXT("LuaFileDialogMoreDemo", "更多代码示例..."))
						.OnNavigate_Lambda([]() {
#if PLATFORM_WINDOWS
					FWindowsPlatformMisc::OsExecute(TEXT("open"), *(FPaths::ConvertRelativePathToFull(FPaths::ProjectDir()) + TEXT("Source/Lua/client/module/umg/test")));
#endif
						})
					]
				]
			]
		]
	];

	UIContainerTypeComboBox->SetSelectedItem(UIContainerTypeTargets[0]);
}
END_SLATE_FUNCTION_BUILD_OPTIMIZATION


FText SNewLuaDialog::OnGetLuaPathText() const
{
	return FText::FromString(NewLuaPath);
}

void SNewLuaDialog::OnLuaPathTextChanged(const FText& NewText)
{
	NewLuaPath = NewText.ToString();
}

FText SNewLuaDialog::OnGetLuaFileNameText() const
{
	return FText::FromString(NewLuaName);
}

FText SNewLuaDialog::OnGetLuaTableNameText() const
{
	return FText::FromString(NewTableName);
}

void SNewLuaDialog::OnLuaFileNameTextChanged(const FText& NewText)
{
	NewLuaName = NewText.ToString();
	if (IsCreateEntryFile)
	{
		EntryFileName = NewLuaName.Replace(TEXT("bp_"), TEXT("")) + TEXT("_entry");
	}
	if (IsCreateLogicFile)
	{
		LogicFileName = TEXT("logic_") + NewLuaName.Replace(TEXT("bp_"), TEXT(""));
	}
}

void SNewLuaDialog::OnLuaTableNameTextChanged(const FText& NewText)
{
	NewTableName = NewText.ToString();
	if (IsCreateLogicFile)
	{
		LogicDirName = NewLuaName.Replace(TEXT("bp_"), TEXT(""));
		LogicTableName = NewTableName + TEXT("System");
	}
}

void SNewLuaDialog::OnLuaUIZOrderChanged(const FText& NewText)
{
	UIZOrder = NewText.ToString();
}

void SNewLuaDialog::HandleBPFilePathPickerPathPicked(const FString& InOutPath)
{
	FString FilePath = InOutPath.Replace(TEXT("\\"), TEXT("/"));
	if (!FilePath.IsEmpty())
	{
		FString SLeft, SRight;
		if (FilePath.Split(TEXT("/Content/"), &SLeft, &SRight))
			BPPath = TEXT("/Game/") + SRight.Replace(TEXT(".uasset"), TEXT(""));
		else
			BPPath = FilePath;
	}
}

FReply SNewLuaDialog::HandleChooseFolderButtonClicked()
{
	IDesktopPlatform* DesktopPlatform = FDesktopPlatformModule::Get();
	if (DesktopPlatform)
	{
		TSharedPtr<SWindow> ParentWindow = FSlateApplication::Get().FindWidgetWindow(AsShared());
		void* ParentWindowWindowHandle = (ParentWindow.IsValid()) ? ParentWindow->GetNativeWindow()->GetOSWindowHandle() : nullptr;

		FString FolderName;
		const FString Title = LOCTEXT("NewLuaBrowseTitle", "Choose a source location").ToString();
		const bool bFolderSelected = DesktopPlatform->OpenDirectoryDialog(
			ParentWindowWindowHandle,
			Title,
			NewLuaPath,
			FolderName
		);
		if (bFolderSelected)
		{
			if (!FolderName.EndsWith(TEXT("/")))
			{
				FolderName += TEXT("/");
			}
			NewLuaPath = FolderName;
		}
	}
	return FReply::Handled();
}

TSharedRef<SWidget> SNewLuaDialog::HandleGenerateUIContainerComboItemWidget(TSharedPtr<FString> InItem) const
{
	return SNew(STextBlock)
		.Text(FText::FromString(*InItem));
}

void SNewLuaDialog::HandleUIContainerComboSelectionChanged(TSharedPtr<FString> InItem, ESelectInfo::Type InSeletionInfo)
{
	if (!InItem.IsValid())
		return;
	SelectedUIContainerTarget = *InItem;
}

FText SNewLuaDialog::GetSelectedUIContainerTargetDisplayName() const
{
	if (UIContainerTypeComboBox.IsValid())
	{
		TSharedPtr<FString> SelectedTarget = UIContainerTypeComboBox->GetSelectedItem();
		if (SelectedTarget.IsValid())
		{
			return FText::FromString(*SelectedTarget);
		}
	}
	return FText::GetEmpty();
}

void SNewLuaDialog::OnToggleStatusLogin(ECheckBoxState CheckType)
{
	IsStatusLogin = CheckType == ECheckBoxState::Checked;
}
ECheckBoxState SNewLuaDialog::IsStatusLoginChecked() const
{
	return IsStatusLogin ? ECheckBoxState::Checked : ECheckBoxState::Unchecked;
}
void SNewLuaDialog::OnToggleStatusFighting(ECheckBoxState CheckType)
{
	IsStatusFighting = CheckType == ECheckBoxState::Checked;
}
ECheckBoxState SNewLuaDialog::SNewLuaDialog::IsStatusFightingChecked() const
{
	return IsStatusFighting ? ECheckBoxState::Checked : ECheckBoxState::Unchecked;
}
void SNewLuaDialog::OnToggleStatusLobby(ECheckBoxState CheckType)
{
	IsStatusLobby = CheckType == ECheckBoxState::Checked;
}
ECheckBoxState SNewLuaDialog::IsStatusLobbyChecked() const
{
	return IsStatusLobby ? ECheckBoxState::Checked : ECheckBoxState::Unchecked;
}

void SNewLuaDialog::OnToggleStatusTraining(ECheckBoxState CheckType)
{
	IsStatusTraining = CheckType == ECheckBoxState::Checked;
}
ECheckBoxState SNewLuaDialog::SNewLuaDialog::IsStatusTrainingChecked() const
{
	return IsStatusTraining ? ECheckBoxState::Checked : ECheckBoxState::Unchecked;
}

void SNewLuaDialog::OnToggleConcernGameState(ECheckBoxState CheckType)
{
	IsConcernGameState = CheckType == ECheckBoxState::Checked;
}

ECheckBoxState SNewLuaDialog::IsConcernGameStateChecked() const
{
	return IsConcernGameState ? ECheckBoxState::Checked : ECheckBoxState::Unchecked;
}

void SNewLuaDialog::OnTogglePushPanel(ECheckBoxState CheckType)
{
	IsPushPanel = CheckType == ECheckBoxState::Checked;
}

ECheckBoxState SNewLuaDialog::IsPushPanelChecked() const
{
	return IsPushPanel ? ECheckBoxState::Checked : ECheckBoxState::Unchecked;
}

void SNewLuaDialog::OnToggleCreateEntryFile(ECheckBoxState CheckType)
{
	IsCreateEntryFile = CheckType == ECheckBoxState::Checked;
	EntryFileName = "";
	if (IsCreateEntryFile)
	{
		EntryFileName = NewLuaName.Replace(TEXT("bp_"), TEXT("")) + TEXT("_entry");
	}
}

ECheckBoxState SNewLuaDialog::IsCreateEntryFileChecked() const
{
	return IsCreateEntryFile ? ECheckBoxState::Checked : ECheckBoxState::Unchecked;
}

void SNewLuaDialog::OnToggleCreateLogicFile(ECheckBoxState CheckType)
{
	IsCreateLogicFile = CheckType == ECheckBoxState::Checked;
	LogicDirName = "";
	LogicFileName = "";
	LogicTableName = "";
	if (IsCreateLogicFile)
	{
		LogicDirName = NewLuaName.Replace(TEXT("bp_"), TEXT(""));
		LogicFileName = TEXT("logic_") + NewLuaName.Replace(TEXT("bp_"), TEXT(""));
		LogicTableName = NewTableName + TEXT("System");
	}
}

ECheckBoxState SNewLuaDialog::IsCreateLogicFileChecked() const
{
	return IsCreateLogicFile ? ECheckBoxState::Checked : ECheckBoxState::Unchecked;
}

FText SNewLuaDialog::OnGetLuaEntryNameText() const
{
	return FText::FromString(EntryFileName);
}

void SNewLuaDialog::OnLuaEntryNameTextChanged(const FText& NewText)
{
	EntryFileName = NewText.ToString();
}

FText SNewLuaDialog::OnGetLuaLogicDirNameText() const
{
	return FText::FromString(LogicDirName);
}

void SNewLuaDialog::OnLuaLogicDirNameTextChanged(const FText& NewText)
{
	LogicDirName = NewText.ToString();
}

FText SNewLuaDialog::OnGetLuaLogicFileNameText() const
{
	return FText::FromString(LogicFileName);
}

void SNewLuaDialog::OnLuaLogicFileNameTextChanged(const FText& NewText)
{
	LogicFileName = NewText.ToString();
}

FText SNewLuaDialog::OnGetLuaLogicTableNameText() const
{
	return FText::FromString(LogicTableName);
}

void SNewLuaDialog::OnLuaLogicTableNameTextChanged(const FText& NewText)
{
	LogicTableName = NewText.ToString();
}

void SNewLuaDialog::CloseContainingWindow()
{
	FWidgetPath WidgetPath;
	TSharedPtr<SWindow> ContainingWindow = FSlateApplication::Get().FindWidgetWindow(AsShared(), WidgetPath);

	if (ContainingWindow.IsValid())
	{
		ContainingWindow->RequestDestroyWindow();
	}
}

void SNewLuaDialog::GetUIWidgetInfo()
{
	UObject* LoadObj = StaticLoadObject(UBlueprint::StaticClass(), NULL, *BPPath);
	UBlueprint* BlueprintObj = nullptr;
	if (LoadObj)
	{
		BlueprintObj = Cast<UBlueprint>(LoadObj);
	}

	if (BlueprintObj == nullptr)
	{
		ResultText.Get()->SetText(FText::FromString(BPPath + TEXT(" 蓝图加载失败!")));
		return;
	}

	EFieldIteratorFlags::SuperClassFlags FieldIteratorSuperFlag = EFieldIteratorFlags::ExcludeSuper;
	// Grab Variables
	for (TFieldIterator<FProperty> PropertyIt(BlueprintObj->SkeletonGeneratedClass, FieldIteratorSuperFlag); PropertyIt; ++PropertyIt)
	{
		FProperty* Property = *PropertyIt;
		FObjectPropertyBase* Obj = CastField<FObjectPropertyBase>(Property);
		if (Obj && Obj->PropertyClass)
		{
			UE_LOG(LogTemp, Warning, TEXT("PropertyName %s %s"), *Property->GetName(), *Obj->PropertyClass->GetDefaultObjectName().ToString());
			UIWidgetInfo.Add(Property->GetName(), Obj->PropertyClass->GetDefaultObjectName().ToString());
		}
	}
}

void SNewLuaDialog::GetStringFromUI(FString& UIEventAdd, FString& UIEventRemove, FString& InitUI, FString& FuncAdd)
{
	for (auto& Elem : UIWidgetInfo)
	{
		if (Elem.Value == "Default__Button" || Elem.Value == "Default__NewButton")
		{
			UIEventRemove.Append("self.uiObj." + Elem.Key + ".OnClicked:RemoveAll()\n	");
			UIEventAdd.Append("self.uiObj." + Elem.Key + ".OnClicked:Add(self.On_Clicked_" + Elem.Key + ", self)\n	");
			FuncAdd.Append("\nfunction "+ NewTableName +":On_Clicked_" + Elem.Key + "()\n\nend\n");
		}
		else if (Elem.Value == "Default__CheckBox")
		{
			UIEventRemove.Append("self.uiObj." + Elem.Key + ".OnCheckStateChanged:RemoveAll()\n	");
			UIEventAdd.Append("self.uiObj." + Elem.Key + ".OnCheckStateChanged:Add(self.On_Check_State_Changed_" + Elem.Key + ", self)\n	");
			FuncAdd.Append("\nfunction " + NewTableName + ":On_Check_State_Changed_" + Elem.Key + "(isChecked)\n\nend\n");
		}
		else if (Elem.Key.Contains("Common_Avatar_BP"))
		{
			UIEventRemove.Append("self.uiObj." + Elem.Key + ".OnClickItemCallback:RemoveAll()\n	");
			UIEventAdd.Append("self.uiObj."+ Elem.Key +".OnClickItemCallback:Add(self.OnClickHeadCallBack, self)\n	");
			InitUI.Append("\n	self.uiObj." + Elem.Key + ":InitView(1, profile.uid, profile.picUrl, profile.sex, profile.cur_avatar_box_id, profile.level)\n	");
			FuncAdd.Append("\nfunction " + NewTableName + ":OnClickHeadCallBack()\n\nend\n");
		}
		else if (Elem.Key.Contains("Common_RankTitleInfo_UIBP"))
		{
			InitUI.Append("\n	self.uiObj." + Elem.Key + ":SetRankInfo(profile.cur_max_segment_level, false)\n	");
		}
		else if (Elem.Key.Contains("CharismaValue_Style2_UIBP"))
		{
			InitUI.Append("\n	CharismaItem:StaticSet_Style2(self.uiObj." + Elem.Key + ", charisma_value, charisma_level, true, charisma_allow_view)\n	");
		}
		else if (Elem.Value == "Default__DynaCanvasPanel")
		{
			InitUI.Append("\n	local dynaWidget = self.uiObj." + Elem.Key + ":GetDynaWidget(\"DynaWidget_0\")\n	");
		}
		else if (Elem.Value == "Default__ComboBox_Group_BP_C")
		{
			InitUI.Append("\n	self.ComboboxGroup = ComboBoxGroupUI:New(self.uiObj, self.uiObj." + Elem.Key + ")\n	");
		}
		else if (Elem.Value == "Default__ComboBox_BP_C")
		{
			InitUI.Append("\n	self.ComboBoxUI = self.ComboboxGroup:Register(self.uiObj." + Elem.Key + ")\n	self.ComboBoxUI:AddCallBackEvent(self.OnSelectComboBox)\n");
			FuncAdd.Append("\nfunction " + NewTableName + ".OnSelectComboBox(index)\n\nend\n");
		}
		else if (Elem.Value == "Default__NewLuaNameReuseList_C" || Elem.Value == "Default__ReuseList2_C" || Elem.Value == "Default__ReuseList3_C")
		{
			UIEventRemove.Append("self.uiObj." + Elem.Key + ".OnCreateItem:RemoveAll()\n	");
			UIEventRemove.Append("self.uiObj." + Elem.Key + ".OnUpdateItem:RemoveAll()\n	");
			UIEventAdd.Append("self.uiObj." + Elem.Key + ".OnCreateItem:Add(self.OnCreateItem_" + Elem.Key + ", self)\n	");
			UIEventAdd.Append("self.uiObj." + Elem.Key + ".OnUpdateItem:Add(self.OnUpdateItem_" + Elem.Key + ", self)\n	");
			FuncAdd.Append("\nfunction " + NewTableName + ":OnCreateItem_" + Elem.Key + "()\n\nend\n");
			FuncAdd.Append("\nfunction " + NewTableName + ":OnUpdateItem_" + Elem.Key + "()\n\nend\n");
		}
		else if (Elem.Value == "Default__ReuseListSp_C" || Elem.Value == "Default__ReuseListDiffTest_C")
		{
			UIEventRemove.Append("self.uiObj." + Elem.Key + ".OnPreUpdateItem:RemoveAll()\n	");
			UIEventRemove.Append("self.uiObj." + Elem.Key + ".OnUpdateItem:RemoveAll()\n	");
			UIEventAdd.Append("self.uiObj." + Elem.Key + ".OnPreUpdateItem:Add(self.OnPreUpdateItem_" + Elem.Key + ", self)\n	");
			UIEventAdd.Append("self.uiObj." + Elem.Key + ".OnUpdateItem:Add(self.OnUpdateItem_" + Elem.Key + ", self)\n	");
			FuncAdd.Append("\nfunction " + NewTableName + ":OnPreUpdateItem_" + Elem.Key + "()\n\nend\n");
			FuncAdd.Append("\nfunction " + NewTableName + ":OnUpdateItem_" + Elem.Key + "()\n\nend\n");
		}
	}
}

FString SNewLuaDialog::CreateEntryFile(FString& ResultString)
{
	if (!IsCreateEntryFile)
		return "";
	
	FString RelativeDir = FPaths::ConvertRelativePathToFull(FPaths::ProjectDir());
	FString TemplateFilePath = RelativeDir + TEXT("Source/Lua/client/module/system/test/test_entry.txt");
	FString TemplateContents;
	FFileHelper::LoadFileToString(TemplateContents, *TemplateFilePath);
	if (TemplateContents.Len() == 0)
	{
		ResultString.Append(TEXT("模板文件缺失!") + TemplateFilePath);
		return "";
	}
	if (EntryFileName.Len() == 0)
	{
		ResultString.Append(TEXT("Entry文件名不能空!!"));
		return "";
	}
	TArray<FStringFormatArg> FormatArray;
	FormatArray.Add(FStringFormatArg(LogicDirName));
	FormatArray.Add(FStringFormatArg(LogicFileName));
	FormatArray.Add(FStringFormatArg(LogicTableName));
	FormatArray.Add(FStringFormatArg(LogicDirName.ToUpper()));
	FormatArray.Add(FStringFormatArg(LogicTableName));

	FString Content = FString::Format(*TemplateContents, FormatArray);
	FString AbsoluteFilePath = RelativeDir + TEXT("Source/Lua/client/module/entry/") + EntryFileName + ".lua";
	FFileHelper::EEncodingOptions EncodingOptions = FFileHelper::EEncodingOptions::ForceUTF8WithoutBOM;
	uint32 WriteFlags = (uint32)(EFileWrite::FILEWRITE_None);
	if (FFileHelper::SaveStringToFile(Content, *AbsoluteFilePath, EncodingOptions, &IFileManager::Get(), WriteFlags))
	{
		ResultString.Append(AbsoluteFilePath + TEXT("文件已生成!\n"));
		return AbsoluteFilePath;
	}
	else
	{
		ResultString.Append(AbsoluteFilePath + TEXT("文件生成失败!\n"));
	}
	return "";
}

FString SNewLuaDialog::CreateLogicFile(FString& ResultString)
{
	if (!IsCreateLogicFile)
		return "";

	if (LogicFileName.Len() == 0)
	{
		ResultString.Append(TEXT("Logic文件名不能空!!"));
		return "";
	}
	if (LogicDirName.Len() == 0)
	{
		ResultString.Append(TEXT("LogicDir目录名不能空!!"));
		return "";
	}
	if (LogicTableName.Len() == 0)
	{
		ResultString.Append(TEXT("LogicTable名不能空!!"));
		return "";
	}
	FString RelativeDir = FPaths::ConvertRelativePathToFull(FPaths::ProjectDir());
	FString TemplateFilePath = RelativeDir + TEXT("Source/Lua/client/module/system/test/test_logic.txt");
	FString TemplateContents;
	FFileHelper::LoadFileToString(TemplateContents, *TemplateFilePath);
	if (TemplateContents.Len() == 0)
	{
		ResultString.Append(TEXT("模板文件缺失!") + TemplateFilePath);
		return "";
	}

	FString Content = TemplateContents.Replace(TEXT("[Test]"), *LogicTableName);
	FString AbsoluteFilePath = RelativeDir + TEXT("Source/Lua/client/module/logic/")+ LogicDirName +TEXT("/")+ LogicFileName + ".lua";
	FFileHelper::EEncodingOptions EncodingOptions = FFileHelper::EEncodingOptions::ForceUTF8WithoutBOM;
	uint32 WriteFlags = (uint32)(EFileWrite::FILEWRITE_None);
	if (FFileHelper::SaveStringToFile(Content, *AbsoluteFilePath, EncodingOptions, &IFileManager::Get(), WriteFlags))
	{
		ResultString.Append(AbsoluteFilePath + TEXT("文件已生成!\n"));
		return AbsoluteFilePath;
	}
	else
	{
		ResultString.Append(AbsoluteFilePath + TEXT("文件生成失败!\n"));
	}
	return "";
}

FReply SNewLuaDialog::HandleFinishClicked()
{
	FString TemplateFilePath = FPaths::ConvertRelativePathToFull(FPaths::ProjectDir()) + TEXT("Source/Lua/client/module/system/test/test_bp.txt");
	FString TemplateFileContents;
	FFileHelper::LoadFileToString(TemplateFileContents, *TemplateFilePath);
	if (TemplateFileContents.Len() == 0)
	{
		FString Msg = TEXT("模板文件缺失!") + TemplateFilePath;
		ResultText.Get()->SetText(FText::FromString(Msg));
		return FReply::Handled();
	}
	FString LuaFilePath = NewLuaPath + "/" + NewLuaName;
	IFileManager* FileManager = &IFileManager::Get();
	if (FileManager->FileExists(*LuaFilePath))
	{
		ResultText.Get()->SetText(FText::FromString(TEXT("文件已存在!")));
		return FReply::Handled();
	}
	if (NewLuaName.Len() == 0)
	{
		ResultText.Get()->SetText(FText::FromString(TEXT("LUA文件名不能空!")));
		return FReply::Handled();
	}
	if (NewTableName.Len() == 0)
	{
		ResultText.Get()->SetText(FText::FromString(TEXT("Table名不能空!")));
		return FReply::Handled();
	}
	if (UIZOrder.Len() == 0)
	{
		ResultText.Get()->SetText(FText::FromString(TEXT("ZOrder不能空!")));
		return FReply::Handled();
	}
	if (!UIZOrder.IsNumeric())
	{
		ResultText.Get()->SetText(FText::FromString(TEXT("ZOrder需填数字!")));
		return FReply::Handled();
	}
	if (!IsStatusLobby && !IsStatusFighting && !IsStatusLogin && !IsStatusTraining)
	{
		ResultText.Get()->SetText(FText::FromString(TEXT("需勾选出现场景!")));
		return FReply::Handled();
	}
	int32 index = 0;
	if (!BPPath.FindLastChar(TEXT('/'), index))
	{
		ResultText.Get()->SetText(FText::FromString(TEXT("蓝图路径错误!")));
		return FReply::Handled();
	}
	if (IsCreateEntryFile && EntryFileName.Len() == 0)
	{
		ResultText.Get()->SetText(FText::FromString(TEXT("Entry文件名不能空!")));
		return FReply::Handled();
	}
	if (IsCreateLogicFile && LogicFileName.Len() == 0)
	{
		ResultText.Get()->SetText(FText::FromString(TEXT("Logic文件名不能空!")));
		return FReply::Handled();
	}

	GetUIWidgetInfo();

	FString ContentTmp = TemplateFileContents.Replace(TEXT("bp_test"), *NewLuaName);
	ContentTmp = ContentTmp.Replace(TEXT("TestUI"), *NewTableName);
	FString UIName = BPPath.Right(BPPath.Len() - index - 1) + TEXT("_C");
	TArray<FStringFormatArg> FormatArray;

	//{0}全局存在？
	if (IsStatusFighting || IsStatusTraining)
		FormatArray.Add(FStringFormatArg(TEXT("--")));
	else
		FormatArray.Add(FStringFormatArg(TEXT("")));
	//{1}蓝图路径
	FormatArray.Add(FStringFormatArg(TEXT("\"") + BPPath + TEXT(".") + UIName + TEXT("\"")));
	//{2}容器层级
	FormatArray.Add(FStringFormatArg(TEXT("\"") + SelectedUIContainerTarget + TEXT("\"")));
	//{3}zorder
	FormatArray.Add(FStringFormatArg(UIZOrder));
	//{4}存在场景
	FString status = "";
	if (IsStatusLogin)
		status.Append(TEXT("\"Login\""));
	if (IsStatusLobby)
	{
		if (status.Len() > 0)
			status.Append(TEXT(", "));
		status.Append(TEXT("\"Lobby\""));
	}
	if (IsStatusFighting)
	{
		if (status.Len() > 0)
			status.Append(TEXT(", "));
		status.Append(TEXT("\"Fighting\""));
	}
	if (IsStatusTraining)
	{
		if (status.Len() > 0)
			status.Append(TEXT(", "));
		status.Append(TEXT("\"Training\""));
	}
	FString UIEventAdd = "";
	FString UIEventRemove = "";
	FString InitUI = "";
	FString FuncAdd = "";
	GetStringFromUI(UIEventAdd, UIEventRemove, InitUI, FuncAdd);
	FormatArray.Add(FStringFormatArg(status));
	//{5}关注场景切换
	if (IsConcernGameState)
		FormatArray.Add(FStringFormatArg(TEXT("true")));
	else
		FormatArray.Add(FStringFormatArg(TEXT("false")));
	//{6}UI名字
	FormatArray.Add(FStringFormatArg(TEXT("\"") + UIName + TEXT("\"")));
	//{7}关注场景切换
	FString Temp = "";
	if(IsConcernGameState)
		Temp.Append(TEXT("\nfunction ") + NewLuaName + TEXT("_OnModeSwitched(gamestatus)\n\nend\n"));
	if (IsPushPanel)
	{
		Temp.Append(TEXT("\nfunction ") + NewLuaName + TEXT("_OnAfterCover()\n\nend\n"));
		Temp.Append(TEXT("\nfunction ") + NewLuaName + TEXT("_OnAfterRecover()\n\nend\n"));
		Temp.Append(TEXT("\nfunction ") + NewLuaName + TEXT("_OnUIStackRecover(src_tag, dst_tag)\n\nend\n"));
	}
	FormatArray.Add(FStringFormatArg(Temp));
	
	//{8}BindUIEvent
	FormatArray.Add(FStringFormatArg(UIEventAdd));
	//{9}RemoveUIEvent
	FormatArray.Add(FStringFormatArg(UIEventRemove));
	//{10}InitUI
	FormatArray.Add(FStringFormatArg(InitUI));
	//{11}ReleaseUI
	FormatArray.Add(FStringFormatArg(""));
	//{12}PushPanel
	if (IsPushPanel)
	{
		FormatArray.Add(FStringFormatArg("GlobalData.PushPanel(" + NewLuaName + ", \"\", \"" + NewTableName + "\")"));
	}
	else
	{
		FormatArray.Add(FStringFormatArg(""));
	}
	//{13}RefreshUI
	FormatArray.Add(FStringFormatArg(""));
	//{14}代理函数
	FormatArray.Add(FStringFormatArg(FuncAdd));

	bool isUMGCreated = false;
	FString ResultString = "";
	FString Content = FString::Format(*ContentTmp, FormatArray);
	FString UMGFilePath = NewLuaPath + NewLuaName + ".lua";
	FFileHelper::EEncodingOptions EncodingOptions = FFileHelper::EEncodingOptions::ForceUTF8WithoutBOM;
	uint32 WriteFlags = (uint32)(EFileWrite::FILEWRITE_None);
	if (FFileHelper::SaveStringToFile(Content, *UMGFilePath, EncodingOptions, FileManager, WriteFlags))
	{
		ResultString.Append(UMGFilePath + TEXT("文件已生成!\n"));
		isUMGCreated = true;
	}
	else
	{
		ResultString.Append(UMGFilePath + TEXT("文件生成失败!\n"));
	}

	FString EntryFilePath = CreateEntryFile(ResultString);
	FString LogicFilePath = CreateLogicFile(ResultString);
	ResultText.Get()->SetText(FText::FromString(ResultString));
	
	if (isUMGCreated)
	{
#if PLATFORM_WINDOWS
		FWindowsPlatformMisc::OsExecute(TEXT("open"), TEXT("Code"), *(TEXT("-g ") + UMGFilePath));
#endif
	}
	if (IsCreateEntryFile && EntryFilePath.Len() > 0)
	{
#if PLATFORM_WINDOWS
		FWindowsPlatformMisc::OsExecute(TEXT("open"), TEXT("Code"), *(TEXT("-g ") + EntryFilePath));
#endif
	}
	if (IsCreateLogicFile && LogicFilePath.Len() > 0)
	{
#if PLATFORM_WINDOWS
		FWindowsPlatformMisc::OsExecute(TEXT("open"), TEXT("Code"), *(TEXT("-g ") + LogicFilePath));
#endif
	}
	return FReply::Handled();
}
#undef LOCTEXT_NAMESPACE
