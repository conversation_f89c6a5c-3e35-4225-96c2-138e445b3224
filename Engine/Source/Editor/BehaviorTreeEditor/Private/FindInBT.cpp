// Copyright Epic Games, Inc. All Rights Reserved.
#include "FindInBT.h"

#include "BehaviorTreeEditor.h"
#include "BehaviorTreeGraphNode.h"
#include "BehaviorTreeGraphNode_Decorator.h"
#include "BehaviorTreeGraphNode_Service.h"
#include "EdGraph/EdGraph.h"
#include "EdGraph/EdGraphNode.h"
#include "Framework/Application/SlateApplication.h"
#include "Framework/Views/ITypedTableView.h"
#include "GraphEditor.h"
#include "HAL/PlatformMath.h"
#include "Input/Events.h"
#include "Internationalization/Internationalization.h"
#include "Layout/Children.h"
#include "Layout/WidgetPath.h"
#include "Math/Color.h"
#include "Misc/Attribute.h"
#include "SlotBase.h"
#include "Styling/AppStyle.h"
#include "Styling/SlateColor.h"
#include "Templates/Casts.h"
#include "Types/SlateStructs.h"
#include "UObject/Class.h"
#include "UObject/ObjectPtr.h"
#include "Widgets/Images/SImage.h"
#include "Widgets/Input/SSearchBox.h"
#include "Widgets/Layout/SBorder.h"
#include "Widgets/Layout/SBox.h"
#include "Widgets/Layout/SScrollBox.h"
#include "Widgets/SBoxPanel.h"
#include "Widgets/Text/STextBlock.h"
#include "Widgets/Views/STableRow.h"
#include "Engine/ObjectLibrary.h"
#include "BehaviorTree/BehaviorTree.h"
#include "Framework/MultiBox/MultiBoxBuilder.h"
#include "FindInAllBT.h"

class ITableRow;
class SWidget;
struct FSlateBrush;

#define LOCTEXT_NAMESPACE "FindInBT"

//////////////////////////////////////////////////////////////////////////
// FFindInBTResult

FFindInBTResult::FFindInBTResult(const FString& InValue) 
	: Value(InValue), GraphNode(NULL)
{
}

FFindInBTResult::FFindInBTResult(const FString& InValue, TSharedPtr<FFindInBTResult>& InParent, UEdGraphNode* InNode)
	: Value(InValue), GraphNode(InNode), Parent(InParent)
{
}

void FFindInBTResult::SetNodeHighlight(bool bHighlight)
{
	if (GraphNode.IsValid())
	{
		UBehaviorTreeGraphNode* BTNode = Cast<UBehaviorTreeGraphNode>(GraphNode.Get());
		if (BTNode)
		{
			BTNode->bHighlightInSearchTree = bHighlight;
		}
	}
}

TSharedRef<SWidget> FFindInBTResult::CreateIcon() const
{
	FSlateColor IconColor = FSlateColor::UseForeground();
	const FSlateBrush* Brush = NULL;

	if (GraphNode.IsValid())
	{
		if (Cast<UBehaviorTreeGraphNode_Service>(GraphNode.Get()))
		{
			Brush = FAppStyle::GetBrush(TEXT("GraphEditor.PinIcon"));
		}
		else if (Cast<UBehaviorTreeGraphNode_Decorator>(GraphNode.Get()))
		{
			Brush = FAppStyle::GetBrush(TEXT("GraphEditor.RefPinIcon"));
		}
		else
		{
			Brush = FAppStyle::GetBrush(TEXT("GraphEditor.FIB_Event"));
		}
	}
	
	return SNew(SImage)
		.Image(Brush)
		.ColorAndOpacity(IconColor);
}

FReply FFindInBTResult::OnClick(TWeakPtr<FBehaviorTreeEditor> BehaviorTreeEditor, TSharedPtr<FFindInBTResult> Root)
{
	const TSharedPtr<FBehaviorTreeEditor> BTEditorAsShared = BehaviorTreeEditor.Pin();
	FBehaviorTreeEditor* BTEditorPtr = BTEditorAsShared.Get();

	const TSharedPtr<FFindInBTResult> ParentAsShared = Parent.Pin();
	const FFindInBTResult* ParentPtr = ParentAsShared.Get();

	// @SStudio clickwang - BEGIN: by 239683 搜索结果点击时的跳转功能
	if (BTEditorPtr != nullptr && GraphNode.IsValid())
	{
		if (Parent.IsValid() && ParentAsShared == Root)
		{
			BTEditorPtr->JumpToNode(GraphNode.Get());
		}
		else
		{
			if (ParentPtr != nullptr)
			{
				BTEditorPtr->JumpToNode(ParentPtr->GraphNode.Get());
			}
			else
			{
				BTEditorPtr->JumpToNode(GraphNode.Get());
			}
		}
	}
	// @SStudio clikcwang - END

	return FReply::Handled();
}

FString FFindInBTResult::GetNodeTypeText() const
{
	if (GraphNode.IsValid())
	{
		FString NodeClassName = GraphNode->GetClass()->GetName();
		int32 Pos = NodeClassName.Find("_");
		if (Pos == INDEX_NONE)
		{
			return NodeClassName;
		}
		else
		{
			return NodeClassName.RightChop(Pos + 1);
		}
	}

	return FString();
}

FString FFindInBTResult::GetCommentText() const
{
	if (GraphNode.IsValid())
	{
		return GraphNode->NodeComment;
	}

	return FString();
}

//////////////////////////////////////////////////////////////////////////
// SFindInBT

void SFindInBT::Construct( const FArguments& InArgs, TSharedPtr<FBehaviorTreeEditor> InBehaviorTreeEditor)
{
	BehaviorTreeEditorPtr = InBehaviorTreeEditor;

	this->ChildSlot
		[
			SNew(SVerticalBox)
			+SVerticalBox::Slot()
			.AutoHeight()
			[
				SNew(SHorizontalBox)
				// @SStudio clickwang - BEGIN: 【编辑器】 完善行为树编辑器搜索功能
				+ SHorizontalBox::Slot()
				.HAlign(HAlign_Left)
				.AutoWidth()
				[
					SNew(SComboButton)
						.ContentPadding(FMargin(2.0f, 2.0f))
						.OnGetMenuContent(this, &SFindInBT::OnGetSearchTypeContent)
						.ButtonContent()
						[
							SNew(STextBlock)
								.Text(this, &SFindInBT::GetSearchTypeName)
						]
				]
				// @SStudio clikcwang - END
				+SHorizontalBox::Slot()
				.FillWidth(1)
				[
					SAssignNew(SearchTextField, SSearchBox)
					.HintText(LOCTEXT("BehaviorTreeSearchHint", "Enter text to find nodes..."))
					.OnTextChanged(this, &SFindInBT::OnSearchTextChanged)
					.OnTextCommitted(this, &SFindInBT::OnSearchTextCommitted)
				]
			]
			+SVerticalBox::Slot()
			.FillHeight(1.0f)
			.Padding(0.f, 4.f, 0.f, 0.f)
			[
				SNew(SBorder)
				.BorderImage(FAppStyle::GetBrush("Menu.Background"))
				[
					SAssignNew(TreeView, STreeViewType)
					.ItemHeight(24)
					.TreeItemsSource(&ItemsFound)
					.OnGenerateRow(this, &SFindInBT::OnGenerateRow)
					.OnGetChildren(this, &SFindInBT::OnGetChildren)
					.OnSelectionChanged(this, &SFindInBT::OnTreeSelectionChanged)
					.SelectionMode(ESelectionMode::Multi)
				]
			]
		];

	// @SStudio clickwang - BEGIN: 【编辑器】 完善行为树编辑器搜索功能
	SearchType = EFindInBTSearchType::LocalNode;
	// @SStudio clikcwang - END
}

void SFindInBT::FocusForUse()
{
	// NOTE: Careful, GeneratePathToWidget can be reentrant in that it can call visibility delegates and such
	FWidgetPath FilterTextBoxWidgetPath;
	FSlateApplication::Get().GeneratePathToWidgetUnchecked(SearchTextField.ToSharedRef(), FilterTextBoxWidgetPath);

	// Set keyboard focus directly
	FSlateApplication::Get().SetKeyboardFocus(FilterTextBoxWidgetPath, EFocusCause::SetDirectly);
}

void SFindInBT::OnSearchTextChanged(const FText& Text)
{
	SearchValue = Text.ToString();
	
	InitiateSearch();
}

void SFindInBT::OnSearchTextCommitted(const FText& Text, ETextCommit::Type CommitType)
{
	// @SStudio clickwang - BEGIN:  【编辑器】 完善行为树编辑器搜索功能
	if (SearchType != EFindInBTSearchType::GlobalNode)
	{
		OnSearchTextChanged(Text);
	}
	else if (CommitType == ETextCommit::OnEnter && SearchType == EFindInBTSearchType::GlobalNode)
	{
		FFindInAllBT::Get()->FindAllNodes(Text.ToString());
	}
	// @SStudio clikcwang - END
}

void SFindInBT::InitiateSearch()
{
	TArray<FString> Tokens;
	SearchValue.ParseIntoArray(Tokens, TEXT(" "), true);

	for (auto It(ItemsFound.CreateIterator()); It; ++It)
	{
		(*It).Get()->SetNodeHighlight(false); // need to reset highlight
		TreeView->SetItemExpansion(*It, false);
	}
	ItemsFound.Empty();
	if (Tokens.Num() > 0)
	{
		HighlightText = FText::FromString(SearchValue);
		if (SearchType == EFindInBTSearchType::LocalNode)
		{
			MatchTokens(Tokens);
		}
		else if (SearchType == EFindInBTSearchType::BBK)
		{
			MatchBBTokens(Tokens);
		}
	}

	// Insert a fake result to inform user if none found
	if (ItemsFound.Num() == 0)
	{
		ItemsFound.Add(FSearchResult(new FFindInBTResult(LOCTEXT("BehaviorTreeSearchNoResults", "No Results found").ToString())));
	}

	TreeView->RequestTreeRefresh();

	for (auto It(ItemsFound.CreateIterator()); It; ++It)
	{
		TreeView->SetItemExpansion(*It, true);
	}
}

void SFindInBT::MatchTokens(const TArray<FString>& Tokens)
{
	RootSearchResult.Reset();

	TWeakPtr<SGraphEditor> FocusedGraphEditor = BehaviorTreeEditorPtr.Pin()->GetFocusedGraphPtr();
	UEdGraph* Graph = NULL;
	if (FocusedGraphEditor.IsValid())
	{
		Graph = FocusedGraphEditor.Pin()->GetCurrentGraph();
	}

	if (Graph == NULL)
	{
		return;
	}

	RootSearchResult = FSearchResult(new FFindInBTResult(FString("BehaviorTreeRoot")));

	for (auto It(Graph->Nodes.CreateConstIterator()); It; ++It)
	{
		UEdGraphNode* Node = *It;
			
		const FString NodeName = Node->GetNodeTitle(ENodeTitleType::ListView).ToString();
		FSearchResult NodeResult(new FFindInBTResult(NodeName, RootSearchResult, Node));
		// @SStudio clickwang - BEGIN: 【编辑器】 完善行为树编辑器搜索功能
		UBehaviorTreeGraphNode* BTNode = Cast<UBehaviorTreeGraphNode>(Node);
		const FString NodeInstanceName = BTNode && BTNode->NodeInstance ? BTNode->NodeInstance->GetName() : TEXT("");

		FString NodeSearchString = NodeName + Node->GetClass()->GetName() + Node->NodeComment + NodeInstanceName;
		// @SStudio clikcwang - END
		NodeSearchString = NodeSearchString.Replace(TEXT(" "), TEXT(""));

		bool bNodeMatchesSearch = StringMatchesSearchTokens(Tokens, NodeSearchString);

		if (BTNode)
		{
			// searching through nodes' decorators
			for (auto DecoratorIt(BTNode->Decorators.CreateConstIterator()); DecoratorIt; ++DecoratorIt)
			{
				UBehaviorTreeGraphNode* Decorator = *DecoratorIt;
				MatchTokensInChild(Tokens, Decorator, NodeResult);
			}

			// searching through nodes' services
			for (auto ServiceIt(BTNode->Services.CreateConstIterator()); ServiceIt; ++ServiceIt)
			{
				UBehaviorTreeGraphNode* Service = *ServiceIt;
				MatchTokensInChild(Tokens, Service, NodeResult);
			}
		}

		if ((NodeResult->Children.Num() > 0) || bNodeMatchesSearch)
		{
			NodeResult->SetNodeHighlight(true);
			ItemsFound.Add(NodeResult);
		}
	}
}

void SFindInBT::MatchTokensInChild(const TArray<FString>& Tokens, UBehaviorTreeGraphNode* Child, FSearchResult ParentNode)
{
	if (Child == NULL)
	{
		return;
	}

	FString ChildName = Child->GetNodeTitle(ENodeTitleType::ListView).ToString();
	FString ChildSearchString = ChildName + Child->GetClass()->GetName() + Child->NodeComment + GetNameSafe(Child->NodeInstance ? Child->NodeInstance->GetClass() : nullptr);
	ChildSearchString = ChildSearchString.Replace(TEXT(" "), TEXT(""));
	if (StringMatchesSearchTokens(Tokens, ChildSearchString))
	{
		FSearchResult DecoratorResult(new FFindInBTResult(ChildName, ParentNode, Child));
		ParentNode->Children.Add(DecoratorResult);
	}
}

// @SStudio clickwang - BEGIN: by 230409 【AI行为树编辑器】单颗行为树查找引用特定黑板变量节点功能
void SFindInBT::MatchBBTokens(const TArray<FString>& Tokens)
{
	RootSearchResult.Reset();

	TWeakPtr<SGraphEditor> FocusedGraphEditor = BehaviorTreeEditorPtr.Pin()->GetFocusedGraphPtr();
	UEdGraph* Graph = NULL;
	if (FocusedGraphEditor.IsValid())
	{
		Graph = FocusedGraphEditor.Pin()->GetCurrentGraph();
	}

	if (Graph == NULL)
	{
		return;
	}

	RootSearchResult = FSearchResult(new FFindInBTResult(FString("BehaviorTreeRoot")));

	for (auto It(Graph->Nodes.CreateConstIterator()); It; ++It)
	{
		UEdGraphNode* Node = *It;
		UBehaviorTreeGraphNode* BTNode = Cast<UBehaviorTreeGraphNode>(Node);
		if (BTNode == nullptr) continue;
		UObject* NodeInstance = BTNode->NodeInstance;
		if (NodeInstance == nullptr) continue;

		bool bMatchesSearch = false;
		const FString NodeName = Node->GetNodeTitle(ENodeTitleType::ListView).ToString();
		FSearchResult NodeResult(new FFindInBTResult(NodeName, RootSearchResult, Node));

		bMatchesSearch = MatchBBTokensInProperty(Tokens, NodeInstance);

		// searching through nodes' services
		for (auto ServiceIt(BTNode->Services.CreateConstIterator()); ServiceIt; ++ServiceIt)
		{
			UBehaviorTreeGraphNode* Service = *ServiceIt;
			if (Service == nullptr) continue;
			if (MatchBBTokensInProperty(Tokens, Service->NodeInstance))
			{
				FString ChildName = Service->GetNodeTitle(ENodeTitleType::ListView).ToString();
				FSearchResult ChildResult(new FFindInBTResult(ChildName, NodeResult, Service));
				NodeResult->Children.Add(ChildResult);
			}
		}
		// searching through nodes' decorators
		for (auto DecoratorIt(BTNode->Decorators.CreateConstIterator()); DecoratorIt; ++DecoratorIt)
		{
			UBehaviorTreeGraphNode* Decorator = *DecoratorIt;
			if (Decorator == nullptr) continue;
			if (MatchBBTokensInProperty(Tokens, Decorator->NodeInstance))
			{
				FString ChildName = Decorator->GetNodeTitle(ENodeTitleType::ListView).ToString();
				FSearchResult ChildResult(new FFindInBTResult(ChildName, NodeResult, Decorator));
				NodeResult->Children.Add(ChildResult);
			}
		}

		if ((NodeResult->Children.Num() > 0) || bMatchesSearch)
		{
			NodeResult->SetNodeHighlight(true);
			ItemsFound.Add(NodeResult);
		}
	}
}

bool SFindInBT::MatchBBTokensInProperty(const TArray<FString>& Tokens, UObject* Obj)
{
	if (Obj == nullptr)
	{
		return false;
	}

	bool bMatchesSearch = false;
	for (FProperty* TestProperty = Obj->GetClass()->PropertyLink; TestProperty; TestProperty = TestProperty->PropertyLinkNext)
	{
		FStructProperty* StructProp = CastField<FStructProperty>(TestProperty);
		if (StructProp && StructProp->GetCPPType(NULL, CPPF_None) == "FBlackboardKeySelector")
		{
			FBlackboardKeySelector* PropertyValue = StructProp->ContainerPtrToValuePtr<FBlackboardKeySelector>(Obj);
			if (PropertyValue == nullptr) continue;
			// @SStudio clickwang - BEGIN: 【编辑器】 完善行为树编辑器搜索功能
			bMatchesSearch = StringMatchesSearchTokens(Tokens, PropertyValue->SelectedKeyName.ToString());
			// @SStudio clikcwang - END
			if (bMatchesSearch)
			{
				bMatchesSearch = true;
				break;
			}
		}
	}

	return bMatchesSearch;
}
// @SStudio clikcwang - END

TSharedRef<ITableRow> SFindInBT::OnGenerateRow( FSearchResult InItem, const TSharedRef<STableViewBase>& OwnerTable )
{
	return SNew(STableRow< TSharedPtr<FFindInBTResult> >, OwnerTable)
		[
			SNew(SHorizontalBox)
			+SHorizontalBox::Slot()
			.VAlign(VAlign_Center)
			.AutoWidth()
			[
				SNew(SBox)
				.WidthOverride(450.0f)
				[
					SNew(SHorizontalBox)
					+SHorizontalBox::Slot()
					.AutoWidth()
					[
						InItem->CreateIcon()
					]
					+SHorizontalBox::Slot()
					.VAlign(VAlign_Center)
					.AutoWidth()
					.Padding(2, 0)
					[
						SNew(STextBlock)
						.Text(FText::FromString(InItem->Value))
						.HighlightText(HighlightText)
					]
				]
			]
			+SHorizontalBox::Slot()
			.AutoWidth()
			.VAlign(VAlign_Center)
			[
				SNew(STextBlock)
				.Text(FText::FromString(InItem->GetNodeTypeText()))
				.HighlightText(HighlightText)
			]
			+SHorizontalBox::Slot()
			.HAlign(HAlign_Right)
			.VAlign(VAlign_Center)
			[
				SNew(STextBlock)
				.Text(FText::FromString(InItem->GetCommentText()))
				.ColorAndOpacity(FLinearColor::Yellow)
				.HighlightText(HighlightText)
			]
		];
}

void SFindInBT::OnGetChildren(FSearchResult InItem, TArray< FSearchResult >& OutChildren)
{
	OutChildren += InItem->Children;
}

void SFindInBT::OnTreeSelectionChanged(FSearchResult Item , ESelectInfo::Type)
{
	if (Item.IsValid())
	{
		Item->OnClick(BehaviorTreeEditorPtr, RootSearchResult);
	}
}

bool SFindInBT::StringMatchesSearchTokens(const TArray<FString>& Tokens, const FString& ComparisonString)
{
	bool bFoundAllTokens = true;

	//search the entry for each token, it must have all of them to pass
	for (auto TokIT(Tokens.CreateConstIterator()); TokIT; ++TokIT)
	{
		const FString& Token = *TokIT;
		if (!ComparisonString.Contains(Token))
		{
			bFoundAllTokens = false;
			break;
		}
	}
	return bFoundAllTokens;
}

// @SStudio clickwang - BEGIN: by 230409 【AI行为树编辑器】单颗行为树查找引用特定黑板变量节点功能
bool SFindInBT::StringEqualSearchTokens(const TArray<FString>& Tokens, const FString& ComparisonString)
{
	bool result = false;

	for (auto TokIT(Tokens.CreateConstIterator()); TokIT; ++TokIT)
	{
		const FString& Token = *TokIT;
		if (ComparisonString.Equals(Token, ESearchCase::IgnoreCase))
		{
			result = true;
			break;
		}
	}
	return result;
}
// @SStudio clikcwang - END

// @SStudio clickwang - BEGIN: 【编辑器】 完善行为树编辑器搜索功能
TSharedRef<SWidget> SFindInBT::OnGetSearchTypeContent()
{
	FMenuBuilder MenuBuilder(true, NULL);

	MenuBuilder.AddMenuEntry(
		LOCTEXT("LocalNode", "Find Local Nodes With Name"),
		LOCTEXT("LocalNode_ToolTip", "Find Local Nodes With Name"),
		FSlateIcon(),
		FUIAction(FExecuteAction::CreateSP(this, &SFindInBT::OnSearchTypeChange, EFindInBTSearchType::LocalNode))
	);

	MenuBuilder.AddMenuEntry(
		LOCTEXT("BBK", "Find Blackboard Key With Name"),
		LOCTEXT("BBK_ToolTip", "Find Blackboard Key With Name"),
		FSlateIcon(),
		FUIAction(FExecuteAction::CreateSP(this, &SFindInBT::OnSearchTypeChange, EFindInBTSearchType::BBK))
	);

	MenuBuilder.AddMenuEntry(
		LOCTEXT("Global Nodes", "Find Global Nodes With Name"),
		LOCTEXT("GlobalNodes_ToolTip", "Find Global Nodes With Name"),
		FSlateIcon(),
		FUIAction(FExecuteAction::CreateSP(this, &SFindInBT::OnSearchTypeChange, EFindInBTSearchType::GlobalNode))
	);

	return MenuBuilder.MakeWidget();
}


void SFindInBT::OnSearchTypeChange(EFindInBTSearchType InSerchType)
{
	if (InSerchType == SearchType)
	{
		return;
	}

	SearchType = InSerchType;
	if (SearchTextField.IsValid())
	{
		SearchTextField->SetText(FText::GetEmpty());
		if (SearchType == EFindInBTSearchType::LocalNode)
		{
			SearchTextField->SetHintText(LOCTEXT("BehaviorTreeSearchHint", "Enter text to find nodes..."));
		}
		else if (SearchType == EFindInBTSearchType::BBK)
		{
			SearchTextField->SetHintText(LOCTEXT("BehaviorTreeBBKSearchHint", "Enter text to find blackboard key..."));
		}
		else if (SearchType == EFindInBTSearchType::GlobalNode)
		{
			SearchTextField->SetHintText(LOCTEXT("BehaviorTreeGlobalNodeSearchHint", "Enter text and commit to find nodes in all blackboard trees..."));
		}
	}
}


FText SFindInBT::GetSearchTypeName() const
{
	if (SearchType == EFindInBTSearchType::BBK)
	{
		return LOCTEXT("BehaviorTreeSearchBBK", "Search Local BBK");
	}
	else if (SearchType == EFindInBTSearchType::GlobalNode)
	{
		return LOCTEXT("BehaviorTreeSearchGlobalNodes", "Search Global Nodes");
	}

	return LOCTEXT("BehaviorTreeSearchLocalNode", "Search Local Nodes");
}
// @SStudio clikcwang - END

/////////////////////////////////////////////////////

#undef LOCTEXT_NAMESPACE
